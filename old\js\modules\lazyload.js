/**

 * 图片懒加载模块

 * 提供高性能的图片懒加载功能

 */



export class LazyLoader {

    constructor(options = {}) {

        this.options = {

            // 只有当图片距离视口100px时才开始加载

            rootMargin: '100px 0px',

            // 图片至少10%进入视口才触发加载

            threshold: 0.1,

            loadingClass: 'lazy-loading',

            loadedClass: 'lazy-loaded',

            errorClass: 'lazy-error',

            // 更好的占位符

            placeholderSrc: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8ZGVmcz4KICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZ3JhZGllbnQiIHgxPSIwJSIgeTE9IjAlIiB4Mj0iMTAwJSIgeTI9IjEwMCUiPgogICAgICA8c3RvcCBvZmZzZXQ9IjAlIiBzdHlsZT0ic3RvcC1jb2xvcjojZjNmNGY2O3N0b3Atb3BhY2l0eToxIiAvPgogICAgICA8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0eWxlPSJzdG9wLWNvbG9yOiNlNWU3ZWI7c3RvcC1vcGFjaXR5OjEiIC8+CiAgICA8L2xpbmVhckdyYWRpZW50PgogIDwvZGVmcz4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSJ1cmwoI2dyYWRpZW50KSIvPgogIDxjaXJjbGUgY3g9IjUwJSIgY3k9IjUwJSIgcj0iMjAiIGZpbGw9IiM5Y2EzYWYiIG9wYWNpdHk9IjAuNSI+CiAgICA8YW5pbWF0ZSBhdHRyaWJ1dGVOYW1lPSJvcGFjaXR5IiB2YWx1ZXM9IjAuNTsxOzAuNSIgZHVyPSIxLjVzIiByZXBlYXRDb3VudD0iaW5kZWZpbml0ZSIvPgogIDwvY2lyY2xlPgogIDx0ZXh0IHg9IjUwJSIgeT0iNjAlIiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMTQiIGZpbGw9IiM2YjcyODAiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj7lm77niYfmraPlnKjliqDovb0uLi48L3RleHQ+Cjwvc3ZnPg==',

            ...options

        };

        

        this.observer = null;

        this.images = new Set();

        this.init();

    }



    /**

     * 初始化 Intersection Observer

     */

    init() {

        if (!('IntersectionObserver' in window)) {

            // 降级处理：直接加载所有图片

            this.loadAllImages();

            return;

        }



        this.observer = new IntersectionObserver((entries) => {

            entries.forEach(entry => {

                const img = entry.target;

                

                if (entry.isIntersecting) {

                    // 添加调试日志

                    console.log('图片进入视口，开始加载:', img.dataset.src);

                    

                    // 添加即将加载的视觉提示

                    img.classList.add('lazy-entering');

                    

                    // 延迟一小段时间，让用户看到加载效果

                    setTimeout(() => {

                        this.loadImage(img);

                    }, 100);

                    

                    // 停止观察这个图片

                    this.observer.unobserve(img);

                } else {

                    // 图片离开视口时移除进入状态

                    img.classList.remove('lazy-entering');

                }

            });

        }, {

            rootMargin: this.options.rootMargin,

            threshold: this.options.threshold

        });

    }



    /**

     * 观察图片元素

     * @param {HTMLImageElement} img - 图片元素

     */

    observe(img) {

        if (!img || img.tagName !== 'IMG') return;

        

        this.images.add(img);

        

        if (this.observer) {

            this.observer.observe(img);

        } else {

            // 降级处理

            this.loadImage(img);

        }

    }



    /**

     * 批量观察图片元素

     * @param {NodeList|Array} images - 图片元素列表

     */

    observeAll(images) {

        images.forEach(img => this.observe(img));

    }



    /**

     * 加载单个图片

     * @param {HTMLImageElement} img - 图片元素

     */

    loadImage(img) {

        if (!img.dataset.src) return;



        // 添加加载状态

        img.classList.add(this.options.loadingClass);



        // 创建新的图片对象来预加载

        const imageLoader = new Image();

        

        imageLoader.onload = () => {

            // 加载成功

            img.src = img.dataset.src;

            img.classList.remove(this.options.loadingClass);

            img.classList.add(this.options.loadedClass);

            

            // 移除 data-src 属性

            delete img.dataset.src;

            

            // 触发自定义事件

            img.dispatchEvent(new CustomEvent('lazyloaded', {

                detail: { src: img.src }

            }));

        };

        

        imageLoader.onerror = () => {

            // 加载失败

            img.classList.remove(this.options.loadingClass);

            img.classList.add(this.options.errorClass);

            

            // 设置错误占位图

            img.src = this.getErrorPlaceholder();

            img.alt = '图片加载失败';

            

            // 触发自定义事件

            img.dispatchEvent(new CustomEvent('lazyerror', {

                detail: { originalSrc: img.dataset.src }

            }));

        };

        

        // 开始加载

        imageLoader.src = img.dataset.src;

    }



    /**

     * 加载所有图片（降级处理）

     */

    loadAllImages() {

        this.images.forEach(img => this.loadImage(img));

    }



    /**

     * 获取加载占位图

     */

    getLoadingPlaceholder() {

        return this.options.placeholderSrc;

    }



    /**

     * 获取错误占位图

     */

    getErrorPlaceholder() {

        return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjE4MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZmVlMmUyIi8+CiAgPHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNCIgZmlsbD0iI2VmNDQ0NCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkxvYWQgRXJyb3I8L3RleHQ+Cjwvc3ZnPg==';

    }



    /**

     * 检查图片是否在视口中

     * @param {HTMLImageElement} img - 图片元素

     * @returns {boolean} 是否在视口中

     */

    isInViewport(img) {

        const rect = img.getBoundingClientRect();

        const windowHeight = window.innerHeight || document.documentElement.clientHeight;

        const windowWidth = window.innerWidth || document.documentElement.clientWidth;

        

        return (

            rect.top >= -100 && // 允许100px的预加载距离

            rect.left >= 0 &&

            rect.bottom <= windowHeight + 100 &&

            rect.right <= windowWidth

        );

    }



    /**

     * 手动检查并加载视口中的图片

     */

    checkViewport() {

        this.images.forEach(img => {

            if (this.isInViewport(img) && img.dataset.src) {

                console.log('手动检查：图片在视口中，开始加载:', img.dataset.src);

                this.loadImage(img);

                if (this.observer) {

                    this.observer.unobserve(img);

                }

            }

        });

    }



    /**

     * 停止观察所有图片

     */

    disconnect() {

        if (this.observer) {

            this.observer.disconnect();

        }

        this.images.clear();

    }



    /**

     * 重新观察所有图片

     */

    refresh() {

        if (this.observer) {

            this.disconnect();

            this.init();

            

            // 重新观察页面中的懒加载图片

            const lazyImages = document.querySelectorAll('img[data-src]');

            this.observeAll(lazyImages);

        }

    }

}



/**

 * 创建懒加载图片的HTML

 * @param {string} src - 图片真实地址

 * @param {string} alt - 图片描述

 * @param {string} className - CSS类名

 * @param {Object} options - 其他选项

 * @returns {string} HTML字符串

 */

export function createLazyImage(src, alt = '', className = '', options = {}) {

    const {

        width = '',

        height = '',

        style = '',

        placeholder = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjE4MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+CiAgPHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzlDQTNBRiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkxvYWRpbmcuLi48L3RleHQ+Cjwvc3ZnPg=='

    } = options;



    return `<img 

        src="${placeholder}" 

        data-src="${src}" 

        alt="${alt}" 

        class="lazy-image ${className}"

        ${width ? `width="${width}"` : ''}

        ${height ? `height="${height}"` : ''}

        ${style ? `style="${style}"` : ''}

        loading="lazy"

    >`;

}



// 创建全局懒加载实例

export const globalLazyLoader = new LazyLoader();



// 自动初始化页面中的懒加载图片

document.addEventListener('DOMContentLoaded', () => {

    const lazyImages = document.querySelectorAll('img[data-src]');

    globalLazyLoader.observeAll(lazyImages);

});