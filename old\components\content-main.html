<!-- 内容主体组件 -->

<div class="max-w-2xl mx-auto p-4">

    <div id="contentCard" class="content-card bg-white dark:bg-gray-800 eye-protect:bg-eye-protect-card rounded-xl p-6 shadow-md">

        <!-- 加载状态 -->

        <div id="contentLoading" class="flex items-center justify-center py-12">

            <div class="animate-spin h-8 w-8 border-4 border-blue-500 rounded-full border-t-transparent"></div>

            <span class="ml-3 text-gray-600 dark:text-gray-300 eye-protect:text-eye-protect-text">加载中...</span>

        </div>

        

        <!-- 错误状态 -->

        <div id="contentError" class="hidden text-center py-12">

            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">

                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />

            </svg>

            <h3 class="mt-2 text-lg font-medium text-gray-900 dark:text-white eye-protect:text-eye-protect-text">无法访问此内容</h3>

            <p id="errorMessage" class="mt-1 text-sm text-gray-500 dark:text-gray-400 eye-protect:text-eye-protect-text"></p>

            <button id="retryButton" class="mt-4 btn btn-primary">

                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">

                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>

                </svg>

                重试

            </button>

        </div>

        

        <!-- 主要内容 -->

        <div id="contentMain" class="hidden">

            <!-- 内容元信息 -->

            <div class="memo-header mb-6">

                <div class="memo-author">

                    <img id="contentAvatar" src="" alt="" class="memo-avatar">

                    <div class="memo-meta">

                        <div id="contentAuthor" class="memo-username">匿名用户</div>

                        <div id="contentDate" class="memo-time"></div>

                    </div>

                </div>

                <div class="flex items-center space-x-2">

                    <span id="contentStatus" class="memo-status"></span>

                    <span id="contentId" class="text-xs text-gray-400 font-mono"></span>

                </div>

            </div>

            

            <!-- 内容正文 -->

            <div id="contentText" class="memo-content prose dark:prose-invert eye-protect:text-eye-protect-text max-w-none mb-6"></div>

            

            <!-- 图片容器 -->

            <div id="contentImageContainer" class="memo-media mb-6 hidden">

                <div class="space-y-4" id="contentImages">

                    <!-- 图片将在这里动态添加 -->

                </div>

            </div>

            

            <!-- 标签 -->

            <div id="contentTags" class="memo-footer mb-6 hidden">

                <div class="memo-tags" id="tagsList">

                    <!-- 标签将在这里动态添加 -->

                </div>

            </div>

            

            <!-- 操作按钮 -->

            <div class="memo-footer pt-4 border-t border-gray-100 dark:border-gray-700">

                <div class="flex flex-wrap gap-3">

                    <button id="shareButton" class="btn btn-ghost btn-sm">

                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">

                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>

                        </svg>

                        分享

                    </button>

                    <button id="copyLinkButton" class="btn btn-ghost btn-sm">

                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">

                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>

                        </svg>

                        复制链接

                    </button>

                    <button id="generateCardBtn" class="btn btn-primary btn-sm">

                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">

                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>

                        </svg>

                        生成卡片

                    </button>

                </div>

            </div>

        </div>

    </div>

</div>