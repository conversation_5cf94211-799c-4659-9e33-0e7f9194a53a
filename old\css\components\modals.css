/**

 * 模态框组件样式

 */



.modal-overlay {

  position: fixed;

  top: 0;

  left: 0;

  right: 0;

  bottom: 0;

  background-color: rgba(0, 0, 0, 0.5);

  backdrop-filter: blur(4px);

  z-index: var(--z-modal-backdrop);

  display: flex;

  align-items: center;

  justify-content: center;

  padding: var(--space-4);

  opacity: 0;

  visibility: hidden;

  transition: all var(--duration-normal) var(--ease-out);

}



.modal-overlay.show {

  opacity: 1;

  visibility: visible;

}



.modal {

  background-color: var(--color-bg, white);

  border-radius: var(--radius-xl);

  box-shadow: var(--shadow-2xl);

  border: 1px solid var(--color-border, var(--color-gray-200));

  max-width: 90vw;

  max-height: 90vh;

  overflow: hidden;

  transform: scale(0.95) translateY(1rem);

  transition: all var(--duration-normal) var(--ease-out);

  z-index: var(--z-modal);

}



.modal-overlay.show .modal {

  transform: scale(1) translateY(0);

}



.modal-header {

  display: flex;

  align-items: center;

  justify-content: space-between;

  padding: var(--space-6);

  border-bottom: 1px solid var(--color-border-light, var(--color-gray-100));

}



.modal-title {

  font-size: var(--text-xl);

  font-weight: 600;

  color: var(--color-text, var(--color-gray-900));

  margin: 0;

}



.modal-close {

  display: flex;

  align-items: center;

  justify-content: center;

  width: 2rem;

  height: 2rem;

  border-radius: var(--radius-full);

  background: none;

  border: none;

  color: var(--color-text-secondary, var(--color-gray-500));

  cursor: pointer;

  transition: all var(--duration-fast) var(--ease-out);

}



.modal-close:hover {

  background-color: var(--color-bg-secondary, var(--color-gray-100));

  color: var(--color-text, var(--color-gray-900));

}



.modal-close svg {

  width: 1.25rem;

  height: 1.25rem;

}



.modal-body {

  padding: var(--space-6);

  overflow-y: auto;

  max-height: calc(90vh - 8rem);

}



.modal-footer {

  display: flex;

  align-items: center;

  justify-content: flex-end;

  gap: var(--space-3);

  padding: var(--space-6);

  border-top: 1px solid var(--color-border-light, var(--color-gray-100));

  background-color: var(--color-bg-secondary, var(--color-gray-50));

}



/* 模态框尺寸 */

.modal-sm {

  max-width: 24rem;

}



.modal-md {

  max-width: 32rem;

}



.modal-lg {

  max-width: 48rem;

}



.modal-xl {

  max-width: 64rem;

}



.modal-full {

  max-width: 95vw;

  max-height: 95vh;

}



/* 登录模态框特殊样式 */

.login-modal .modal {

  max-width: 24rem;

}



.login-modal .modal-body {

  padding: var(--space-6) var(--space-6) var(--space-4);

}



.login-modal .form-group:last-child {

  margin-bottom: 0;

}



/* 确认对话框 */

.confirm-modal .modal-body {

  text-align: center;

  padding: var(--space-8) var(--space-6);

}



.confirm-modal .modal-icon {

  width: 3rem;

  height: 3rem;

  margin: 0 auto var(--space-4);

  border-radius: var(--radius-full);

  display: flex;

  align-items: center;

  justify-content: center;

}



.confirm-modal .modal-icon.warning {

  background-color: rgba(245, 158, 11, 0.1);

  color: var(--color-warning);

}



.confirm-modal .modal-icon.danger {

  background-color: rgba(239, 68, 68, 0.1);

  color: var(--color-error);

}



.confirm-modal .modal-icon.success {

  background-color: rgba(16, 185, 129, 0.1);

  color: var(--color-success);

}



.confirm-modal .modal-icon.info {

  background-color: rgba(59, 130, 246, 0.1);

  color: var(--color-info);

}



.confirm-modal .modal-title {

  font-size: var(--text-lg);

  margin-bottom: var(--space-2);

}



.confirm-modal .modal-message {

  color: var(--color-text-secondary, var(--color-gray-600));

  margin-bottom: var(--space-6);

}



/* 图片预览模态框 */

.image-modal .modal-overlay {

  background-color: rgba(0, 0, 0, 0.9);

}



.image-modal .modal {

  background: none;

  border: none;

  box-shadow: none;

  max-width: 95vw;

  max-height: 95vh;

}



.image-modal .modal img {

  max-width: 100%;

  max-height: 100%;

  object-fit: contain;

  border-radius: var(--radius-lg);

}



/* 抽屉式模态框 */

.drawer-modal .modal-overlay {

  align-items: flex-end;

  justify-content: center;

}



.drawer-modal .modal {

  width: 100%;

  max-width: none;

  border-radius: var(--radius-xl) var(--radius-xl) 0 0;

  transform: translateY(100%);

}



.drawer-modal.show .modal {

  transform: translateY(0);

}



/* 侧边栏模态框 */

.sidebar-modal .modal-overlay {

  align-items: stretch;

  justify-content: flex-start;

}



.sidebar-modal .modal {

  width: 20rem;

  max-width: 80vw;

  height: 100vh;

  max-height: none;

  border-radius: 0;

  transform: translateX(-100%);

}



.sidebar-modal.show .modal {

  transform: translateX(0);

}



.sidebar-modal.right .modal-overlay {

  justify-content: flex-end;

}



.sidebar-modal.right .modal {

  transform: translateX(100%);

}



/* 动画效果 */

@keyframes modalFadeIn {

  from {

    opacity: 0;

    transform: scale(0.95) translateY(1rem);

  }

  to {

    opacity: 1;

    transform: scale(1) translateY(0);

  }

}



@keyframes modalFadeOut {

  from {

    opacity: 1;

    transform: scale(1) translateY(0);

  }

  to {

    opacity: 0;

    transform: scale(0.95) translateY(1rem);

  }

}



.modal.animate-in {

  animation: modalFadeIn var(--duration-normal) var(--ease-out);

}



.modal.animate-out {

  animation: modalFadeOut var(--duration-normal) var(--ease-out);

}



/* 响应式设计 */

@media (max-width: 640px) {

  .modal-overlay {

    padding: var(--space-2);

    align-items: flex-end;

  }

  

  .modal {

    max-width: 100%;

    width: 100%;

    border-radius: var(--radius-xl) var(--radius-xl) 0 0;

    transform: translateY(100%);

  }

  

  .modal-overlay.show .modal {

    transform: translateY(0);

  }

  

  .modal-header,

  .modal-body,

  .modal-footer {

    padding: var(--space-4);

  }

  

  .modal-footer {

    flex-direction: column-reverse;

    gap: var(--space-2);

  }

  

  .modal-footer .btn {

    width: 100%;

  }

  

  .drawer-modal .modal,

  .sidebar-modal .modal {

    border-radius: 0;

  }

  

  .sidebar-modal .modal {

    width: 100%;

    max-width: 100%;

  }

}