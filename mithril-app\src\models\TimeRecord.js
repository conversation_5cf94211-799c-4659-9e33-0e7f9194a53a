/**
 * 时间记录数据模型
 * 负责管理时间记录的数据操作和状态
 */

import { TimeRecordService } from '../services/TimeRecordService.js';
import { formatDuration } from '../utils/format.js';

/**
 * 时间记录模型类
 * 提供时间记录的CRUD操作和状态管理
 */
export class TimeRecordModel {
    constructor() {
        this.timeRecordService = new TimeRecordService();
        this.records = [];
        this.loading = false;
        this.hasMore = false;
        this.error = null;
        this.currentPage = 1;
    }

    /**
     * 加载时间记录
     * @param {number} page - 页码
     * @param {boolean} reset - 是否重置数据
     * @returns {Promise<void>}
     */
    async loadRecords(page = 1, reset = false) {
        if (this.loading) return;

        this.loading = true;
        this.error = null;
        
        if (reset) {
            this.currentPage = 1;
            this.records = [];
        }

        try {
            const result = await this.timeRecordService.getRecords(page);
            
            if (result.success) {
                if (reset) {
                    this.records = result.records;
                } else {
                    this.records.push(...result.records);
                }
                this.hasMore = result.hasMore;
                this.currentPage = page;
            } else {
                this.error = result.error;
                console.error('加载时间记录失败:', result.error);
                
                // 根据错误类型显示不同的用户提示
                let errorMessage = '获取时间记录失败';
                if (result.error.includes('500')) {
                    errorMessage = '服务器内部错误，请稍后重试';
                } else if (result.error.includes('404')) {
                    errorMessage = '数据配置错误，请联系管理员';
                } else if (result.error.includes('network')) {
                    errorMessage = '网络连接错误，请检查网络';
                }
                
                if (window.appState) {
                    window.appState.showNotification(errorMessage, 'error');
                }
            }
        } catch (error) {
            this.error = error.message;
            console.error('加载时间记录异常:', error);
            
            if (window.appState) {
                window.appState.showNotification('网络连接错误，请检查网络后重试', 'error');
            }
        } finally {
            this.loading = false;
            m.redraw();
        }
    }

    /**
     * 创建时间记录
     * @param {Object} recordData - 记录数据
     * @param {string} recordData.content - 工作内容
     * @param {number} recordData.startTime - 开始时间戳
     * @param {number} recordData.endTime - 结束时间戳
     * @param {number} recordData.duration - 持续时间(秒)
     * @returns {Promise<Object>} 创建结果
     */
    async createRecord(recordData) {
        try {
            const result = await this.timeRecordService.createRecord(recordData);
            
            if (result.success && result.data) {
                // 添加到本地记录列表
                this.records.unshift({
                    id: result.data.recordId,
                    ...recordData,
                    durationText: formatDuration(recordData.duration)
                });
                m.redraw();
                
                if (window.appState) {
                    window.appState.showNotification('时间记录已保存', 'success');
                }
            }
            
            return result;
        } catch (error) {
            console.error('创建时间记录失败:', error);
            
            if (window.appState) {
                window.appState.showNotification('保存失败: ' + error.message, 'error');
            }
            
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 更新时间记录
     * @param {string} id - 记录ID
     * @param {Object} updates - 更新数据
     * @returns {Promise<Object>} 更新结果
     */
    async updateRecord(id, updates) {
        try {
            const result = await this.timeRecordService.updateRecord(id, updates);
            
            if (result.success) {
                // 更新本地记录
                const index = this.records.findIndex(r => r.id === id);
                if (index !== -1) {
                    this.records[index] = { ...this.records[index], ...updates };
                    m.redraw();
                }
                
                if (window.appState) {
                    window.appState.showNotification('记录已更新', 'success');
                }
            }
            
            return result;
        } catch (error) {
            console.error('更新时间记录失败:', error);
            
            if (window.appState) {
                window.appState.showNotification('更新失败: ' + error.message, 'error');
            }
            
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 删除时间记录
     * @param {string} id - 记录ID
     * @returns {Promise<Object>} 删除结果
     */
    async deleteRecord(id) {
        try {
            const result = await this.timeRecordService.deleteRecord(id);
            
            if (result.success) {
                // 从本地记录中移除
                this.records = this.records.filter(r => r.id !== id);
                m.redraw();
                
                if (window.appState) {
                    window.appState.showNotification('记录已删除', 'success');
                }
            }
            
            return result;
        } catch (error) {
            console.error('删除时间记录失败:', error);
            
            if (window.appState) {
                window.appState.showNotification('删除失败: ' + error.message, 'error');
            }
            
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 获取统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        const totalDuration = this.records.reduce((sum, record) => sum + (record.duration || 0), 0);
        const todayRecords = this.records.filter(record => {
            const recordDate = new Date(record.startTime);
            const today = new Date();
            return recordDate.toDateString() === today.toDateString();
        });
        const todayDuration = todayRecords.reduce((sum, record) => sum + (record.duration || 0), 0);

        return {
            totalRecords: this.records.length,
            totalDuration,
            todayRecords: todayRecords.length,
            todayDuration
        };
    }

    /**
     * 按日期分组记录
     * @returns {Object} 分组后的记录
     */
    getRecordsByDate() {
        const groups = {};
        
        this.records.forEach(record => {
            const date = new Date(record.startTime).toLocaleDateString('zh-CN');
            if (!groups[date]) {
                groups[date] = [];
            }
            groups[date].push(record);
        });

        return groups;
    }
}