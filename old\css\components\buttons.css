/**

 * 按钮组件样式

 */



.btn {

  display: inline-flex;

  align-items: center;

  justify-content: center;

  padding: var(--space-2) var(--space-4);

  font-size: var(--text-sm);

  font-weight: 500;

  line-height: 1.5;

  border-radius: var(--radius-lg);

  border: 1px solid transparent;

  cursor: pointer;

  transition: all var(--duration-fast) var(--ease-out);

  text-decoration: none;

  white-space: nowrap;

  user-select: none;

}



.btn:focus-visible {

  outline: 2px solid var(--color-primary);

  outline-offset: 2px;

}



.btn:disabled {

  opacity: 0.5;

  cursor: not-allowed;

  pointer-events: none;

}



/* 按钮尺寸 */

.btn-sm {

  padding: var(--space-1) var(--space-3);

  font-size: var(--text-xs);

}



.btn-lg {

  padding: var(--space-3) var(--space-6);

  font-size: var(--text-base);

}



.btn-xl {

  padding: var(--space-4) var(--space-8);

  font-size: var(--text-lg);

}



/* 主要按钮 */

.btn-primary {

  background-color: var(--color-primary);

  color: white;

  box-shadow: var(--shadow-sm);

}



.btn-primary:hover:not(:disabled) {

  background-color: var(--color-primary-hover);

  box-shadow: var(--shadow-md);

  transform: translateY(-1px);

}



.btn-primary:active {

  transform: translateY(0);

  box-shadow: var(--shadow-sm);

}



/* 次要按钮 */

.btn-secondary {

  background-color: var(--color-secondary);

  color: white;

  box-shadow: var(--shadow-sm);

}



.btn-secondary:hover:not(:disabled) {

  background-color: var(--color-secondary-hover);

  box-shadow: var(--shadow-md);

  transform: translateY(-1px);

}



/* 成功按钮 */

.btn-success {

  background-color: var(--color-success);

  color: white;

  box-shadow: var(--shadow-sm);

}



.btn-success:hover:not(:disabled) {

  background-color: var(--color-success-hover);

  box-shadow: var(--shadow-md);

  transform: translateY(-1px);

}



/* 危险按钮 */

.btn-danger {

  background-color: var(--color-error);

  color: white;

  box-shadow: var(--shadow-sm);

}



.btn-danger:hover:not(:disabled) {

  background-color: var(--color-error-hover);

  box-shadow: var(--shadow-md);

  transform: translateY(-1px);

}



/* 轮廓按钮 */

.btn-outline {

  background-color: transparent;

  color: var(--color-text);

  border-color: var(--color-border);

}



.btn-outline:hover:not(:disabled) {

  background-color: var(--color-bg-secondary);

  border-color: var(--color-primary);

  color: var(--color-primary);

}



.btn-outline-primary {

  color: var(--color-primary);

  border-color: var(--color-primary);

}



.btn-outline-primary:hover:not(:disabled) {

  background-color: var(--color-primary);

  color: white;

}



/* 幽灵按钮 */

.btn-ghost {

  background-color: transparent;

  color: var(--color-text-secondary);

  border: none;

}



.btn-ghost:hover:not(:disabled) {

  background-color: var(--color-bg-secondary);

  color: var(--color-text);

}



/* 链接按钮 */

.btn-link {

  background-color: transparent;

  color: var(--color-primary);

  border: none;

  padding: 0;

  text-decoration: underline;

}



.btn-link:hover:not(:disabled) {

  color: var(--color-primary-hover);

}



/* 圆形按钮 */

.btn-circle {

  border-radius: var(--radius-full);

  padding: var(--space-2);

  width: 2.5rem;

  height: 2.5rem;

}



.btn-circle.btn-sm {

  width: 2rem;

  height: 2rem;

  padding: var(--space-1);

}



.btn-circle.btn-lg {

  width: 3rem;

  height: 3rem;

  padding: var(--space-3);

}



/* 图标按钮 */

.btn-icon {

  display: inline-flex;

  align-items: center;

  gap: var(--space-2);

}



.btn-icon svg {

  width: 1rem;

  height: 1rem;

}



.btn-icon.btn-sm svg {

  width: 0.875rem;

  height: 0.875rem;

}



.btn-icon.btn-lg svg {

  width: 1.25rem;

  height: 1.25rem;

}



/* 加载状态 */

.btn-loading {

  position: relative;

  color: transparent;

}



.btn-loading::after {

  content: '';

  position: absolute;

  width: 1rem;

  height: 1rem;

  border: 2px solid transparent;

  border-top: 2px solid currentColor;

  border-radius: var(--radius-full);

  animation: spin 1s linear infinite;

  top: 50%;

  left: 50%;

  transform: translate(-50%, -50%);

}



/* 按钮组 */

.btn-group {

  display: inline-flex;

  border-radius: var(--radius-lg);

  box-shadow: var(--shadow-sm);

}



.btn-group .btn {

  border-radius: 0;

  border-right-width: 0;

}



.btn-group .btn:first-child {

  border-top-left-radius: var(--radius-lg);

  border-bottom-left-radius: var(--radius-lg);

}



.btn-group .btn:last-child {

  border-top-right-radius: var(--radius-lg);

  border-bottom-right-radius: var(--radius-lg);

  border-right-width: 1px;

}



.btn-group .btn:not(:first-child):not(:last-child) {

  border-radius: 0;

}



/* 响应式按钮 */

@media (max-width: 640px) {

  .btn-responsive {

    width: 100%;

    justify-content: center;

  }

  

  .btn-group-responsive {

    flex-direction: column;

  }

  

  .btn-group-responsive .btn {

    border-radius: var(--radius-lg);

    border-right-width: 1px;

    border-bottom-width: 0;

  }

  

  .btn-group-responsive .btn:not(:last-child) {

    border-bottom-width: 0;

  }

  

  .btn-group-responsive .btn:last-child {

    border-bottom-width: 1px;

  }

}