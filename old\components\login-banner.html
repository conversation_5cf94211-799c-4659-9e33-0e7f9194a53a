<!-- 未登录提示横幅 -->

<div id="loginBanner" class="login-banner hidden">

    <div class="login-banner-content">

        <div class="login-banner-icon">

            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">

                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />

            </svg>

        </div>

        <div class="login-banner-text">

            <h3 class="login-banner-title">欢迎来到当记</h3>

            <p class="login-banner-description">登录后可以发布备忘录、查看私密内容，享受完整功能体验</p>

        </div>

        <div class="login-banner-actions">

            <button onclick="app.showLogin()" class="btn btn-primary btn-sm">

                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">

                    <path fill-rule="evenodd" d="M3 3a1 1 0 011 1v12a1 1 0 11-2 0V4a1 1 0 011-1zm7.707 3.293a1 1 0 010 1.414L9.414 9H17a1 1 0 110 2H9.414l1.293 1.293a1 1 0 01-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0z" clip-rule="evenodd" />

                </svg>

                立即登录

            </button>

            <button onclick="app.hideBanner()" class="btn btn-ghost btn-sm login-banner-close" title="关闭提示">

                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">

                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />

                </svg>

            </button>

        </div>

    </div>

</div>