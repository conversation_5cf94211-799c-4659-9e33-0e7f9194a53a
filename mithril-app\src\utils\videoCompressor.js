// 视频压缩工具类
export class VideoCompressor {
    constructor() {
        this.canvas = null;
        this.ctx = null;
        this.video = null;
    }

    // 压缩视频文件
    async compressVideo(file, options = {}) {
        const {
            maxWidth = 1080,       // 最大宽度 - 提高到1080p
            maxHeight = 1920,      // 最大高度
            quality = 0.85,        // 压缩质量 0-1 - 提高质量
            maxSizeMB = 25,        // 最大文件大小(MB) - 放宽限制
            format = 'mp4'         // 输出格式
        } = options;

        try {
            console.log(`开始压缩视频: ${file.name}, 原始大小: ${(file.size / 1024 / 1024).toFixed(2)}MB`);

            // 如果文件已经很小，直接返回 - 提高跳过压缩的阈值
            if (file.size <= maxSizeMB * 1024 * 1024 * 0.6) {
                console.log('文件大小已符合要求，跳过压缩');
                return file;
            }

            // 创建视频元素
            const video = document.createElement('video');
            video.src = URL.createObjectURL(file);
            video.muted = true;
            video.playsInline = true;

            // 等待视频加载
            await new Promise((resolve, reject) => {
                video.onloadedmetadata = resolve;
                video.onerror = reject;
            });

            // 计算压缩后的尺寸
            const { width: newWidth, height: newHeight } = this.calculateDimensions(
                video.videoWidth, 
                video.videoHeight, 
                maxWidth, 
                maxHeight
            );

            console.log(`原始尺寸: ${video.videoWidth}x${video.videoHeight}`);
            console.log(`压缩尺寸: ${newWidth}x${newHeight}`);

            // 创建canvas
            const canvas = document.createElement('canvas');
            canvas.width = newWidth;
            canvas.height = newHeight;
            const ctx = canvas.getContext('2d');

            // 使用MediaRecorder进行压缩
            const stream = canvas.captureStream(30); // 30fps
            const mediaRecorder = new MediaRecorder(stream, {
                mimeType: 'video/webm;codecs=vp9',
                videoBitsPerSecond: this.calculateBitrate(newWidth, newHeight, quality)
            });

            const chunks = [];
            mediaRecorder.ondataavailable = (event) => {
                if (event.data.size > 0) {
                    chunks.push(event.data);
                }
            };

            // 开始录制
            mediaRecorder.start();
            video.currentTime = 0;
            await video.play();

            // 绘制视频帧
            const drawFrame = () => {
                if (video.ended || video.paused) {
                    mediaRecorder.stop();
                    return;
                }
                
                ctx.drawImage(video, 0, 0, newWidth, newHeight);
                requestAnimationFrame(drawFrame);
            };

            drawFrame();

            // 等待录制完成
            const compressedBlob = await new Promise((resolve) => {
                mediaRecorder.onstop = () => {
                    const blob = new Blob(chunks, { type: 'video/webm' });
                    resolve(blob);
                };
            });

            // 清理资源
            URL.revokeObjectURL(video.src);
            stream.getTracks().forEach(track => track.stop());

            // 创建压缩后的文件
            const compressedFile = new File(
                [compressedBlob], 
                file.name.replace(/\.[^/.]+$/, '_compressed.webm'),
                { type: 'video/webm' }
            );

            console.log(`压缩完成: ${(compressedFile.size / 1024 / 1024).toFixed(2)}MB`);
            console.log(`压缩率: ${((1 - compressedFile.size / file.size) * 100).toFixed(1)}%`);

            return compressedFile;

        } catch (error) {
            console.error('视频压缩失败:', error);
            throw new Error(`视频压缩失败: ${error.message}`);
        }
    }

    // 计算合适的尺寸
    calculateDimensions(originalWidth, originalHeight, maxWidth, maxHeight) {
        let { width, height } = { width: originalWidth, height: originalHeight };

        // 按比例缩放
        if (width > maxWidth) {
            height = (height * maxWidth) / width;
            width = maxWidth;
        }

        if (height > maxHeight) {
            width = (width * maxHeight) / height;
            height = maxHeight;
        }

        // 确保尺寸是偶数（视频编码要求）
        width = Math.floor(width / 2) * 2;
        height = Math.floor(height / 2) * 2;

        return { width, height };
    }

    // 计算合适的码率
    calculateBitrate(width, height, quality) {
        // 改进的码率计算：根据分辨率和质量动态调整
        const pixels = width * height;
        
        // 根据分辨率设置基础码率系数
        let bitrateMultiplier;
        if (pixels <= 480 * 854) {        // 480p
            bitrateMultiplier = 0.25;      // 提高480p码率
        } else if (pixels <= 720 * 1280) { // 720p
            bitrateMultiplier = 0.35;      // 720p码率
        } else {                          // 1080p及以上
            bitrateMultiplier = 0.45;      // 1080p码率
        }
        
        const baseBitrate = pixels * bitrateMultiplier;
        const finalBitrate = Math.floor(baseBitrate * quality);
        
        // 设置最小码率，确保基本质量
        const minBitrate = pixels * 0.15;
        return Math.max(finalBitrate, minBitrate);
    }

    // 快速压缩（平衡质量和大小）
    async quickCompress(file) {
        return this.compressVideo(file, {
            maxWidth: 720,         // 提高到720p
            maxHeight: 1280,
            quality: 0.75,         // 提高质量
            maxSizeMB: 15          // 放宽大小限制
        });
    }

    // 高质量压缩（保持较高质量）
    async highQualityCompress(file) {
        return this.compressVideo(file, {
            maxWidth: 1080,
            maxHeight: 1920,
            quality: 0.9,          // 进一步提高质量
            maxSizeMB: 35          // 放宽大小限制
        });
    }

    // 智能压缩（根据原始文件大小和分辨率自动选择策略）
    async smartCompress(file) {
        try {
            // 创建视频元素获取原始信息
            const video = document.createElement('video');
            video.src = URL.createObjectURL(file);
            video.muted = true;
            
            await new Promise((resolve, reject) => {
                video.onloadedmetadata = resolve;
                video.onerror = reject;
            });

            const originalWidth = video.videoWidth;
            const originalHeight = video.videoHeight;
            const fileSizeMB = file.size / (1024 * 1024);

            // 清理资源
            URL.revokeObjectURL(video.src);

            console.log(`智能压缩分析: ${originalWidth}x${originalHeight}, ${fileSizeMB.toFixed(2)}MB`);

            // 根据原始分辨率和文件大小选择压缩策略
            if (fileSizeMB < 10) {
                // 小文件，轻度压缩
                return this.compressVideo(file, {
                    maxWidth: Math.min(originalWidth, 1080),
                    maxHeight: Math.min(originalHeight, 1920),
                    quality: 0.9,
                    maxSizeMB: 15
                });
            } else if (fileSizeMB < 50) {
                // 中等文件，适度压缩
                return this.compressVideo(file, {
                    maxWidth: Math.min(originalWidth, 1080),
                    maxHeight: Math.min(originalHeight, 1920),
                    quality: 0.8,
                    maxSizeMB: 25
                });
            } else {
                // 大文件，较强压缩但保持质量
                return this.compressVideo(file, {
                    maxWidth: Math.min(originalWidth, 720),
                    maxHeight: Math.min(originalHeight, 1280),
                    quality: 0.75,
                    maxSizeMB: 20
                });
            }

        } catch (error) {
            console.error('智能压缩分析失败，使用默认快速压缩:', error);
            return this.quickCompress(file);
        }
    }
}

// 导出单例
export const videoCompressor = new VideoCompressor();