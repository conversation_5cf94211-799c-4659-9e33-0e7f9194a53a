<!-- memos列表 -->
<div id="memosContainer">
    <!-- 加载指示器 -->
    <div id="loadingIndicator" class="loading-container hidden">
        <div class="loading-spinner"></div>
        <div class="loading-text">加载中...</div>
    </div>

    <!-- 空状态 -->
    <div id="emptyState" class="empty-state hidden">
        <svg class="empty-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1"
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
            </path>
        </svg>
        <h3 class="empty-title">暂无内容</h3>
        <p class="empty-description">发布你的第一条Memo吧！</p>
        <button onclick="document.getElementById('memoContent').focus()" class="btn btn-primary btn-icon">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd"
                    d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z"
                    clip-rule="evenodd" />
            </svg>
            新建Memo
        </button>
    </div>

    <!-- Memo列表内容 -->
    <div id="memosContent" class="space-y-4">
        <!-- 动态加载的memo内容 -->
    </div>
</div>