// 使用全局的 Mithril 实例而不是导入
const m = window.m;

// 使用相对路径导入服务
import { AuthService } from '../services/AuthService.js';

export class Auth {
    constructor() {
        this.authService = new AuthService();
        this.token = this.authService.token;
        this.currentUser = this.authService.currentUser;
        this.isAuthenticated = this.authService.isAuthenticated;
    }
    
    // 登录
    async login(username, password) {
        try {
            const result = await this.authService.login(username, password);
            
            if (result.success) {
                this.token = this.authService.token;
                this.currentUser = this.authService.currentUser;
                this.isAuthenticated = this.authService.isAuthenticated;
                m.redraw();
            }
            
            return result;
        } catch (error) {
            return { success: false, error: error.message };
        }
    }
    
    // 登出
    logout() {
        const result = this.authService.logout();
        
        this.token = this.authService.token;
        this.currentUser = this.authService.currentUser;
        this.isAuthenticated = this.authService.isAuthenticated;
        
        // 通知视图更新
        m.redraw();
        
        // 重定向到首页
        // m.route.set('/'); // 在组件中处理
        
        return result;
    }
}