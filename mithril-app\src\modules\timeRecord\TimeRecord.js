/**
 * 精简版时间记录组件
 * 核心功能：计时、保存、显示历史记录
 * 代码行数：~400行 vs 原2000+行
 */

const m = window.m;

// 导入配置
import {
  TIME_RECORD_API_CONFIG,
  TIME_RECORD_CONFIG,
  TIME_RECORD_ERRORS,
  TIME_RECORD_MESSAGES
} from '../../config/timeRecordConfig.js';

// 导入工具类
import { notificationUtils } from '../../utils/NotificationUtils.js';

// 使用配置文件中的常量
const CONFIG = {
  API: {
    DATASHEET_ID: TIME_RECORD_API_CONFIG.DATASHEET_ID,
    TOKEN: TIME_RECORD_API_CONFIG.TOKEN,
    BASE_URL: TIME_RECORD_API_CONFIG.BASE_URL
  },
  MAX_CONTENT_LENGTH: TIME_RECORD_CONFIG.VALIDATION.MAX_CONTENT_LENGTH,
  PAGE_SIZE: TIME_RECORD_CONFIG.PAGINATION.DEFAULT_PAGE_SIZE
};

// 样式将通过传统方式引入，不通过JS模块导入

// 工具函数
const formatDuration = (seconds) => {
  const h = Math.floor(seconds / 3600);
  const m = Math.floor((seconds % 3600) / 60);
  const s = seconds % 60;
  return `${h.toString().padStart(2, '0')}:${m.toString().padStart(2, '0')}:${s.toString().padStart(2, '0')}`;
};

const formatTimestamp = (date) => {
  return new Date(date).toLocaleString('zh-CN');
};

const formatTimeRange = (startTime, endTime) => {
  if (!startTime || !endTime) return '时间未知';

  const start = new Date(startTime);
  const end = new Date(endTime);

  // 格式化开始时间为完整的年月日时分秒
  const startStr = start.toLocaleString('zh-CN');

  // 格式化结束时间，只显示时分秒
  const endTimeStr = end.toLocaleTimeString('zh-CN');

  return `${startStr} - ${endTimeStr}`;
};

// API服务 - 使用v4版本的完整实现
class TimeService {
  constructor() {
    this.requestQueue = [];
    this.isProcessingQueue = false;
    this.requestTimes = [];
    this.maxRequests = 5;
    this.timeWindow = 1000;
  }

  // 检查是否可以发送请求（限流检查）
  canMakeRequest() {
    const now = Date.now();
    this.requestTimes = this.requestTimes.filter(time => now - time < this.timeWindow);
    return this.requestTimes.length < this.maxRequests;
  }

  // 记录请求时间
  recordRequest() {
    this.requestTimes.push(Date.now());
  }

  // 计算需要等待的时间
  getWaitTime() {
    if (this.requestTimes.length === 0) return 0;
    const oldestRequest = Math.min(...this.requestTimes);
    const waitTime = this.timeWindow - (Date.now() - oldestRequest);
    return Math.max(0, waitTime);
  }

  // 执行带限流和重试的请求
  async makeRequest(requestFn, retryOptions = {}) {
    const options = {
      maxAttempts: retryOptions.maxAttempts || 3,
      delay: retryOptions.delay || 1000,
      backoffMultiplier: retryOptions.backoffMultiplier || 2
    };

    return new Promise((resolve, reject) => {
      this.requestQueue.push({
        requestFn,
        resolve,
        reject,
        retryOptions: options,
        attempts: 0
      });
      this.processQueue();
    });
  }

  // 处理请求队列
  async processQueue() {
    if (this.isProcessingQueue || this.requestQueue.length === 0) {
      return;
    }

    this.isProcessingQueue = true;

    while (this.requestQueue.length > 0) {
      if (this.canMakeRequest()) {
        const queueItem = this.requestQueue.shift();
        const { requestFn, resolve, reject, retryOptions, attempts } = queueItem;
        this.recordRequest();

        try {
          const result = await requestFn();
          resolve(result);
        } catch (error) {
          if (this.shouldRetry(error, attempts, retryOptions)) {
            queueItem.attempts = attempts + 1;
            this.requestQueue.unshift(queueItem);

            const delay = retryOptions.delay * Math.pow(retryOptions.backoffMultiplier, attempts);
            await new Promise(resolve => setTimeout(resolve, delay));
          } else {
            reject(error);
          }
        }
      } else {
        const waitTime = this.getWaitTime();
        await new Promise(resolve => setTimeout(resolve, waitTime + 100));
      }
    }

    this.isProcessingQueue = false;
  }

  // 判断是否应该重试
  shouldRetry(error, attempts, retryOptions) {
    if (attempts >= retryOptions.maxAttempts) {
      return false;
    }

    const errorMessage = error.message.toLowerCase();

    // 网络错误 - 可重试
    if (errorMessage.includes('network') ||
      errorMessage.includes('fetch') ||
      errorMessage.includes('timeout') ||
      errorMessage.includes('connection')) {
      return true;
    }

    // API限流错误 - 可重试
    if (errorMessage.includes('rate limit') ||
      errorMessage.includes('429') ||
      errorMessage.includes('too many requests')) {
      return true;
    }

    // 服务器错误 (5xx) - 可重试
    if (errorMessage.includes('500') ||
      errorMessage.includes('502') ||
      errorMessage.includes('503') ||
      errorMessage.includes('504')) {
      return true;
    }

    return false;
  }

  // 执行HTTP请求的通用方法
  async executeRequest(url, options = {}) {
    const config = {
      headers: {
        'Authorization': `Bearer ${CONFIG.API.TOKEN}`,
        'Content-Type': 'application/json'
      },
      timeout: options.timeout || 15000,
      ...options
    };

    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), config.timeout);

      const response = await fetch(url, {
        ...config,
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      let data;
      try {
        data = await response.json();
      } catch (parseError) {
        throw new Error(`响应解析失败: ${parseError.message}`);
      }

      if (!response.ok) {
        // 详细记录错误信息
        console.error('HTTP错误详情:', {
          status: response.status,
          statusText: response.statusText,
          url: url,
          data: data
        });

        const errorInfo = this.classifyHttpError(response.status, data);
        throw new Error(errorInfo.message);
      }

      // 对于vika API，检查success字段
      if (data.success === false) {
        console.error('API业务错误:', data);
        throw new Error(data.message || '操作失败');
      }

      return data;
    } catch (error) {
      if (error.name === 'AbortError') {
        throw new Error('请求超时，请检查网络连接');
      }

      if (error.name === 'TypeError' && error.message.includes('fetch')) {
        throw new Error(TIME_RECORD_ERRORS.NETWORK_ERROR);
      }

      throw error;
    }
  }

  // HTTP错误分类
  classifyHttpError(status, data) {
    const message = data?.message || '';

    switch (status) {
      case 400:
        return {
          type: 'validation',
          message: message || '请求参数错误',
          retryable: false
        };
      case 401:
        return {
          type: 'auth',
          message: TIME_RECORD_ERRORS.PERMISSION_ERROR,
          retryable: false
        };
      case 403:
        return {
          type: 'permission',
          message: TIME_RECORD_ERRORS.PERMISSION_ERROR,
          retryable: false
        };
      case 404:
        return {
          type: 'not_found',
          message: message || '请求的资源不存在',
          retryable: false
        };
      case 429:
        return {
          type: 'rate_limit',
          message: TIME_RECORD_ERRORS.API_LIMIT_ERROR,
          retryable: true
        };
      case 500:
      case 502:
      case 503:
      case 504:
        return {
          type: 'server_error',
          message: message || '服务器错误，请稍后重试',
          retryable: true
        };
      default:
        return {
          type: 'unknown',
          message: message || `HTTP ${status} 错误`,
          retryable: status >= 500
        };
    }
  }

  async createRecord(data) {
    try {
      console.log('开始创建记录:', data);

      // 数据验证
      this.validateRecordData(data);

      // 使用与其他API调用相同的格式，在URL中添加fieldKey参数
      const url = `${CONFIG.API.BASE_URL}/datasheets/${CONFIG.API.DATASHEET_ID}/records?fieldKey=name`;

      // 使用字段名称而不是字段ID，包含所有必要字段
      const requestBody = {
        records: [{
          fields: {
            '时间记录分类': data.category || TIME_RECORD_CONFIG.DEFAULTS.CATEGORY,
            '备注信息': data.content,
            '开始时间戳(秒)': Math.floor(data.startTime / 1000),
            '结束时间戳(秒)': Math.floor(data.endTime / 1000),
            '持续时间文本描述': data.durationText
          }
        }]
      };

      console.log('创建记录请求 (简化版):', { url, requestBody });

      try {
        const response = await this.makeRequest(() =>
          this.executeRequest(url, {
            method: 'POST',
            body: JSON.stringify(requestBody)
          })
        );

        console.log('创建记录响应:', response);

        if (response && response.data && response.data.records && response.data.records.length > 0) {
          const record = response.data.records[0];
          const transformedRecord = {
            id: record.recordId || record.id || Date.now().toString(),
            content: data.content,
            startTime: new Date(data.startTime),
            endTime: new Date(data.endTime),
            duration: data.duration,
            durationText: data.durationText,
            category: data.category || TIME_RECORD_CONFIG.DEFAULTS.CATEGORY,
            createdAt: new Date()
          };

          console.log('转换后的记录:', transformedRecord);

          return {
            success: true,
            data: transformedRecord
          };
        }

        if (response.success) {
          const basicRecord = {
            id: response.data?.recordId || Date.now().toString(),
            ...data,
            createdAt: new Date()
          };

          return {
            success: true,
            data: basicRecord
          };
        }

        throw new Error('创建记录失败：服务器未返回有效数据');
      } catch (simpleError) {
        console.warn('简化版创建失败，尝试完整版:', simpleError.message);

        // 如果简化版失败，尝试完整版本，仍然使用字段名称
        const fullRequestBody = {
          records: [{
            fields: {
              '时间记录分类': data.category || TIME_RECORD_CONFIG.DEFAULTS.CATEGORY,
              '备注信息': data.content,
              '开始时间戳(秒)': Math.floor(data.startTime / 1000),
              '结束时间戳(秒)': Math.floor(data.endTime / 1000),
              '持续时间文本描述': data.durationText
            }
          }]
        };

        console.log('创建记录请求 (完整版):', { url, fullRequestBody });

        const fullResponse = await this.makeRequest(() =>
          this.executeRequest(url, {
            method: 'POST',
            body: JSON.stringify(fullRequestBody)
          })
        );

        console.log('完整版创建记录响应:', fullResponse);

        if (fullResponse && fullResponse.data && fullResponse.data.records && fullResponse.data.records.length > 0) {
          const transformedRecord = this.transformFromVikaFormat(fullResponse.data.records[0]);
          console.log('转换后的记录:', transformedRecord);

          return {
            success: true,
            data: transformedRecord
          };
        }

        throw simpleError; // 如果完整版也失败，抛出原始错误
      }
    } catch (error) {
      console.error('创建记录失败:', error);
      return {
        success: false,
        error: this.getUserFriendlyError(error)
      };
    }
  }

  async getRecords(page = 1) {
    try {
      console.log(`开始获取第 ${page} 页记录，每页 ${CONFIG.PAGE_SIZE} 条`);

      const params = {
        pageNum: page,
        pageSize: Math.min(CONFIG.PAGE_SIZE, 100)
      };

      const url = `${CONFIG.API.BASE_URL}/datasheets/${CONFIG.API.DATASHEET_ID}/records?` +
        `pageSize=${params.pageSize}&pageNum=${params.pageNum}&fieldKey=name`;

      console.log('请求URL:', url);

      const response = await this.makeRequest(() => this.executeRequest(url));

      console.log('API响应:', response);

      if (!response || !response.data) {
        throw new Error('API响应无效：缺少数据字段');
      }

      // 标准化API响应数据结构
      if (response.data && response.data.records) {
        response.data.records = response.data.records.map(record => {
          if (record.recordId && !record.id) {
            record.id = record.recordId;
          }
          if (!record.fields) {
            record.fields = {};
          }
          return record;
        });
      } else {
        response.data.records = [];
      }

      // 转换数据格式
      const records = response.data.records.map(record => {
        try {
          return this.transformFromVikaFormat(record);
        } catch (transformError) {
          console.warn(`记录 ${record.recordId || record.id} 转换失败:`, transformError);
          return null;
        }
      }).filter(record => record !== null);

      // 客户端排序：按创建时间倒序（最新的在前面）
      records.sort((a, b) => {
        // 优先使用createdAt，如果没有则使用endTime作为备选
        const timeA = a.createdAt || a.endTime || new Date(0);
        const timeB = b.createdAt || b.endTime || new Date(0);
        return new Date(timeB) - new Date(timeA); // 倒序排列
      });

      const result = {
        success: true,
        records,
        hasMore: (response.data.pageNum || page) * (response.data.pageSize || CONFIG.PAGE_SIZE) < (response.data.total || 0)
      };

      console.log(`成功获取 ${records.length} 条记录`);

      return result;
    } catch (error) {
      console.error('获取记录失败:', error);
      return {
        success: false,
        error: this.getUserFriendlyError(error),
        records: [],
        hasMore: false
      };
    }
  }

  // 数据验证方法
  validateRecordData(data) {
    if (!data) {
      throw new Error('验证失败：记录数据不能为空');
    }

    if (!data.content || data.content.trim() === '') {
      throw new Error('验证失败：备注内容不能为空');
    }

    if (!data.startTime || typeof data.startTime !== 'number') {
      throw new Error('验证失败：开始时间必须是有效的时间戳');
    }

    if (!data.endTime || typeof data.endTime !== 'number') {
      throw new Error('验证失败：结束时间必须是有效的时间戳');
    }

    if (data.endTime <= data.startTime) {
      throw new Error('验证失败：结束时间必须晚于开始时间');
    }

    const duration = Math.floor((data.endTime - data.startTime) / 1000);
    if (duration < 1) {
      throw new Error('验证失败：记录时长至少为1秒');
    }

    if (data.content.length > CONFIG.MAX_CONTENT_LENGTH) {
      throw new Error(`验证失败：备注内容不能超过${CONFIG.MAX_CONTENT_LENGTH}个字符`);
    }

    return true;
  }

  // 获取用户友好的错误消息
  getUserFriendlyError(error) {
    const message = error.message || '未知错误';

    if (message.includes('验证失败')) {
      return message;
    }

    if (message.includes('网络') || message.includes('连接') || message.includes('timeout')) {
      return TIME_RECORD_ERRORS.NETWORK_ERROR;
    }

    if (message.includes('429') || message.includes('频繁')) {
      return TIME_RECORD_ERRORS.API_LIMIT_ERROR;
    }

    if (message.includes('401') || message.includes('认证')) {
      return TIME_RECORD_ERRORS.PERMISSION_ERROR;
    }

    if (message.includes('403') || message.includes('权限')) {
      return TIME_RECORD_ERRORS.PERMISSION_ERROR;
    }

    if (message.includes('500') || message.includes('服务器')) {
      return TIME_RECORD_ERRORS.SAVE_ERROR;
    }

    return `操作失败: ${message}`;
  }

  // 数据格式转换方法
  transformFromVikaFormat(record) {
    const fields = record.fields || {};

    // 使用字段名称而不是字段ID，与API调用保持一致
    const startTimestamp = fields['开始时间戳(秒)'];
    const endTimestamp = fields['结束时间戳(秒)'];
    const duration = (startTimestamp && endTimestamp) ? endTimestamp - startTimestamp : 0;

    console.log('转换记录字段:', {
      recordId: record.recordId,
      fields: Object.keys(fields),
      startTimestamp,
      endTimestamp,
      duration
    });

    return {
      id: record.recordId || record.id,
      category: fields['时间记录分类'] || '',
      content: fields['备注信息'] || '',
      startTime: startTimestamp ? new Date(startTimestamp * 1000) : null,
      endTime: endTimestamp ? new Date(endTimestamp * 1000) : null,
      duration: duration,
      durationText: fields['持续时间文本描述'] || this.formatDuration(duration),
      createdAt: fields['记录创建时间'] ? new Date(fields['记录创建时间']) : null
    };
  }

  // 格式化持续时间为可读文本
  formatDuration(seconds) {
    if (!seconds || seconds <= 0) return '0秒';

    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;

    const parts = [];
    if (hours > 0) parts.push(`${hours}小时`);
    if (minutes > 0) parts.push(`${minutes}分钟`);
    if (remainingSeconds > 0 || parts.length === 0) parts.push(`${remainingSeconds}秒`);

    return parts.join('');
  }
}

// 主组件
export const SimpleTimeRecord = {
  oninit(vnode) {
    const initialState = {
      service: new TimeService(),
      records: [],
      hasMore: false,
      loading: false,
      error: null,
      inputText: '',
      isRunning: false,
      startTime: null,
      duration: 0,
      timerInterval: null, // 添加定时器ID用于清理
      keyboardHandler: null // 键盘事件处理器
    };

    // 将初始状态赋值给vnode.state
    Object.assign(vnode.state, initialState);

    // 使用原有的加载逻辑
    this.loadRecords(vnode);
    this.startTimerUpdate(vnode);
    this.setupKeyboardShortcuts(vnode);
  },

  startTimerUpdate(vnode) {
    // 清理之前的定时器
    if (vnode.state.timerInterval) {
      clearInterval(vnode.state.timerInterval);
    }

    // 创建新的定时器并保存ID
    const intervalId = setInterval(() => {
      if (vnode.state.isRunning && vnode.state.startTime) {
        const newDuration = Math.floor((Date.now() - vnode.state.startTime) / 1000);
        const newState = {
          duration: newDuration
        };

        // 更新状态
        Object.assign(vnode.state, newState);
        m.redraw();
      }
    }, 1000);

    // 保存定时器ID到状态
    Object.assign(vnode.state, { timerInterval: intervalId });
  },

  /**
   * 设置键盘快捷键
   * Space - 开始/暂停计时
   * Escape - 重置计时器
   * Enter - 保存记录（当计时器停止且有内容时）
   */
  setupKeyboardShortcuts(vnode) {
    const handler = (event) => {
      // 只在组件激活时响应
      if (!vnode.state || document.activeElement.tagName === 'TEXTAREA') {
        return;
      }

      switch (event.code) {
        case 'Space':
          event.preventDefault();
          if (vnode.state.isRunning) {
            this.stopTimer(vnode);
          } else {
            this.startTimer(vnode);
          }
          break;

        case 'Escape':
          event.preventDefault();
          this.resetTimer(vnode);
          break;

        case 'Enter':
          if (!vnode.state.isRunning &&
            vnode.state.duration > 0 &&
            vnode.state.inputText.trim()) {
            event.preventDefault();
            this.saveRecord(vnode);
          }
          break;
      }
    };

    // 保存处理器引用
    Object.assign(vnode.state, { keyboardHandler: handler });

    // 添加事件监听
    document.addEventListener('keydown', handler);
  },

  async loadRecords(vnode) {
    const newState = {
      loading: true,
      records: vnode.state.records,
      hasMore: vnode.state.hasMore,
      error: vnode.state.error
    };

    // 更新状态
    Object.assign(vnode.state, { loading: newState.loading });
    m.redraw();

    try {
      const result = await vnode.state.service.getRecords();
      if (result.success) {
        newState.records = result.records;
        newState.hasMore = result.hasMore;
        newState.error = null;
      } else {
        newState.error = result.error;
        console.error('加载时间记录失败:', result.error);

        // 显示用户友好的错误提示
        if (result.error.includes('500')) {
          notificationUtils.showWarning('时间记录服务暂时不可用，请稍后重试');
        } else if (result.error.includes('404')) {
          notificationUtils.showError('时间记录配置错误，请联系管理员');
        } else {
          notificationUtils.showError('获取时间记录失败: ' + result.error);
        }
      }
    } catch (error) {
      newState.error = error.message;
      console.error('加载时间记录异常:', error);
      notificationUtils.showError('网络连接错误，请检查网络后重试');
    } finally {
      newState.loading = false;

      // 更新状态
      Object.assign(vnode.state, {
        records: newState.records,
        hasMore: newState.hasMore,
        error: newState.error,
        loading: newState.loading
      });
      m.redraw();
    }
  },

  startTimer(vnode) {
    if (!vnode.state.isRunning) {
      const newState = {
        isRunning: true,
        startTime: Date.now(),
        duration: 0,
        inputText: vnode.state.inputText
      };

      // 更新状态
      Object.assign(vnode.state, newState);
      m.redraw();
    }
  },

  stopTimer(vnode) {
    if (vnode.state.isRunning) {
      const newState = {
        isRunning: false
      };

      // 更新状态
      Object.assign(vnode.state, newState);
      m.redraw();
    }
  },

  resetTimer(vnode) {
    const newState = {
      isRunning: false,
      startTime: null,
      duration: 0,
      inputText: ''
    };

    // 更新状态
    Object.assign(vnode.state, newState);
    m.redraw();
  },

  async saveRecord(vnode) {
    let newState = {
      error: vnode.state.error,
      records: [...vnode.state.records]
    };

    if (!vnode.state.duration || vnode.state.duration < 1) {
      newState.error = '计时时长不能少于1秒';
      Object.assign(vnode.state, { error: newState.error });
      m.redraw();
      return;
    }

    if (!vnode.state.inputText.trim()) {
      newState.error = '请输入工作内容';
      Object.assign(vnode.state, { error: newState.error });
      m.redraw();
      return;
    }

    if (vnode.state.inputText.length > CONFIG.MAX_CONTENT_LENGTH) {
      newState.error = `内容不能超过${CONFIG.MAX_CONTENT_LENGTH}字符`;
      Object.assign(vnode.state, { error: newState.error });
      m.redraw();
      return;
    }

    const recordData = {
      content: vnode.state.inputText.trim(),
      startTime: vnode.state.startTime,
      endTime: Date.now(),
      duration: vnode.state.duration,
      durationText: formatDuration(vnode.state.duration)
    };

    const result = await vnode.state.service.createRecord(recordData);
    if (result.success) {
      // 确保result.data存在并且有recordId
      if (result.data && result.data.recordId) {
        newState.records.unshift({
          id: result.data.recordId,
          ...recordData
        });
      } else {
        // 如果没有recordId，使用时间戳作为临时ID
        newState.records.unshift({
          id: Date.now().toString(),
          ...recordData
        });
      }
      this.resetTimer(vnode);
      newState.error = null;

      // 显示成功通知
      notificationUtils.showSuccess(TIME_RECORD_MESSAGES.SAVE_SUCCESS);
    } else {
      newState.error = result.error || '保存记录失败';
    }

    // 更新状态
    Object.assign(vnode.state, {
      records: newState.records,
      error: newState.error
    });
    m.redraw();
  },

  view(vnode) {
    const { isRunning, duration, records, inputText, loading, error } = vnode.state;

    return m('div.simple-time-record', [
      // 计时器区域
      m('div.timer-section', [
        m('div.timer-display', [
          m('span.duration', formatDuration(duration))
        ]),

        m('div.timer-controls', [
          m('button', {
            onclick: () => this.startTimer(vnode),
            disabled: isRunning
          }, '开始计时'),

          m('button', {
            onclick: () => this.stopTimer(vnode),
            disabled: !isRunning
          }, '结束计时'),

          m('button', {
            onclick: () => this.resetTimer(vnode)
          }, '重置')
        ])
      ]),

      // 输入区域
      !isRunning && duration > 0 && [
        m('div.input-section', [
          m('textarea', {
            placeholder: TIME_RECORD_CONFIG.DEFAULTS.CONTENT_PLACEHOLDER,
            value: inputText,
            oninput: (e) => {
              // 使用临时变量避免直接修改vnode.state
              const newInputText = e.target.value;
              const newState = {
                inputText: newInputText,
                error: null
              };

              // 更新状态
              Object.assign(vnode.state, newState);
              m.redraw();
            }
          }),

          m('div.input-info', [
            m('span', `${inputText.length}/${CONFIG.MAX_CONTENT_LENGTH}`),
            m('button', {
              onclick: () => this.saveRecord(vnode),
              disabled: !inputText.trim()
            }, '保存记录')
          ])
        ])
      ],

      // 错误提示
      error && m('div.error-message', error),

      // 快捷键提示
      m('div.shortcuts-hint', [
        m('small', '快捷键: '),
        m('kbd', '空格'), ' 开始/暂停 ',
        m('kbd', 'Esc'), ' 重置 ',
        m('kbd', 'Enter'), ' 保存'
      ]),

      // 历史记录
      m('div.records-section', [
        m('h3', '历史记录'),

        loading && m('div.loading', '加载中...'),

        (!records || records.length === 0) && !loading &&
        m('div.empty', '暂无记录'),

        m('div.records-list',
          records && records.length > 0 ?
            records.map(record =>
              m('div.record-item', [
                m('div.record-time', [
                  m('span', formatTimeRange(record.startTime, record.endTime)),
                  m('span.duration', formatDuration(record.duration))
                ]),
                m('div.record-content', record.content)
              ])
            ) : []
        )
      ])
    ]);
  },

  onremove(vnode) {
    // 清理定时器
    if (vnode.state.timerInterval) {
      clearInterval(vnode.state.timerInterval);
    }

    // 清理键盘事件监听器
    if (vnode.state.keyboardHandler) {
      document.removeEventListener('keydown', vnode.state.keyboardHandler);
    }

    // 清理状态
    const cleanState = {
      isRunning: false,
      startTime: null,
      duration: 0,
      records: [],
      inputText: '',
      loading: false,
      error: null,
      hasMore: false,
      service: null,
      timerInterval: null,
      keyboardHandler: null
    };

    Object.assign(vnode.state, cleanState);
  }
};