/**
 * API批处理器
 * 用于优化API请求，支持请求合并和批处理
 */

class ApiBatchProcessor {
    constructor() {
        // 存储待处理的请求
        this.requestQueue = [];
        // 批处理延迟（毫秒）
        this.batchDelay = 50;
        // 是否正在处理队列
        this.isProcessing = false;
        // 统计信息
        this.stats = {
            totalRequests: 0,
            batchedRequests: 0,
            pendingRequests: 0
        };
    }

    /**
     * 添加请求到批处理队列
     * @param {string} type - 请求类型
     * @param {Function} requestFn - 请求函数
     * @param {number} priority - 优先级（数字越小优先级越高）
     * @returns {Promise} 请求结果
     */
    async addRequest(type, requestFn, priority = 5) {
        return new Promise((resolve, reject) => {
            // 将请求添加到队列
            this.requestQueue.push({
                type,
                requestFn,
                priority,
                resolve,
                reject,
                timestamp: Date.now()
            });
            
            // 更新统计信息
            this.stats.pendingRequests = this.requestQueue.length;
            this.stats.totalRequests++;
            
            // 按优先级排序（优先级数字越小越优先）
            this.requestQueue.sort((a, b) => a.priority - b.priority);
            
            // 延迟处理队列
            this.processQueue();
        });
    }

    /**
     * 处理请求队列
     */
    async processQueue() {
        // 如果已经在处理队列，则直接返回
        if (this.isProcessing) return;
        
        this.isProcessing = true;
        
        // 等待一小段时间以收集更多请求
        await new Promise(resolve => setTimeout(resolve, this.batchDelay));
        
        // 处理队列中的所有请求
        while (this.requestQueue.length > 0) {
            // 取出队列中的第一个请求
            const request = this.requestQueue.shift();
            
            try {
                // 执行请求
                const result = await request.requestFn();
                request.resolve(result);
                
                // 更新统计信息
                this.stats.batchedRequests++;
                this.stats.pendingRequests = this.requestQueue.length;
            } catch (error) {
                request.reject(error);
                
                // 更新统计信息
                this.stats.pendingRequests = this.requestQueue.length;
            }
        }
        
        this.isProcessing = false;
    }

    /**
     * 批量创建记录
     * @param {Array} records - 记录数组
     * @param {TimeRecordService} service - 时间记录服务实例
     * @returns {Promise} 处理结果
     */
    async batchCreateRecords(records, service) {
        // 对于简单实现，我们逐个创建记录
        const results = [];
        
        for (const record of records) {
            try {
                // 转换为vika格式
                const vikaRecord = service.transformToVikaFormat(record);
                const url = service.buildURL('records');
                const requestBody = {
                    records: [vikaRecord],
                    fieldKey: service.fieldKey
                };
                
                const response = await service.executeRequest(url, {
                    method: 'POST',
                    body: JSON.stringify(requestBody)
                });
                
                if (response && response.data && response.data.records && response.data.records.length > 0) {
                    const transformedRecord = service.transformFromVikaFormat(response.data.records[0]);
                    results.push({ status: 'fulfilled', value: transformedRecord });
                } else {
                    results.push({ status: 'rejected', reason: new Error('创建记录失败') });
                }
            } catch (error) {
                results.push({ status: 'rejected', reason: error });
            }
        }
        
        return results;
    }

    /**
     * 批量更新记录
     * @param {Array} updates - 更新数组
     * @param {TimeRecordService} service - 时间记录服务实例
     * @returns {Promise} 处理结果
     */
    async batchUpdateRecords(updates, service) {
        // 对于简单实现，我们逐个更新记录
        const results = [];
        
        for (const update of updates) {
            try {
                const url = service.buildURL(`records/${update.id}`);
                const requestBody = {
                    records: [{
                        recordId: update.id,
                        fields: service.transformToVikaFormat(update.data).fields
                    }],
                    fieldKey: service.fieldKey
                };
                
                const response = await service.executeRequest(url, {
                    method: 'PATCH',
                    body: JSON.stringify(requestBody)
                });
                
                if (response && response.data && response.data.records && response.data.records.length > 0) {
                    const transformedRecord = service.transformFromVikaFormat(response.data.records[0]);
                    results.push({ status: 'fulfilled', value: transformedRecord });
                } else {
                    results.push({ status: 'rejected', reason: new Error('更新记录失败') });
                }
            } catch (error) {
                results.push({ status: 'rejected', reason: error });
            }
        }
        
        return results;
    }

    /**
     * 批量删除记录
     * @param {Array} recordIds - 记录ID数组
     * @param {TimeRecordService} service - 时间记录服务实例
     * @returns {Promise} 处理结果
     */
    async batchDeleteRecords(recordIds, service) {
        // 对于简单实现，我们逐个删除记录
        const results = [];
        
        for (const id of recordIds) {
            try {
                const url = service.buildURL(`records/${id}`);
                const response = await service.executeRequest(url, {
                    method: 'DELETE'
                });
                
                if (response && response.success) {
                    results.push({ status: 'fulfilled', value: { id, success: true } });
                } else {
                    results.push({ status: 'rejected', reason: new Error('删除记录失败') });
                }
            } catch (error) {
                results.push({ status: 'rejected', reason: error });
            }
        }
        
        return results;
    }

    /**
     * 批量获取记录
     * @param {Array} recordIds - 记录ID数组
     * @param {TimeRecordService} service - 时间记录服务实例
     * @returns {Promise} 处理结果
     */
    async batchGetRecords(recordIds, service) {
        // 对于简单实现，我们逐个获取记录
        const results = [];
        
        for (const id of recordIds) {
            try {
                const url = service.buildURL(`records/${id}`);
                const response = await service.executeRequest(url);
                
                if (response && response.data && response.data.records && response.data.records.length > 0) {
                    const transformedRecord = service.transformFromVikaFormat(response.data.records[0]);
                    results.push({ status: 'fulfilled', value: transformedRecord });
                } else {
                    results.push({ status: 'rejected', reason: new Error('获取记录失败') });
                }
            } catch (error) {
                results.push({ status: 'rejected', reason: error });
            }
        }
        
        return results;
    }

    /**
     * 获取统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        return { ...this.stats };
    }

    /**
     * 清理批处理器
     */
    cleanup() {
        this.requestQueue = [];
        this.isProcessing = false;
        this.stats = {
            totalRequests: 0,
            batchedRequests: 0,
            pendingRequests: 0
        };
    }
}

// 创建全局实例
export const apiBatchProcessor = new ApiBatchProcessor();