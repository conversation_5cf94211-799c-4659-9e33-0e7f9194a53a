/**
 * 通知工具类
 * 提供统一的用户通知接口
 */

export class NotificationUtils {
  static showNotification(message, type = 'info', duration = 5000) {
    // 检查是否有全局通知系统
    if (window.appState && window.appState.showNotification) {
      window.appState.showNotification(message, type, duration);
      return;
    }
    
    // 如果没有全局通知系统，使用浏览器原生通知
    if (type === 'error') {
      console.error('错误:', message);
      alert(`错误: ${message}`);
    } else if (type === 'warning') {
      console.warn('警告:', message);
      alert(`警告: ${message}`);
    } else if (type === 'success') {
      console.log('成功:', message);
      // 成功消息可以不显示alert，只在控制台记录
    } else {
      console.info('信息:', message);
    }
  }
  
  static showError(message, duration = 5000) {
    this.showNotification(message, 'error', duration);
  }
  
  static showWarning(message, duration = 5000) {
    this.showNotification(message, 'warning', duration);
  }
  
  static showSuccess(message, duration = 3000) {
    this.showNotification(message, 'success', duration);
  }
  
  static showInfo(message, duration = 4000) {
    this.showNotification(message, 'info', duration);
  }
}

// 导出工具对象，包含所有静态方法
export const notificationUtils = {
  showNotification: NotificationUtils.showNotification,
  showError: NotificationUtils.showError,
  showWarning: NotificationUtils.showWarning,
  showSuccess: NotificationUtils.showSuccess,
  showInfo: NotificationUtils.showInfo
};