/* 简化版时间记录样式 */
.simple-time-record {
    max-width: 600px;
    margin: 0 auto;
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

.simple-time-record h2 {
    text-align: center;
    color: #333;
    margin-bottom: 30px;
}

.timer-section {
    text-align: center;
    margin-bottom: 30px;
    padding: 30px;
    background: #f8f9fa;
    border-radius: 10px;
}

.timer-display {
    margin-bottom: 20px;
}

.timer-display .duration {
    font-size: 48px;
    font-weight: bold;
    color: #007bff;
    font-family: 'Courier New', monospace;
}

.timer-controls {
    display: flex;
    gap: 10px;
    justify-content: center;
}

.timer-controls button {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
    transition: all 0.3s ease;
}

.timer-controls button:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

.timer-controls button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.timer-controls button:nth-child(1) {
    background: #28a745;
    color: white;
}

.timer-controls button:nth-child(2) {
    background: #dc3545;
    color: white;
}

.timer-controls button:nth-child(3) {
    background: #6c757d;
    color: white;
}

.input-section {
    margin-bottom: 30px;
}

.input-section textarea {
    width: 100%;
    min-height: 100px;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 16px;
    resize: vertical;
}

.input-section textarea:focus {
    outline: none;
    border-color: #007bff;
}

.input-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 10px;
}

.input-info span {
    color: #666;
    font-size: 14px;
}

.input-info button {
    padding: 8px 16px;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
}

.input-info button:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.error-message {
    background: #f8d7da;
    color: #721c24;
    padding: 10px;
    border-radius: 5px;
    margin-bottom: 20px;
}

.records-section h3 {
    color: #333;
    margin-bottom: 20px;
    border-bottom: 2px solid #007bff;
    padding-bottom: 5px;
}

.loading {
    text-align: center;
    color: #666;
    padding: 20px;
}

.empty {
    text-align: center;
    color: #666;
    padding: 40px;
    font-style: italic;
}

.records-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.record-item {
    padding: 15px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background: white;
    transition: box-shadow 0.3s ease;
}

.record-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.record-time {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 14px;
    color: #666;
}

.record-time .duration {
    font-weight: bold;
    color: #007bff;
}

.record-content {
    color: #333;
    line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 600px) {
    .simple-time-record {
        padding: 10px;
    }
    
    .timer-display .duration {
        font-size: 36px;
    }
    
    .timer-controls {
        flex-direction: column;
    }
    
    .timer-controls button {
        width: 100%;
    }
}