# 当记 - 极简设计指南

## 🎯 设计理念

这次样式重构采用极简主义设计原则，追求简洁、清晰、易用的界面体验。

### 核心原则

1. **极简至上** - 去除一切不必要的装饰元素
2. **内容为王** - 让内容成为界面的主角
3. **清晰易读** - 优秀的排版和对比度
4. **一致性** - 统一的设计语言和交互模式
5. **高效实用** - 专注于功能性而非装饰性

## 🎨 设计系统

### 颜色系统
```css
--primary: #000        /* 主色：纯黑 */
--secondary: #666      /* 次要文字颜色 */
--muted: #999         /* 弱化文字颜色 */
--border: #e5e5e5     /* 边框颜色 */
--background: #fff    /* 背景色：纯白 */
--surface: #fafafa    /* 表面色：浅灰 */
```

### 间距系统
```css
--space-xs: 0.5rem    /* 8px */
--space-sm: 1rem      /* 16px */
--space-md: 1.5rem    /* 24px */
--space-lg: 2rem      /* 32px */
--space-xl: 3rem      /* 48px */
```

### 字体系统
```css
--font-size-sm: 0.875rem   /* 14px */
--font-size-base: 1rem     /* 16px */
--font-size-lg: 1.125rem   /* 18px */
--font-size-xl: 1.5rem     /* 24px */
```

### 设计元素
```css
--radius: 8px              /* 统一圆角 */
--border-width: 1px        /* 统一边框宽度 */
--transition: 0.2s ease    /* 统一过渡动画 */
```

## 📐 组件设计

### 按钮 (.btn)
- **主要按钮**: 黑色背景，白色文字
- **次要按钮**: 白色背景，黑色边框
- **小按钮**: 减少内边距的紧凑版本
- **悬浮效果**: 简单的透明度变化

### 卡片 (.memo-card)
- 白色背景
- 细边框分隔
- 悬浮时边框变为主色
- 无阴影，无装饰

### 表单元素
- 简洁的输入框设计
- 聚焦时边框变为主色
- 清晰的标签和占位符

### 布局
- 居中的内容容器
- 充足的留白空间
- 清晰的视觉层次

## 🚫 避免的元素

1. **复杂渐变** - 使用纯色代替
2. **多重阴影** - 完全去除阴影效果
3. **复杂动画** - 只保留必要的过渡
4. **装饰图标** - 减少不必要的图标使用
5. **多彩配色** - 坚持黑白灰主调

## 📱 响应式设计

### 移动端适配
- 头部导航垂直排列
- 发布操作垂直排列
- 图片网格变为单列
- 模态框适配小屏幕

### 断点
- 768px 以下为移动端
- 简单的媒体查询规则

## 🎯 使用指南

### HTML 结构
```html
<!-- 按钮 -->
<button class="btn btn-primary">主要操作</button>
<button class="btn btn-secondary">次要操作</button>

<!-- 卡片 -->
<div class="memo-card">
    <div class="memo-header">
        <div class="memo-time">时间</div>
    </div>
    <div class="memo-content">内容</div>
</div>
```

### Mithril 组件
```javascript
// 按钮
m('button.btn.btn-primary', '主要按钮')

// 卡片
m('.memo-card', [
    m('.memo-header', [
        m('.memo-time', '时间')
    ]),
    m('.memo-content', '内容')
])
```

## ✨ 设计优势

1. **加载速度快** - 极简的 CSS 代码
2. **易于维护** - 简单的样式结构
3. **跨平台兼容** - 基础的 CSS 特性
4. **专注内容** - 不会分散用户注意力
5. **永不过时** - 经典的黑白设计

## 🔧 自定义

如需自定义主题，只需修改 CSS 变量：

```css
:root {
    --primary: #your-color;
    --background: #your-bg;
}
```

## 📋 检查清单

设计新组件时，请确保：

- [ ] 使用统一的颜色变量
- [ ] 遵循间距系统
- [ ] 保持简洁的视觉效果
- [ ] 避免不必要的装饰
- [ ] 确保良好的对比度
- [ ] 测试响应式效果

---

这个极简设计系统为当记应用提供了清晰、高效、易用的界面，让用户能够专注于内容创作和阅读体验。