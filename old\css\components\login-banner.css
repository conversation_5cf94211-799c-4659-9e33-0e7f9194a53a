/**

 * 登录横幅样式

 */



.login-banner {

    background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%);

    border-radius: var(--radius-xl);

    margin-bottom: 2rem;

    box-shadow: 0 4px 20px rgba(59, 130, 246, 0.2);

    position: relative;

    overflow: hidden;

    animation: login-banner-slide-in 0.5s ease-out;

}



.login-banner::before {

    content: '';

    position: absolute;

    top: 0;

    left: -100%;

    width: 100%;

    height: 100%;

    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);

    animation: login-banner-shine 3s ease-in-out infinite;

}



.login-banner-content {

    display: flex;

    align-items: center;

    padding: 1.5rem 2rem;

    position: relative;

    z-index: 1;

    gap: 1rem;

}



.login-banner-icon {

    flex-shrink: 0;

    width: 3rem;

    height: 3rem;

    background: rgba(255, 255, 255, 0.2);

    border-radius: 50%;

    display: flex;

    align-items: center;

    justify-content: center;

    backdrop-filter: blur(10px);

}



.login-banner-icon svg {

    width: 1.5rem;

    height: 1.5rem;

    color: white;

}



.login-banner-text {

    flex: 1;

    color: white;

}



.login-banner-title {

    font-size: 1.125rem;

    font-weight: 600;

    margin-bottom: 0.25rem;

    line-height: 1.4;

}



.login-banner-description {

    font-size: 0.875rem;

    opacity: 0.9;

    line-height: 1.5;

    margin: 0;

}



.login-banner-actions {

    display: flex;

    align-items: center;

    gap: 0.75rem;

}



.login-banner .btn {

    background: rgba(255, 255, 255, 0.2);

    border: 1px solid rgba(255, 255, 255, 0.3);

    color: white;

    backdrop-filter: blur(10px);

    transition: all 0.3s ease;

    font-size: 0.875rem;

    padding: 0.5rem 1rem;

}



.login-banner .btn:hover {

    background: rgba(255, 255, 255, 0.3);

    transform: translateY(-1px);

    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);

}



.login-banner .btn svg {

    width: 1rem;

    height: 1rem;

    margin-right: 0.5rem;

}



.login-banner-close {

    padding: 0.5rem !important;

    border-radius: 50% !important;

    opacity: 0.8;

}



.login-banner-close:hover {

    opacity: 1;

    background: rgba(255, 255, 255, 0.3) !important;

}



.login-banner-close svg {

    margin: 0 !important;

    width: 1.25rem !important;

    height: 1.25rem !important;

}



/* 动画效果 */

@keyframes login-banner-slide-in {

    from {

        opacity: 0;

        transform: translateY(-20px);

    }

    to {

        opacity: 1;

        transform: translateY(0);

    }

}



@keyframes login-banner-shine {

    0% {

        left: -100%;

    }

    100% {

        left: 100%;

    }

}



/* 响应式设计 */

@media (max-width: 768px) {

    .login-banner-content {

        padding: 1rem 1.5rem;

        gap: 0.75rem;

    }

    

    .login-banner-icon {

        width: 2.5rem;

        height: 2.5rem;

    }

    

    .login-banner-icon svg {

        width: 1.25rem;

        height: 1.25rem;

    }

    

    .login-banner-title {

        font-size: 1rem;

    }

    

    .login-banner-description {

        font-size: 0.8rem;

    }

    

    .login-banner-actions {

        flex-direction: column;

        gap: 0.5rem;

    }

    

    .login-banner .btn {

        font-size: 0.8rem;

        padding: 0.4rem 0.8rem;

    }

}



@media (max-width: 480px) {

    .login-banner-content {

        flex-direction: column;

        text-align: center;

        padding: 1.5rem 1rem;

    }

    

    .login-banner-actions {

        flex-direction: row;

        justify-content: center;

        margin-top: 0.5rem;

    }

}



/* 深色主题适配 */

.dark .login-banner {

    box-shadow: 0 4px 20px rgba(59, 130, 246, 0.3);

}



/* 护眼模式适配 */

.eye-protect .login-banner {

    background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-accent) 100%);

    box-shadow: 0 4px 20px rgba(16, 185, 129, 0.2);

}



/* 隐藏状态 */

.login-banner.hidden {

    display: none;

}



/* 淡出动画 */

.login-banner.fade-out {

    animation: login-banner-fade-out 0.3s ease-in forwards;

}



@keyframes login-banner-fade-out {

    from {

        opacity: 1;

        transform: translateY(0);

    }

    to {

        opacity: 0;

        transform: translateY(-10px);

    }

}