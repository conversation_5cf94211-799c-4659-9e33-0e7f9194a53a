// 使用全局的 Mithril 实例
const m = window.m;

// 导入 Markdown 工具
import { markdownR<PERSON><PERSON>, MarkdownHelper } from '../utils/markdown.js';

/**
 * 增强的 Markdown 编辑器组件
 * 支持实时预览、工具栏、语法高亮等功能
 */
export const MarkdownEditor = {
    oninit: function(vnode) {
        this.content = vnode.attrs.value || '';
        this.showPreview = false;
        this.showToolbar = vnode.attrs.showToolbar !== false;
        this.placeholder = vnode.attrs.placeholder || '支持 Markdown 语法...';
        this.minHeight = vnode.attrs.minHeight || '100px';
        this.maxHeight = vnode.attrs.maxHeight || '400px';
        this.autoResize = vnode.attrs.autoResize !== false;
        this.debounceTimer = null;
        this.debounceDelay = 300;
        
        // 工具栏按钮配置
        this.toolbarButtons = MarkdownHelper.getToolbarButtons();
        
        // 预览内容缓存
        this.previewContent = '';
        this.lastRenderedContent = '';
    },

    onupdate: function(vnode) {
        // 同步外部传入的值
        if (vnode.attrs.value !== undefined && vnode.attrs.value !== this.content) {
            this.content = vnode.attrs.value;
            this.updatePreview();
        }
    },

    /**
     * 处理输入变化
     */
    handleInput: function(e) {
        this.content = e.target.value;
        
        // 自动调整高度
        if (this.autoResize) {
            this.adjustTextareaHeight(e.target);
        }
        
        // 防抖更新预览
        this.debounceUpdatePreview();
        
        // 通知父组件
        if (this.attrs.oninput) {
            this.attrs.oninput(e);
        }
        
        m.redraw();
    },

    /**
     * 防抖更新预览
     */
    debounceUpdatePreview: function() {
        if (this.debounceTimer) {
            clearTimeout(this.debounceTimer);
        }
        
        this.debounceTimer = setTimeout(() => {
            this.updatePreview();
            m.redraw();
        }, this.debounceDelay);
    },

    /**
     * 更新预览内容
     */
    updatePreview: function() {
        if (this.content !== this.lastRenderedContent) {
            this.previewContent = markdownRenderer.render(this.content);
            this.lastRenderedContent = this.content;
        }
    },

    /**
     * 自动调整文本框高度
     */
    adjustTextareaHeight: function(textarea) {
        textarea.style.height = 'auto';
        const scrollHeight = textarea.scrollHeight;
        const minHeight = parseInt(this.minHeight);
        const maxHeight = parseInt(this.maxHeight);
        
        const newHeight = Math.min(Math.max(scrollHeight, minHeight), maxHeight);
        textarea.style.height = newHeight + 'px';
    },

    /**
     * 处理工具栏按钮点击
     */
    handleToolbarAction: function(button, e) {
        e.preventDefault();
        const textarea = document.getElementById('markdown-textarea');
        if (textarea && button.action) {
            button.action(textarea);
        }
    },

    /**
     * 处理键盘快捷键
     */
    handleKeyDown: function(e) {
        // Ctrl/Cmd + B: 粗体
        if ((e.ctrlKey || e.metaKey) && e.key === 'b') {
            e.preventDefault();
            MarkdownHelper.insertMarkdown(e.target, '**', '**', '粗体文本');
            return;
        }
        
        // Ctrl/Cmd + I: 斜体
        if ((e.ctrlKey || e.metaKey) && e.key === 'i') {
            e.preventDefault();
            MarkdownHelper.insertMarkdown(e.target, '*', '*', '斜体文本');
            return;
        }
        
        // Ctrl/Cmd + K: 链接
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            MarkdownHelper.insertMarkdown(e.target, '[', '](url)', '链接文本');
            return;
        }
        
        // Ctrl/Cmd + Enter: 提交（如果有回调）
        if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
            if (this.attrs.onsubmit) {
                e.preventDefault();
                this.attrs.onsubmit();
            }
            return;
        }
        
        // Tab: 插入缩进
        if (e.key === 'Tab') {
            e.preventDefault();
            MarkdownHelper.insertMarkdown(e.target, '  ', '', '');
            return;
        }
    },

    /**
     * 切换预览模式
     */
    togglePreview: function() {
        this.showPreview = !this.showPreview;
        if (this.showPreview) {
            this.updatePreview();
        }
    },

    view: function(vnode) {
        this.attrs = vnode.attrs;
        
        return m('.markdown-editor', {
            class: this.showPreview ? 'preview-mode' : ''
        }, [
            // 工具栏
            this.showToolbar ? m('.markdown-toolbar', [
                m('.toolbar-group', [
                    // 格式化按钮
                    this.toolbarButtons.map(button => 
                        m('button.toolbar-btn', {
                            key: button.name,
                            title: button.title,
                            onclick: (e) => this.handleToolbarAction(button, e)
                        }, button.icon)
                    )
                ]),
                m('.toolbar-group', [
                    // 预览切换按钮
                    m('button.toolbar-btn.preview-toggle', {
                        class: this.showPreview ? 'active' : '',
                        title: this.showPreview ? '编辑模式' : '预览模式',
                        onclick: () => this.togglePreview()
                    }, this.showPreview ? '✏️' : '👁️')
                ])
            ]) : null,

            // 编辑器内容区域
            m('.editor-content', [
                // 编辑模式
                !this.showPreview ? m('textarea.markdown-textarea', {
                    id: 'markdown-textarea',
                    value: this.content,
                    placeholder: this.placeholder,
                    style: {
                        minHeight: this.minHeight,
                        maxHeight: this.maxHeight
                    },
                    oninput: (e) => this.handleInput(e),
                    onkeydown: (e) => this.handleKeyDown(e),
                    oncreate: (vnode) => {
                        if (this.autoResize) {
                            this.adjustTextareaHeight(vnode.dom);
                        }
                    }
                }) : null,

                // 预览模式
                this.showPreview ? m('.markdown-preview', {
                    innerHTML: this.previewContent || '<p class="preview-empty">暂无内容</p>'
                }) : null
            ]),

            // 状态栏
            m('.editor-status', [
                m('.status-info', [
                    m('span.char-count', `${this.content.length} 字符`),
                    this.content ? m('span.word-count', `${this.content.split(/\s+/).filter(w => w).length} 词`) : null
                ]),
                m('.status-tips', [
                    m('span.tip', 'Ctrl+B 粗体 | Ctrl+I 斜体 | Ctrl+K 链接')
                ])
            ])
        ]);
    }
};