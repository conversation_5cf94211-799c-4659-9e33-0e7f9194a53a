# 时间记录模块设计文档

## 概述

时间记录模块是一个集成到现有mithril应用中的功能模块，提供时间跟踪、记录保存和历史查看功能。该模块采用MVC架构模式，使用vika表作为数据存储后端，确保数据的持久化和可靠性。

## 架构

### 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   View Layer    │    │  Model Layer    │    │ Service Layer   │
│                 │    │                 │    │                 │
│ TimeRecordPage  │◄──►│ TimeRecord      │◄──►│TimeRecordService│
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                                                        ▼
                                               ┌─────────────────┐
                                               │   Vika API      │
                                               │                 │
                                               │ dsta8guxXYK3pCYsVe │
                                               └─────────────────┘
```

### 模块结构
- **TimeRecordPage** - 主要的UI组件，处理用户交互
- **TimeRecord** - 数据模型，管理时间记录状态和业务逻辑
- **TimeRecordService** - API服务层，处理与vika表的数据交互

## 组件和接口

### 1. TimeRecordPage 组件

**职责：**
- 渲染时间记录界面
- 处理用户交互（开始/结束计时、输入备注、保存记录）
- 显示计时器状态和历史记录

**主要方法：**
```javascript
{
  oninit() // 初始化组件，加载历史记录
  view() // 渲染UI界面
  startTimer() // 开始计时
  stopTimer() // 结束计时
  saveRecord() // 保存记录
  loadMoreRecords() // 加载更多历史记录
}
```

**状态管理：**
- 计时器状态（运行中/停止）
- 当前时长显示
- 用户输入内容
- 历史记录列表
- 加载状态

### 2. TimeRecord 模型

**职责：**
- 管理时间记录的业务逻辑
- 维护计时器状态
- 格式化时间显示
- 处理数据验证

**属性：**
```javascript
{
  isRunning: boolean,        // 计时器是否运行中
  startTime: Date,           // 开始时间
  endTime: Date,             // 结束时间
  duration: number,          // 持续时长（秒）
  content: string,           // 备注内容
  category: string,          // 记录分类
  records: Array,            // 历史记录列表
  currentPage: number,       // 当前页码
  hasMore: boolean,          // 是否有更多记录
  loading: boolean           // 加载状态
}
```

**主要方法：**
```javascript
{
  startTimer(),              // 开始计时
  stopTimer(),               // 结束计时
  resetTimer(),              // 重置计时器
  formatDuration(seconds),   // 格式化时长显示
  saveRecord(),              // 保存当前记录
  loadRecords(page),         // 加载历史记录
  validateInput()            // 验证用户输入
}
```

### 3. TimeRecordService 服务

**职责：**
- 封装vika API调用
- 处理数据格式转换
- 管理API请求限流
- 错误处理和重试机制

**API方法：**
```javascript
{
  getRecords(page, limit),   // 获取记录列表
  createRecord(data),        // 创建新记录
  updateRecord(id, data),    // 更新记录
  deleteRecord(id)           // 删除记录
}
```

## 数据模型

### Vika表字段映射

根据vika-time-api.md文档，时间记录表包含以下字段：

| 字段名称 | 字段ID | 类型 | 描述 | 用途 |
|---------|--------|------|------|------|
| 时间记录分类 | fld9Mole3W9eR | 单行文本 | 记录分类标签 | 用于分类管理不同类型的时间记录 |
| 备注信息 | fld0znfZ7H9xM | 多行文本 | 详细备注内容 | 存储用户输入的工作内容描述 |
| 开始时间戳(秒) | fld3e659QaQ11 | 数字 | Unix时间戳 | 记录开始时间的精确时间戳 |
| 结束时间戳(秒) | flddjkaHotlMJ | 数字 | Unix时间戳 | 记录结束时间的精确时间戳 |
| 持续时间文本描述 | fldNY5MabI72y | 单行文本 | 可读的时长描述 | 存储格式化的时长文本，便于阅读 |
| 记录创建时间 | fld1pawhrPs9x | 创建时间 | 自动生成 | 系统自动记录的创建时间戳 |
| 用户 | fldS1LvP1QVhN | 单向关联 | 关联用户表 | 关联到用户表，支持多用户场景 |

### 数据转换逻辑

**保存记录时的数据转换：**
```javascript
// 前端数据 → vika字段
{
  category: "工作记录",           // → fld9Mole3W9eR
  content: "完成项目文档编写",     // → fld0znfZ7H9xM
  startTime: Date,              // → fld3e659QaQ11 (转换为Unix时间戳)
  endTime: Date,                // → flddjkaHotlMJ (转换为Unix时间戳)
  durationText: "2小时30分钟"    // → fldNY5MabI72y
}
```

**读取记录时的数据转换：**
```javascript
// vika字段 → 前端数据
{
  id: record.recordId,
  category: fields.fld9Mole3W9eR,
  content: fields.fld0znfZ7H9xM,
  startTime: new Date(fields.fld3e659QaQ11 * 1000),
  endTime: new Date(fields.flddjkaHotlMJ * 1000),
  duration: fields.flddjkaHotlMJ - fields.fld3e659QaQ11,
  durationText: fields.fldNY5MabI72y,
  createdAt: new Date(fields.fld1pawhrPs9x)
}
```

## 错误处理

### API错误处理策略

1. **网络错误**
   - 检测网络连接状态
   - 提供重试机制
   - 显示用户友好的错误信息

2. **API限流处理**
   - 实现请求队列管理
   - 自动重试机制（指数退避）
   - 用户操作反馈

3. **数据验证错误**
   - 前端输入验证
   - API响应验证
   - 错误信息本地化

4. **权限错误**
   - 检测API权限
   - 引导用户重新认证
   - 降级功能处理

### 错误信息设计

```javascript
const ERROR_MESSAGES = {
  NETWORK_ERROR: '网络连接失败，请检查网络设置',
  API_LIMIT: 'API请求过于频繁，请稍后再试',
  VALIDATION_ERROR: '输入数据格式不正确',
  PERMISSION_ERROR: '没有访问权限，请重新登录',
  UNKNOWN_ERROR: '操作失败，请稍后重试'
};
```

## 测试策略

### 单元测试

1. **TimeRecord模型测试**
   - 计时器功能测试
   - 数据格式化测试
   - 输入验证测试

2. **TimeRecordService测试**
   - API调用测试
   - 数据转换测试
   - 错误处理测试

3. **TimeRecordPage组件测试**
   - 用户交互测试
   - 状态管理测试
   - 渲染逻辑测试

### 集成测试

1. **API集成测试**
   - vika API连接测试
   - 数据CRUD操作测试
   - 错误场景测试

2. **用户流程测试**
   - 完整的时间记录流程
   - 历史记录查看流程
   - 错误恢复流程

### 性能测试

1. **响应时间测试**
   - API调用响应时间
   - 界面渲染性能
   - 大量数据加载性能

2. **内存使用测试**
   - 长时间运行内存泄漏检测
   - 大量历史记录内存使用

## 用户界面设计

### 主界面布局

```
┌─────────────────────────────────────────┐
│                 导航栏                   │
├─────────────────────────────────────────┤
│              时间记录模块                │
│                                         │
│  ┌─────────────────────────────────────┐ │
│  │           计时器显示区               │ │
│  │        [00:15:30]                  │ │
│  │                                    │ │
│  │    [开始计时]  [结束计时]  [重置]    │ │
│  └─────────────────────────────────────┘ │
│                                         │
│  ┌─────────────────────────────────────┐ │
│  │           备注输入区                 │ │
│  │  ┌─────────────────────────────────┐ │ │
│  │  │  请输入工作内容...              │ │ │
│  │  │                                │ │ │
│  │  └─────────────────────────────────┘ │ │
│  │              [保存记录]             │ │
│  └─────────────────────────────────────┘ │
│                                         │
│  ┌─────────────────────────────────────┐ │
│  │           历史记录列表               │ │
│  │  • 2024-01-15 09:00-11:30 (2.5h)  │ │
│  │    完成项目文档编写                 │ │
│  │                                    │ │
│  │  • 2024-01-15 14:00-16:15 (2.25h) │ │
│  │    代码审查和bug修复               │ │
│  │                                    │ │
│  │           [加载更多]                │ │
│  └─────────────────────────────────────┘ │
└─────────────────────────────────────────┘
```

### 响应式设计

- **桌面端**：三栏布局，充分利用屏幕空间
- **平板端**：两栏布局，适应中等屏幕
- **移动端**：单栏布局，垂直堆叠所有元素

### 视觉反馈设计

1. **计时状态指示**
   - 运行中：绿色背景，脉冲动画
   - 已停止：灰色背景，静态显示
   - 暂停中：黄色背景，闪烁提示

2. **按钮状态**
   - 可用：正常颜色，可点击
   - 禁用：灰色，不可点击
   - 加载中：显示加载动画

3. **数据状态**
   - 加载中：骨架屏或加载动画
   - 空状态：友好的空状态插图
   - 错误状态：错误图标和重试按钮

## 性能优化

### 前端优化

1. **组件优化**
   - 使用Mithril的生命周期方法优化渲染
   - 避免不必要的重新渲染
   - 合理使用组件缓存

2. **数据管理**
   - 实现分页加载，避免一次性加载大量数据
   - 使用本地缓存减少API调用
   - 实现数据预加载机制

3. **用户体验**
   - 实现乐观更新，提升操作响应速度
   - 使用防抖技术优化用户输入处理
   - 提供离线功能支持

### API优化

1. **请求优化**
   - 实现请求队列管理，遵守API限流规则
   - 使用批量操作减少请求次数
   - 实现智能重试机制

2. **数据传输**
   - 只请求必要的字段，减少数据传输量
   - 使用压缩传输减少网络开销
   - 实现增量更新机制

## 安全考虑

### 数据安全

1. **输入验证**
   - 前端输入格式验证
   - 防止XSS攻击
   - 数据长度限制

2. **API安全**
   - 使用HTTPS传输
   - API token安全存储
   - 请求签名验证

3. **用户隐私**
   - 敏感数据加密存储
   - 用户数据访问控制
   - 数据删除和清理机制

### 错误信息安全

- 不暴露系统内部信息
- 提供用户友好的错误描述
- 记录详细错误日志用于调试