<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>当记</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/vendor/github.min.css">
    <script charset="UTF-8" id="LA_COLLECT" src="//sdk.51.la/js-sdk-pro.min.js"></script>
    <script>LA.init({id:"Jqud4Iv4jX9nBtrP",ck:"Jqud4Iv4jX9nBtrP"})</script>
    <!-- 使用CDN链接替代本地文件 -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      // 配置Tailwind CSS以避免生产环境警告
      tailwind.config = {
        corePlugins: {
          preflight: false,
        }
      }
    </script>
    <script src="https://cdn.jsdelivr.net/npm/markdown-it@13.0.1/dist/markdown-it.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/highlight.js@11.8.0/lib/index.min.js"></script>
    
    <!-- 离线支持 -->
    <style>
        #offline-banner {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: #f44336;
            color: white;
            text-align: center;
            padding: 10px;
            z-index: 9999;
            display: none;
        }
        
        #offline-banner.show {
            display: block;
        }
    </style>
</head>

<body class="main-container">
    <div class="content-wrapper">
        <!-- 主题切换器组件 -->
        <div id="headerComponent"></div>

        <!-- 未登录提示横幅组件 -->
        <div id="loginBannerComponent"></div>

        <!-- 登录模态窗组件 -->
        <div id="loginModalComponent"></div>

        <!-- 发布区域组件 -->
        <div id="postSectionComponent"></div>

        <!-- memos列表组件 -->
        <div id="memosListComponent"></div>

        <!-- 加载更多按钮组件 -->
        <div id="loadMoreButtonComponent"></div>
    </div>

    <!-- 底部区域组件 -->
    <div id="footerComponent"></div>

    <!-- 更新日志模态窗组件 -->
    <div id="updateLogModalComponent"></div>

    <!-- 初始化脚本 -->
    <script type="module">
        import { componentLoader } from './js/modules/componentLoader.js';

        // 定义组件配置
        const components = [
            { containerId: 'headerComponent', componentPath: 'components/header.html' },
            { containerId: 'loginBannerComponent', componentPath: 'components/login-banner.html' },
            { containerId: 'loginModalComponent', componentPath: 'components/login-modal.html' },
            { containerId: 'postSectionComponent', componentPath: 'components/post-section.html' },
            { containerId: 'memosListComponent', componentPath: 'components/memos-list.html' },
            { containerId: 'loadMoreButtonComponent', componentPath: 'components/load-more-button.html' },
            { containerId: 'footerComponent', componentPath: 'components/footer.html' },
            { containerId: 'updateLogModalComponent', componentPath: 'components/update-log-modal.html' }
        ];

        // 页面加载完成后初始化组件
        document.addEventListener('DOMContentLoaded', async () => {
            try {
                console.log('开始加载组件...');

                // 加载所有组件
                await componentLoader.loadComponents(components);
                console.log('所有组件加载完成');

                // 等待一小段时间确保DOM完全渲染
                await new Promise(resolve => setTimeout(resolve, 100));

                // 动态导入主应用模块
                await import('./js/app.js');
                console.log('应用模块加载完成');

                // 手动触发应用初始化（因为DOMContentLoaded已经触发过了）
                if (window.app && typeof window.app.init === 'function') {
                    console.log('手动初始化应用...');
                    await window.app.init();
                    console.log('应用手动初始化完成');
                }

            } catch (error) {
                console.error('组件加载失败:', error);

                // 显示错误信息
                document.body.innerHTML = `
                    <div class="error-container" style="display: flex; justify-content: center; align-items: center; height: 100vh; flex-direction: column;">
                        <h2 style="color: #ef4444; margin-bottom: 1rem;">组件加载失败</h2>
                        <p style="color: #6b7280; margin-bottom: 2rem;">${error.message}</p>
                        <button onclick="location.reload()" style="padding: 0.5rem 1rem; background: #3b82f6; color: white; border: none; border-radius: 0.375rem; cursor: pointer;">
                            重新加载
                        </button>
                    </div>
                `;
            }
        });
    </script>
    
    <!-- 离线支持 -->
    <script>
    // 页面在线/离线状态检测
    function updateOnlineStatus() {
        const offlineBanner = document.getElementById('offline-banner');
        if (!offlineBanner) return; // 元素不存在则直接返回
        
        if (navigator.onLine) {
            offlineBanner.classList.remove('show');
        } else {
            offlineBanner.classList.add('show');
        }
    }
    
    window.addEventListener('online', updateOnlineStatus);
    window.addEventListener('offline', updateOnlineStatus);
    updateOnlineStatus(); // 初始化状态
    </script>
</body>

</html>