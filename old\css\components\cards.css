/**

 * 卡片组件样式

 */



.card {

  background-color: var(--color-bg, white);

  border-radius: var(--radius-xl);

  box-shadow: var(--shadow-md);

  border: 1px solid var(--color-border, var(--color-gray-200));

  overflow: hidden;

  transition: all var(--duration-normal) var(--ease-out);

}



.card:hover {

  box-shadow: var(--shadow-lg);

  transform: translateY(-2px);

}



.card-header {

  padding: var(--space-6);

  border-bottom: 1px solid var(--color-border-light, var(--color-gray-100));

}



.card-body {

  padding: var(--space-6);

}



.card-footer {

  padding: var(--space-6);

  border-top: 1px solid var(--color-border-light, var(--color-gray-100));

  background-color: var(--color-bg-secondary, var(--color-gray-50));

}



/* 卡片变体 */

.card-flat {

  box-shadow: none;

  border: 1px solid var(--color-border);

}



.card-elevated {

  box-shadow: var(--shadow-xl);

}



.card-elevated:hover {

  box-shadow: var(--shadow-2xl);

  transform: translateY(-4px);

}



/* 玻璃拟态卡片 */

.card-glass {

  background: rgba(255, 255, 255, 0.1);

  backdrop-filter: blur(10px);

  border: 1px solid rgba(255, 255, 255, 0.2);

  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);

}



.dark .card-glass {

  background: rgba(0, 0, 0, 0.2);

  border: 1px solid rgba(255, 255, 255, 0.1);

}



/* Memo卡片特殊样式 */

.memo-card {

  position: relative;

  background: var(--color-bg, white);

  border-radius: var(--radius-xl);

  padding: var(--space-6);

  margin-bottom: var(--space-4);

  box-shadow: var(--shadow-md);

  border: 1px solid var(--color-border, var(--color-gray-200));

  transition: all var(--duration-normal) var(--ease-out);

}



.memo-card:hover {

  box-shadow: var(--shadow-lg);

  transform: translateY(-1px);

}



.memo-card[style*="cursor: pointer"]:hover {

  box-shadow: var(--shadow-xl);

  transform: translateY(-2px);

  border-color: var(--color-primary);

}



.memo-card::before {

  content: '';

  position: absolute;

  top: 0;

  left: 0;

  right: 0;

  height: 3px;

  background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));

  border-radius: var(--radius-xl) var(--radius-xl) 0 0;

}



.memo-header {

  display: flex;

  align-items: center;

  justify-content: space-between;

  margin-bottom: var(--space-4);

}



.memo-author {

  display: flex;

  align-items: center;

  gap: var(--space-3);

}



.memo-avatar {

  width: 2.5rem;

  height: 2.5rem;

  border-radius: var(--radius-full);

  background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));

  display: flex;

  align-items: center;

  justify-content: center;

  color: white;

  font-weight: 600;

  font-size: var(--text-sm);

}



.memo-meta {

  display: flex;

  flex-direction: column;

}



.memo-username {

  font-weight: 600;

  color: var(--color-text, var(--color-gray-900));

  font-size: var(--text-sm);

}



.memo-time {

  font-size: var(--text-xs);

  color: var(--color-text-tertiary, var(--color-gray-500));

}



.memo-actions {

  display: flex;

  gap: var(--space-2);

  opacity: 0;

  transition: opacity var(--duration-fast) var(--ease-out);

}



.memo-card:hover .memo-actions {

  opacity: 1;

}



.memo-content {

  color: var(--color-text, var(--color-gray-900));

  line-height: 1.6;

  margin-bottom: var(--space-4);

}



.memo-content p {

  margin-bottom: var(--space-3);

}



.memo-content p:last-child {

  margin-bottom: 0;

}



.memo-content h1,

.memo-content h2,

.memo-content h3,

.memo-content h4,

.memo-content h5,

.memo-content h6 {

  margin-bottom: var(--space-2);

  font-weight: 600;

}



.memo-content code {

  background-color: var(--color-bg-secondary, var(--color-gray-100));

  padding: var(--space-1) var(--space-2);

  border-radius: var(--radius-sm);

  font-family: var(--font-mono);

  font-size: 0.875em;

}



.memo-content pre {

  background-color: var(--color-bg-secondary, var(--color-gray-100));

  padding: var(--space-4);

  border-radius: var(--radius-lg);

  overflow-x: auto;

  margin: var(--space-4) 0;

}



.memo-content pre code {

  background: none;

  padding: 0;

}



.memo-content blockquote {

  border-left: 4px solid var(--color-primary);

  padding-left: var(--space-4);

  margin: var(--space-4) 0;

  font-style: italic;

  color: var(--color-text-secondary, var(--color-gray-700));

}



.memo-media {

  margin: var(--space-4) 0;

}



.memo-image {

  width: 100%;

  border-radius: var(--radius-lg);

  cursor: zoom-in;

  transition: transform var(--duration-fast) var(--ease-out);

}



.memo-image:hover {

  transform: scale(1.02);

}



.memo-video {

  width: 100%;

  border-radius: var(--radius-lg);

}



.memo-footer {

  display: flex;

  align-items: center;

  justify-content: space-between;

  padding-top: var(--space-4);

  border-top: 1px solid var(--color-border-light, var(--color-gray-100));

}



.memo-status {

  display: inline-flex;

  align-items: center;

  gap: var(--space-1);

  padding: var(--space-1) var(--space-2);

  border-radius: var(--radius-full);

  font-size: var(--text-xs);

  font-weight: 500;

}



.memo-status.public {

  background-color: rgba(16, 185, 129, 0.1);

  color: var(--color-success);

}



.memo-status.private {

  background-color: rgba(107, 114, 128, 0.1);

  color: var(--color-gray-600);

}



.memo-tags {

  display: flex;

  gap: var(--space-2);

  flex-wrap: wrap;

}



.memo-tag {

  padding: var(--space-1) var(--space-2);

  background-color: var(--color-bg-secondary, var(--color-gray-100));

  color: var(--color-text-secondary, var(--color-gray-700));

  border-radius: var(--radius-full);

  font-size: var(--text-xs);

  font-weight: 500;

  transition: all var(--duration-fast) var(--ease-out);

}



.memo-tag:hover {

  background-color: var(--color-primary);

  color: white;

  cursor: pointer;

}



/* 响应式设计 */

@media (max-width: 640px) {

  .card {

    border-radius: var(--radius-lg);

    margin: 0 calc(-1 * var(--space-4));

  }

  

  .card-header,

  .card-body,

  .card-footer {

    padding: var(--space-4);

  }

  

  .memo-card {

    border-radius: var(--radius-lg);

    padding: var(--space-4);

    margin: 0 calc(-1 * var(--space-4)) var(--space-4);

  }

  

  .memo-header {

    flex-direction: column;

    align-items: flex-start;

    gap: var(--space-2);

  }

  

  .memo-actions {

    opacity: 1;

  }

  

  .memo-footer {

    flex-direction: column;

    align-items: flex-start;

    gap: var(--space-3);

  }

}