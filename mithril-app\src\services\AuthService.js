// 使用相对路径导入服务
import { ApiService } from './ApiService.js';

/**
 * 认证服务类，处理用户认证相关逻辑
 */
export class AuthService {
    constructor() {
        this.apiService = new ApiService();
        this.token = localStorage.getItem('authToken') || null;
        this.currentUser = JSON.parse(localStorage.getItem('currentUser') || 'null');
        this.isAuthenticated = !!this.token;
    }
    
    /**
     * 用户登录
     */
    async login(username, password) {
        try {
            // 调用API验证用户
            const response = await this.apiService.getUserByUsername(username);
            
            if (response && response.data && response.data.records && response.data.records.length > 0) {
                const userRecord = response.data.records[0];
                const userData = userRecord.fields;
                
                // 验证密码（注意：实际应用中应该使用加密密码）
                if (userData.密码 === password) {
                    const user = { 
                        id: userRecord.recordId,
                        username: userData.用户名,
                        nickname: userData.昵称 || userData.用户名,
                        phone: userData.手机号 || '',
                        avatar: userData.头像 || `https://ui-avatars.com/api/?name=${encodeURIComponent(userData.昵称 || userData.用户名)}&background=3B82F6&color=fff`,
                        token: 'vika-token-' + Date.now(),
                        loginTime: new Date().toISOString(),
                        vikaId: userRecord.recordId
                    };
                    
                    // 设置认证状态
                    this.token = user.token;
                    this.currentUser = user;
                    this.isAuthenticated = true;
                    
                    // 保存到localStorage
                    localStorage.setItem('authToken', this.token);
                    localStorage.setItem('currentUser', JSON.stringify(this.currentUser));
                    
                    return { success: true, user: this.currentUser };
                } else {
                    return { success: false, error: '密码错误' };
                }
            } else {
                return { success: false, error: '用户不存在' };
            }
        } catch (error) {
            console.error('登录失败:', error);
            return { success: false, error: '登录请求失败，请稍后重试' };
        }
    }
    
    /**
     * 用户登出
     */
    logout() {
        this.token = null;
        this.currentUser = null;
        this.isAuthenticated = false;
        
        // 清除localStorage
        localStorage.removeItem('authToken');
        localStorage.removeItem('currentUser');
        
        return { success: true };
    }
    
    /**
     * 检查登录状态
     */
    checkAuthStatus() {
        return this.isAuthenticated;
    }
    
    /**
     * 获取当前用户信息
     */
    getCurrentUser() {
        return this.currentUser;
    }
}