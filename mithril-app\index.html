<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>当记</title>
    <link rel="icon" type="image/svg+xml" href="favicon.svg">
    <link rel="stylesheet" href="src/styles/main.css">
    <link rel="stylesheet" href="src/styles/markdown.css">
    <link rel="stylesheet" href="src/styles/optimized.css">
    <link rel="stylesheet" href="src/modules/timeRecord/styles/timeRecord.css">
    <!-- 51.la 统计 -->
    <script charset="UTF-8" id="LA_COLLECT" src="//sdk.51.la/js-sdk-pro.min.js"></script>
    <script>LA.init({id:"Jqud4Iv4jX9nBtrP",ck:"Jqud4Iv4jX9nBtrP"})</script>
    <!-- 离线支持 -->
    <style>
        #offline-banner {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
            text-align: center;
            padding: 12px 16px;
            z-index: 9999;
            display: none;
            font-weight: 600;
            font-size: 14px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        #offline-banner.show {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            animation: slideDown 0.3s ease-out;
        }

        #offline-banner::before {
            content: '📡';
            font-size: 16px;
        }

        @keyframes slideDown {
            from {
                transform: translateY(-100%);
                opacity: 0;
            }

            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        /* 页面加载时的初始样式 */
        body {
            margin: 0;
            opacity: 0;
            animation: fadeIn 0.5s ease-out forwards;
        }

        @keyframes fadeIn {
            to {
                opacity: 1;
            }
        }
    </style>
</head>

<body>
    <!-- 离线状态横幅 -->
    <div id="offline-banner">
        网络连接已断开，部分功能可能无法使用
    </div>

    <!-- 主应用容器 -->
    <div id="app"></div>

    <script src="https://unpkg.com/mithril@2.2.2/mithril.min.js"></script>
    <script type="module" src="src/app.js"></script>

    <!-- 离线支持 -->
    <script>
        // 页面在线/离线状态检测
        function updateOnlineStatus() {
            const offlineBanner = document.getElementById('offline-banner');
            if (!offlineBanner) return; // 元素不存在则直接返回

            if (navigator.onLine) {
                offlineBanner.classList.remove('show');
            } else {
                offlineBanner.classList.add('show');
            }
        }

        window.addEventListener('online', updateOnlineStatus);
        window.addEventListener('offline', updateOnlineStatus);
        updateOnlineStatus(); // 初始化状态
    </script>
</body>

</html>