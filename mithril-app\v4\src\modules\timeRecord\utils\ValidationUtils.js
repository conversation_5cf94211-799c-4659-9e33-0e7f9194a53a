/**
 * 数据验证工具类
 * 需求: 6.5 - 用户输入无效数据时系统应显示验证错误信息
 * 任务: 7.3 - 实现数据验证
 */

import { TIME_RECORD_CONFIG } from '../../../config/timeRecordConfig.js';
// 移除国际化依赖，使用简单的中文文本

export class ValidationUtils {
    /**
     * 验证时间记录输入数据
     * @param {Object} data - 要验证的数据
     * @returns {Object} 验证结果 {isValid: boolean, errors: Array, warnings: Array}
     */
    static validateTimeRecordInput(data) {
        const errors = [];
        const warnings = [];

        if (!data || typeof data !== 'object') {
            errors.push({
                field: 'data',
                code: 'REQUIRED',
                message: '数据不能为空'
            });
            return { isValid: false, errors, warnings };
        }

        // 验证备注内容
        const contentValidation = this.validateContent(data.content);
        if (!contentValidation.isValid) {
            errors.push(...contentValidation.errors);
        }
        warnings.push(...contentValidation.warnings);

        // 验证分类
        const categoryValidation = this.validateCategory(data.category);
        if (!categoryValidation.isValid) {
            errors.push(...categoryValidation.errors);
        }
        warnings.push(...categoryValidation.warnings);

        // 验证时间数据
        const timeValidation = this.validateTimeData(data);
        if (!timeValidation.isValid) {
            errors.push(...timeValidation.errors);
        }
        warnings.push(...timeValidation.warnings);

        return {
            isValid: errors.length === 0,
            errors,
            warnings
        };
    }

    /**
     * 验证备注内容
     * @param {string} content - 备注内容
     * @returns {Object} 验证结果
     */
    static validateContent(content) {
        const errors = [];
        const warnings = [];

        // 检查是否为空
        if (!content || typeof content !== 'string') {
            errors.push({
                field: 'content',
                code: 'REQUIRED',
                message: '备注内容不能为空'
            });
            return { isValid: false, errors, warnings };
        }

        const trimmedContent = content.trim();

        // 检查空白内容
        if (trimmedContent.length === 0) {
            errors.push({
                field: 'content',
                code: 'EMPTY',
                message: '备注内容不能只包含空白字符'
            });
            return { isValid: false, errors, warnings };
        }

        // 检查最小长度
        if (trimmedContent.length < 2) {
            errors.push({
                field: 'content',
                code: 'TOO_SHORT',
                message: '备注内容至少需要2个字符'
            });
        }

        // 检查最大长度
        const maxLength = TIME_RECORD_CONFIG.VALIDATION.MAX_CONTENT_LENGTH;
        if (trimmedContent.length > maxLength) {
            errors.push({
                field: 'content',
                code: 'TOO_LONG',
                message: `备注内容不能超过${maxLength}个字符，当前${trimmedContent.length}个字符`
            });
        }

        // 检查是否接近长度限制（警告）
        if (trimmedContent.length > maxLength * 0.9) {
            warnings.push({
                field: 'content',
                code: 'NEAR_LIMIT',
                message: `备注内容接近字符限制 (${trimmedContent.length}/${maxLength})`
            });
        }

        // 检查特殊字符
        const invalidChars = this.findInvalidCharacters(trimmedContent);
        if (invalidChars.length > 0) {
            warnings.push({
                field: 'content',
                code: 'INVALID_CHARS',
                message: `包含可能有问题的字符: ${invalidChars.join(', ')}`
            });
        }

        // 检查内容质量（警告）
        const qualityCheck = this.checkContentQuality(trimmedContent);
        warnings.push(...qualityCheck);

        return {
            isValid: errors.length === 0,
            errors,
            warnings
        };
    }

    /**
     * 验证分类
     * @param {string} category - 分类名称
     * @returns {Object} 验证结果
     */
    static validateCategory(category) {
        const errors = [];
        const warnings = [];

        // 分类是可选的
        if (!category) {
            return { isValid: true, errors, warnings };
        }

        if (typeof category !== 'string') {
            errors.push({
                field: 'category',
                code: 'INVALID_TYPE',
                message: '分类必须是文本类型'
            });
            return { isValid: false, errors, warnings };
        }

        const trimmedCategory = category.trim();

        // 检查长度
        const maxLength = TIME_RECORD_CONFIG.VALIDATION.MAX_CATEGORY_LENGTH;
        if (trimmedCategory.length > maxLength) {
            errors.push({
                field: 'category',
                code: 'TOO_LONG',
                message: `分类名称不能超过${maxLength}个字符，当前${trimmedCategory.length}个字符`
            });
        }

        // 检查特殊字符
        if (!/^[\u4e00-\u9fa5a-zA-Z0-9\s\-_]+$/.test(trimmedCategory)) {
            warnings.push({
                field: 'category',
                code: 'SPECIAL_CHARS',
                message: '分类名称包含特殊字符，建议只使用中文、英文、数字和常用符号'
            });
        }

        return {
            isValid: errors.length === 0,
            errors,
            warnings
        };
    }

    /**
     * 验证时间数据
     * @param {Object} data - 包含时间信息的数据
     * @returns {Object} 验证结果
     */
    static validateTimeData(data) {
        const errors = [];
        const warnings = [];

        // 验证开始时间
        if (data.startTime) {
            if (!(data.startTime instanceof Date) || isNaN(data.startTime.getTime())) {
                errors.push({
                    field: 'startTime',
                    code: 'INVALID_DATE',
                    message: '开始时间必须是有效的日期'
                });
            } else {
                // 检查时间是否合理
                const now = new Date();
                const oneYearAgo = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate());
                const oneHourFromNow = new Date(now.getTime() + 60 * 60 * 1000);

                if (data.startTime < oneYearAgo) {
                    warnings.push({
                        field: 'startTime',
                        code: 'TOO_OLD',
                        message: '开始时间超过一年前，请确认是否正确'
                    });
                }

                if (data.startTime > oneHourFromNow) {
                    errors.push({
                        field: 'startTime',
                        code: 'FUTURE_TIME',
                        message: '开始时间不能是未来时间'
                    });
                }
            }
        }

        // 验证结束时间
        if (data.endTime) {
            if (!(data.endTime instanceof Date) || isNaN(data.endTime.getTime())) {
                errors.push({
                    field: 'endTime',
                    code: 'INVALID_DATE',
                    message: '结束时间必须是有效的日期'
                });
            } else {
                // 检查时间是否合理
                const now = new Date();
                const oneHourFromNow = new Date(now.getTime() + 60 * 60 * 1000);

                if (data.endTime > oneHourFromNow) {
                    errors.push({
                        field: 'endTime',
                        code: 'FUTURE_TIME',
                        message: '结束时间不能是未来时间'
                    });
                }
            }
        }

        // 验证时间逻辑关系
        if (data.startTime && data.endTime &&
            data.startTime instanceof Date && data.endTime instanceof Date &&
            !isNaN(data.startTime.getTime()) && !isNaN(data.endTime.getTime())) {

            if (data.endTime <= data.startTime) {
                errors.push({
                    field: 'timeRange',
                    code: 'INVALID_RANGE',
                    message: '结束时间必须晚于开始时间'
                });
            } else {
                // 检查持续时间
                const duration = Math.floor((data.endTime.getTime() - data.startTime.getTime()) / 1000);
                const minDuration = TIME_RECORD_CONFIG.VALIDATION.MIN_DURATION;

                if (duration < minDuration) {
                    errors.push({
                        field: 'duration',
                        code: 'TOO_SHORT',
                        message: `记录时长至少为${minDuration}秒，当前${duration}秒`
                    });
                }

                // 检查是否过长（警告）
                const maxReasonableDuration = 12 * 60 * 60; // 12小时
                if (duration > maxReasonableDuration) {
                    warnings.push({
                        field: 'duration',
                        code: 'TOO_LONG',
                        message: `记录时长超过12小时，请确认是否正确 (${this.formatDuration(duration)})`
                    });
                }
            }
        }

        // 验证持续时间字段
        if (data.duration !== undefined) {
            if (typeof data.duration !== 'number' || data.duration < 0) {
                errors.push({
                    field: 'duration',
                    code: 'INVALID_NUMBER',
                    message: '持续时间必须是非负数'
                });
            }
        }

        return {
            isValid: errors.length === 0,
            errors,
            warnings
        };
    }

    /**
     * 验证API响应数据
     * @param {Object} response - API响应数据
     * @param {string} expectedType - 期望的响应类型
     * @returns {Object} 验证结果
     */
    static validateApiResponse(response, expectedType = 'generic') {
        const errors = [];
        const warnings = [];

        // 基础结构验证
        if (!response || typeof response !== 'object') {
            errors.push({
                field: 'response',
                code: 'INVALID_STRUCTURE',
                message: 'API响应数据结构无效'
            });
            return { isValid: false, errors, warnings };
        }

        // 检查成功标志
        if (response.success === undefined) {
            warnings.push({
                field: 'success',
                code: 'MISSING_SUCCESS_FLAG',
                message: 'API响应缺少成功标志'
            });
        } else if (typeof response.success !== 'boolean') {
            errors.push({
                field: 'success',
                code: 'INVALID_SUCCESS_TYPE',
                message: '成功标志必须是布尔值'
            });
        }

        // 根据响应类型进行特定验证
        switch (expectedType) {
            case 'records_list':
                this.validateRecordsListResponse(response, errors, warnings);
                break;
            case 'record_create':
                this.validateRecordCreateResponse(response, errors, warnings);
                break;
            case 'record_update':
                this.validateRecordUpdateResponse(response, errors, warnings);
                break;
            case 'record_delete':
                this.validateRecordDeleteResponse(response, errors, warnings);
                break;
        }

        return {
            isValid: errors.length === 0,
            errors,
            warnings
        };
    }

    /**
     * 验证记录列表响应
     */
    static validateRecordsListResponse(response, errors, warnings) {
        if (!response.data) {
            errors.push({
                field: 'data',
                code: 'MISSING_DATA',
                message: '响应缺少数据字段'
            });
            return;
        }

        const { data } = response;

        // 验证记录数组
        if (!Array.isArray(data.records)) {
            errors.push({
                field: 'records',
                code: 'INVALID_RECORDS_TYPE',
                message: '记录列表必须是数组'
            });
        } else {
            // 验证每条记录
            data.records.forEach((record, index) => {
                const recordValidation = this.validateRecordStructure(record);
                if (!recordValidation.isValid) {
                    recordValidation.errors.forEach(error => {
                        errors.push({
                            ...error,
                            field: `records[${index}].${error.field}`,
                            message: `第${index + 1}条记录: ${error.message}`
                        });
                    });
                }
            });
        }

        // 验证分页信息
        if (data.total !== undefined && (typeof data.total !== 'number' || data.total < 0)) {
            warnings.push({
                field: 'total',
                code: 'INVALID_TOTAL',
                message: '总数必须是非负整数'
            });
        }

        if (data.pageNum !== undefined && (typeof data.pageNum !== 'number' || data.pageNum < 1)) {
            warnings.push({
                field: 'pageNum',
                code: 'INVALID_PAGE_NUM',
                message: '页码必须是正整数'
            });
        }
    }

    /**
     * 验证记录创建响应
     */
    static validateRecordCreateResponse(response, errors, warnings) {
        if (!response.data) {
            errors.push({
                field: 'data',
                code: 'MISSING_DATA',
                message: '创建响应缺少数据字段'
            });
            return;
        }

        // 验证返回的记录ID（支持id或recordId字段）
        if (!response.data.recordId && !response.data.id) {
            errors.push({
                field: 'recordId',
                code: 'MISSING_RECORD_ID',
                message: '创建响应缺少记录ID'
            });
        }
    }

    /**
     * 验证记录结构
     */
    static validateRecordStructure(record) {
        const errors = [];
        const warnings = [];

        if (!record || typeof record !== 'object') {
            errors.push({
                field: 'record',
                code: 'INVALID_STRUCTURE',
                message: '记录结构无效'
            });
            return { isValid: false, errors, warnings };
        }

        // 验证必需字段 - 修复：vika API返回的是recordId而不是id
        if (!record.recordId && !record.id) {
            errors.push({
                field: 'recordId',
                code: 'MISSING_REQUIRED',
                message: '缺少必需字段: recordId'
            });
        }

        // 验证fields字段存在
        if (!record.fields || typeof record.fields !== 'object') {
            errors.push({
                field: 'fields',
                code: 'MISSING_REQUIRED',
                message: '缺少必需字段: fields'
            });
        }

        // 验证时间字段
        const timeFields = ['startTime', 'endTime', 'createdAt'];
        timeFields.forEach(field => {
            if (record[field] && !(record[field] instanceof Date) && typeof record[field] !== 'string') {
                warnings.push({
                    field,
                    code: 'INVALID_TIME_FORMAT',
                    message: `时间字段格式可能不正确: ${field}`
                });
            }
        });

        return {
            isValid: errors.length === 0,
            errors,
            warnings
        };
    }

    /**
     * 查找无效字符
     */
    static findInvalidCharacters(text) {
        const invalidChars = [];
        const dangerousPatterns = [
            /[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, // 控制字符
            /<script[^>]*>.*?<\/script>/gi,        // 脚本标签
            /javascript:/gi,                       // JavaScript协议
            /on\w+\s*=/gi                         // 事件处理器
        ];

        dangerousPatterns.forEach(pattern => {
            const matches = text.match(pattern);
            if (matches) {
                invalidChars.push(...matches);
            }
        });

        return [...new Set(invalidChars)]; // 去重
    }

    /**
     * 检查内容质量
     */
    static checkContentQuality(content) {
        const warnings = [];

        // 检查是否只有重复字符
        if (/^(.)\1{4,}$/.test(content)) {
            warnings.push({
                field: 'content',
                code: 'REPETITIVE',
                message: '内容过于重复，建议添加更多描述'
            });
        }

        // 检查是否过于简短
        if (content.length < 5) {
            warnings.push({
                field: 'content',
                code: 'TOO_BRIEF',
                message: '内容较为简短，建议添加更多工作细节'
            });
        }

        // 检查是否包含有意义的内容
        if (/^[^\u4e00-\u9fa5a-zA-Z0-9]*$/.test(content)) {
            warnings.push({
                field: 'content',
                code: 'NO_MEANINGFUL_CONTENT',
                message: '内容似乎没有包含有意义的文字'
            });
        }

        return warnings;
    }

    /**
     * 格式化持续时间
     */
    static formatDuration(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;

        const parts = [];
        if (hours > 0) parts.push(`${hours}小时`);
        if (minutes > 0) parts.push(`${minutes}分钟`);
        if (secs > 0 || parts.length === 0) parts.push(`${secs}秒`);

        return parts.join('');
    }

    /**
     * 创建用户友好的验证错误消息
     * @param {Array} errors - 错误数组
     * @param {Array} warnings - 警告数组
     * @returns {Object} 格式化的消息
     */
    static formatValidationMessages(errors, warnings) {
        const result = {
            hasErrors: errors.length > 0,
            hasWarnings: warnings.length > 0,
            errorMessage: '',
            warningMessage: '',
            suggestions: []
        };

        if (errors.length > 0) {
            result.errorMessage = errors.length === 1
                ? errors[0].message
                : `发现${errors.length}个错误：\n${errors.map(e => `• ${e.message}`).join('\n')}`;

            // 添加修复建议
            result.suggestions = this.generateFixSuggestions(errors);
        }

        if (warnings.length > 0) {
            result.warningMessage = warnings.length === 1
                ? warnings[0].message
                : `${warnings.length}个提醒：\n${warnings.map(w => `• ${w.message}`).join('\n')}`;
        }

        return result;
    }

    /**
     * 生成修复建议
     */
    static generateFixSuggestions(errors) {
        const suggestions = [];

        errors.forEach(error => {
            switch (error.code) {
                case 'REQUIRED':
                case 'EMPTY':
                    suggestions.push('请填写必要的内容');
                    break;
                case 'TOO_SHORT':
                    suggestions.push('请添加更多详细描述');
                    break;
                case 'TOO_LONG':
                    suggestions.push('请精简内容长度');
                    break;
                case 'INVALID_DATE':
                    suggestions.push('请检查时间格式是否正确');
                    break;
                case 'FUTURE_TIME':
                    suggestions.push('请选择当前时间之前的时间');
                    break;
                case 'INVALID_RANGE':
                    suggestions.push('请确保结束时间晚于开始时间');
                    break;
            }
        });

        return [...new Set(suggestions)]; // 去重
    }
}

// 导出验证相关的常量和工具函数
export const VALIDATION_CODES = {
    // 通用错误码
    REQUIRED: 'REQUIRED',
    INVALID_TYPE: 'INVALID_TYPE',
    INVALID_FORMAT: 'INVALID_FORMAT',

    // 内容相关
    EMPTY: 'EMPTY',
    TOO_SHORT: 'TOO_SHORT',
    TOO_LONG: 'TOO_LONG',
    INVALID_CHARS: 'INVALID_CHARS',

    // 时间相关
    INVALID_DATE: 'INVALID_DATE',
    FUTURE_TIME: 'FUTURE_TIME',
    INVALID_RANGE: 'INVALID_RANGE',

    // API响应相关
    INVALID_STRUCTURE: 'INVALID_STRUCTURE',
    MISSING_DATA: 'MISSING_DATA',
    MISSING_REQUIRED: 'MISSING_REQUIRED'
};

// 创建全局验证工具实例
export const globalValidation = ValidationUtils;