/**
 * 性能监控工具
 * 用于监控和记录应用性能指标
 */

export class PerformanceMonitor {
    constructor() {
        this.metrics = new Map();
        this.startTime = new Map();
    }

    /**
     * 开始性能监控
     * @param {string} name - 监控项名称
     */
    start(name) {
        this.startTime.set(name, performance.now());
    }

    /**
     * 结束性能监控并记录时间
     * @param {string} name - 监控项名称
     */
    end(name) {
        if (!this.startTime.has(name)) return;
        
        const start = this.startTime.get(name);
        const end = performance.now();
        const duration = end - start;
        
        this.metrics.set(name, duration);
        console.log(`${name} 耗时: ${duration.toFixed(2)}ms`);
        
        // 清除开始时间
        this.startTime.delete(name);
    }

    /**
     * 获取性能指标
     * @param {string} name - 监控项名称
     * @returns {number|null} 性能指标
     */
    getMetric(name) {
        return this.metrics.get(name) || null;
    }

    /**
     * 获取所有性能指标
     * @returns {Map} 所有性能指标
     */
    getAllMetrics() {
        return new Map(this.metrics);
    }

    /**
     * 清除所有性能指标
     */
    clear() {
        this.metrics.clear();
        this.startTime.clear();
    }
}

// 创建全局实例
export const performanceMonitor = new PerformanceMonitor();