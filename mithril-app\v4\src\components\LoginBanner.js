// 使用全局的 Mithril 实例而不是导入
const m = window.m;

export const LoginBanner = {
    view: function(vnode) {
        // 只有在未登录时才显示横幅
        if (window.auth.isAuthenticated) {
            return null;
        }
        
        return m('.login-banner', [
            m('span', '您尚未登录，登录后可发布 memo'),
            m('button.btn.btn-primary', {
                onclick: () => window.appState.toggleLoginModal(true)
            }, '立即登录')
        ]);
    }
};