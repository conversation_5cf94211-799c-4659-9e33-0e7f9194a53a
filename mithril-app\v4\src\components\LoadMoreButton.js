// 使用全局的 Mithril 实例而不是导入
const m = window.m;

export const LoadMoreButton = {
    oninit: function(vnode) {
        this.autoLoadEnabled = true;
        this.observer = null;
        this.loadMoreRef = null;
    },
    
    oncreate: function(vnode) {
        // 设置 Intersection Observer 实现自动加载
        if ('IntersectionObserver' in window && this.autoLoadEnabled) {
            this.observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting && window.memos.hasMore && !window.memos.loading) {
                        // 当按钮进入视口时自动加载更多
                        window.memos.loadMore();
                    }
                });
            }, {
                rootMargin: '100px' // 提前100px开始加载
            });
            
            if (this.loadMoreRef) {
                this.observer.observe(this.loadMoreRef);
            }
        }
    },
    
    onremove: function(vnode) {
        // 清理 Observer
        if (this.observer) {
            this.observer.disconnect();
        }
    },
    
    view: function(vnode) {
        if (window.memos.list.length === 0) {
            return null;
        }
        
        return m('.load-more-container', {
            oncreate: (vnode) => {
                this.loadMoreRef = vnode.dom;
                // 如果 Observer 已经创建，开始观察
                if (this.observer) {
                    this.observer.observe(this.loadMoreRef);
                }
            }
        }, [
            window.memos.hasMore ? [
                // 手动加载按钮
                m('button.btn.btn-secondary', {
                    onclick: () => {
                        window.memos.loadMore();
                    },
                    disabled: window.memos.loading,
                    style: { marginBottom: '8px' }
                }, window.memos.loading ? '加载中...' : '加载更多'),
                
                // 自动加载开关
                m('div', {
                    style: { 
                        fontSize: '12px', 
                        color: 'var(--muted)', 
                        textAlign: 'center',
                        marginTop: '8px'
                    }
                }, [
                    m('label', {
                        style: { cursor: 'pointer', display: 'flex', alignItems: 'center', justifyContent: 'center', gap: '4px' }
                    }, [
                        m('input[type=checkbox]', {
                            checked: this.autoLoadEnabled,
                            onchange: (e) => {
                                this.autoLoadEnabled = e.target.checked;
                                if (this.autoLoadEnabled && this.observer && this.loadMoreRef) {
                                    this.observer.observe(this.loadMoreRef);
                                } else if (this.observer) {
                                    this.observer.disconnect();
                                }
                            }
                        }),
                        '自动加载'
                    ])
                ])
            ] : 
                m('.no-more', '没有更多了')
        ]);
    }
};