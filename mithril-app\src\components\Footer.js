// 使用全局的 Mithril 实例而不是导入
const m = window.m;

export const Footer = {
    view: function(vnode) {
        return m('footer.footer', [
            m('.container', [
                m('p', '© 2025 当记 - 记录美好时光 ✨'),
                m('p', [
                    '用心打造 · ',
                    m('a', {
                        href: '#',
                        onclick: (e) => {
                            e.preventDefault();
                            window.appState.toggleUpdateLogModal(true);
                        }
                    }, '更新日志'),
                    ' · 持续改进'
                ])
            ])
        ]);
    }
};