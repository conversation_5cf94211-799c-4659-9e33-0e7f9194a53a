/**
 * 工具函数模块
 * 提供日期格式化、加载状态管理、防抖、API请求等通用功能
 */

import { API_CONFIG, APP_CONFIG } from '../config.js';

/**
 * 格式化日期
 * @param {string} dateString - 日期字符串
 * @returns {string} 格式化后的日期
 */
export function formatDate(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diff = now - date;
    
    if (diff < 60000) {
        return '刚刚';
    } else if (diff < 3600000) {
        return `${Math.floor(diff / 60000)}分钟前`;
    } else if (diff < 86400000) {
        return `${Math.floor(diff / 3600000)}小时前`;
    } else if (diff < 2592000000) {
        return `${Math.floor(diff / 86400000)}天前`;
    } else {
        return date.toLocaleDateString('zh-CN');
    }
}

/**
 * 显示加载状态
 */
export function showLoading() {
    const loadingEl = document.getElementById('loading');
    if (loadingEl) {
        loadingEl.style.display = 'block';
    }
}

/**
 * 隐藏加载状态
 */
export function hideLoading() {
    const loadingEl = document.getElementById('loading');
    if (loadingEl) {
        loadingEl.style.display = 'none';
    }
}

/**
 * 防抖函数
 * @param {Function} func - 需要防抖的函数
 * @param {number} wait - 等待时间（毫秒）
 * @returns {Function} 防抖后的函数
 */
export function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// API 请求队列和限流控制
const apiQueue = [];
let isProcessingQueue = false;
const requestDelay = 1000; // 增加请求间隔到 1000ms (1秒)
const maxRetries = 5; // 增加最大重试次数到 5
const maxConcurrentRequests = 1; // 限制并发请求数为 1

// 用户信息缓存
const userCache = new Map();
const userCacheExpiry = 5 * 60 * 1000; // 5分钟过期

/**
 * 延迟函数
 * @param {number} ms - 延迟毫秒数
 */
function delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 获取缓存的用户信息
 * @param {string} userId - 用户ID
 * @returns {Object|null} 用户信息或null
 */
export function getCachedUser(userId) {
    const cached = userCache.get(userId);
    if (cached && Date.now() - cached.timestamp < userCacheExpiry) {
        return cached.data;
    }
    return null;
}

/**
 * 缓存用户信息
 * @param {string} userId - 用户ID
 * @param {Object} userData - 用户数据
 */
export function setCachedUser(userId, userData) {
    userCache.set(userId, {
        data: userData,
        timestamp: Date.now()
    });
}

/**
 * 清理过期的用户缓存
 */
export function cleanExpiredUserCache() {
    const now = Date.now();
    for (const [userId, cached] of userCache.entries()) {
        if (now - cached.timestamp > userCacheExpiry) {
            userCache.delete(userId);
        }
    }
}

/**
 * 处理 API 请求队列
 */
async function processApiQueue() {
    if (isProcessingQueue || apiQueue.length === 0) return;
    
    isProcessingQueue = true;
    
    while (apiQueue.length > 0) {
        const request = apiQueue.shift();
        const { resolve, reject, url, config } = request;
        
        // 添加重试计数
        if (!request.retryCount) request.retryCount = 0;
        
        try {
            // 请求间隔控制
            await delay(requestDelay);
            
            const response = await fetch(url, config);
            
            if (response.status === 429) {
                // 遇到限流，指数退避重试
                if (request.retryCount < maxRetries) {
                    request.retryCount++;
                    const backoffDelay = Math.min(1000 * Math.pow(2, request.retryCount), 10000); // 最大10秒
                    
                    console.warn(`API限流，${backoffDelay}ms后重试 (${request.retryCount}/${maxRetries}):`, url);
                    
                    // 延迟后重新加入队列
                    setTimeout(() => {
                        apiQueue.unshift(request);
                        if (!isProcessingQueue) processApiQueue();
                    }, backoffDelay);
                    
                    continue;
                } else {
                    throw new Error(`API请求失败，已达到最大重试次数: ${response.status}`);
                }
            }
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            console.log('API响应:', data);
            resolve(data);
            
        } catch (error) {
            console.error('API请求失败:', error);
            reject(error);
        }
        
        // 请求间隔控制
        if (apiQueue.length > 0) {
            await delay(requestDelay);
        }
    }
    
    isProcessingQueue = false;
}

/**
 * 发送API请求（带限流和重试）
 * @param {string} endpoint - API端点
 * @param {Object} options - 请求选项
 * @returns {Promise} API响应
 */
export async function fetchAPI(endpoint, options = {}) {
    const url = `${API_CONFIG.baseURL}${endpoint}`;
    
    const config = {
        headers: {
            'Authorization': `Bearer ${API_CONFIG.token}`,
            'Content-Type': 'application/json',
            ...options.headers
        },
        ...options
    };

    console.log('API请求:', url);

    return new Promise((resolve, reject) => {
        // 添加到请求队列
        apiQueue.push({ resolve, reject, url, config });
        
        // 开始处理队列
        processApiQueue();
    });
}

/**
 * 带重试的 API 请求
 * @param {string} endpoint - API端点
 * @param {Object} options - 请求选项
 * @param {number} retries - 重试次数
 * @returns {Promise} API响应
 */
export async function fetchAPIWithRetry(endpoint, options = {}, retries = maxRetries) {
    try {
        return await fetchAPI(endpoint, options);
    } catch (error) {
        if (error.message.includes('429') && retries > 0) {
            console.warn(`API限流，${retries} 次重试剩余，等待后重试...`);
            await delay(1000 * (maxRetries - retries + 1)); // 递增延迟
            return fetchAPIWithRetry(endpoint, options, retries - 1);
        }
        throw error;
    }
}

/**
 * 获取DOM元素
 * @param {string} selector - CSS选择器
 * @param {HTMLElement} parent - 父元素（可选）
 * @returns {HTMLElement} DOM元素
 */
export function getElement(selector, parent = document) {
    return parent.querySelector(selector);
}

/**
 * 获取所有匹配的DOM元素
 * @param {string} selector - CSS选择器
 * @param {HTMLElement} parent - 父元素（可选）
 * @returns {NodeList} DOM元素列表
 */
export function getElements(selector, parent = document) {
    return parent.querySelectorAll(selector);
}

/**
 * 节流函数
 * @param {Function} func - 需要节流的函数
 * @param {number} limit - 时间间隔（毫秒）
 * @returns {Function} 节流后的函数
 */
export function throttle(func, limit) {
    let inThrottle;
    return function executedFunction(...args) {
        if (!inThrottle) {
            func.apply(this, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

/**
 * 深拷贝对象
 * @param {any} obj - 要拷贝的对象
 * @returns {any} 拷贝后的对象
 */
export function deepClone(obj) {
    if (obj === null || typeof obj !== 'object') return obj;
    if (obj instanceof Date) return new Date(obj.getTime());
    if (obj instanceof Array) return obj.map(item => deepClone(item));
    if (typeof obj === 'object') {
        const clonedObj = {};
        for (const key in obj) {
            if (obj.hasOwnProperty(key)) {
                clonedObj[key] = deepClone(obj[key]);
            }
        }
        return clonedObj;
    }
}

/**
 * 生成唯一ID
 * @param {string} prefix - ID前缀
 * @returns {string} 唯一ID
 */
export function generateId(prefix = 'id') {
    return `${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * 格式化文件大小
 * @param {number} bytes - 字节数
 * @returns {string} 格式化后的文件大小
 */
export function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 检查是否为移动设备
 * @returns {boolean} 是否为移动设备
 */
export function isMobile() {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
}

/**
 * 检查是否支持WebP格式
 * @returns {Promise<boolean>} 是否支持WebP
 */
export function supportsWebP() {
    return new Promise((resolve) => {
        const webP = new Image();
        webP.onload = webP.onerror = () => {
            resolve(webP.height === 2);
        };
        webP.src = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA';
    });
}

/**
 * 复制文本到剪贴板
 * @param {string} text - 要复制的文本
 * @returns {Promise<boolean>} 是否复制成功
 */
export async function copyToClipboard(text) {
    try {
        if (navigator.clipboard && window.isSecureContext) {
            await navigator.clipboard.writeText(text);
            return true;
        } else {
            // 降级方案
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            const result = document.execCommand('copy');
            document.body.removeChild(textArea);
            return result;
        }
    } catch (error) {
        console.error('复制失败:', error);
        return false;
    }
}

/**
 * 滚动到指定元素
 * @param {HTMLElement|string} element - 目标元素或选择器
 * @param {Object} options - 滚动选项
 */
export function scrollToElement(element, options = {}) {
    const target = typeof element === 'string' ? getElement(element) : element;
    if (!target) return;

    const defaultOptions = {
        behavior: 'smooth',
        block: 'start',
        inline: 'nearest'
    };

    target.scrollIntoView({ ...defaultOptions, ...options });
}

/**
 * 创建加载指示器
 * @param {HTMLElement} container - 容器元素
 * @param {string} message - 加载消息
 * @returns {HTMLElement} 加载指示器元素
 */
export function createLoadingIndicator(container, message = '加载中...') {
    const loading = document.createElement('div');
    loading.className = 'loading-container';
    loading.innerHTML = `
        <div class="loading-spinner"></div>
        <div class="loading-text">${message}</div>
    `;
    
    if (container) {
        container.appendChild(loading);
    }
    
    return loading;
}

/**
 * 移除加载指示器
 * @param {HTMLElement} loading - 加载指示器元素
 */
export function removeLoadingIndicator(loading) {
    if (loading && loading.parentNode) {
        loading.parentNode.removeChild(loading);
    }
}

/**
 * 显示通知消息
 * @param {string} message - 消息内容
 * @param {string} type - 消息类型 (success, error, warning, info)
 * @param {number} duration - 显示时长（毫秒）
 */
export function showNotification(message, type = 'info', duration = 3000) {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <span class="notification-message">${message}</span>
            <button class="notification-close" onclick="this.parentElement.parentElement.remove()">×</button>
        </div>
    `;
    
    // 添加样式
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 10000;
        padding: 12px 16px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        max-width: 400px;
        animation: slideInRight 0.3s ease-out;
    `;
    
    // 根据类型设置颜色
    const colors = {
        success: { bg: '#10B981', text: 'white' },
        error: { bg: '#EF4444', text: 'white' },
        warning: { bg: '#F59E0B', text: 'white' },
        info: { bg: '#3B82F6', text: 'white' }
    };
    
    const color = colors[type] || colors.info;
    notification.style.backgroundColor = color.bg;
    notification.style.color = color.text;
    
    document.body.appendChild(notification);
    
    // 自动移除
    if (duration > 0) {
        setTimeout(() => {
            if (notification.parentNode) {
                notification.style.animation = 'slideOutRight 0.3s ease-in';
                setTimeout(() => notification.remove(), 300);
            }
        }, duration);
    }
}

/**
 * 验证邮箱格式
 * @param {string} email - 邮箱地址
 * @returns {boolean} 是否为有效邮箱
 */
export function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * 验证URL格式
 * @param {string} url - URL地址
 * @returns {boolean} 是否为有效URL
 */
export function isValidUrl(url) {
    try {
        new URL(url);
        return true;
    } catch {
        return false;
    }
}

/**
 * 转义HTML字符
 * @param {string} text - 要转义的文本
 * @returns {string} 转义后的文本
 */
export function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

/**
 * 解析HTML字符
 * @param {string} html - 要解析的HTML
 * @returns {string} 解析后的文本
 */
export function unescapeHtml(html) {
    const div = document.createElement('div');
    div.innerHTML = html;
    return div.textContent || div.innerText || '';
}