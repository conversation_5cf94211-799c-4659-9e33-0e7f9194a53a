/**
 * API模块
 * 处理所有与后端API的交互，包括备忘录的CRUD操作
 */

import { fetchAPI } from './utils.js';

/**
 * API服务类
 * 统一管理所有后端API调用
 */
export class APIService {
    /**
     * 获取备忘录列表
     * @param {number} page - 页码
     * @param {number} pageSize - 每页数量
     * @returns {Promise} 备忘录列表数据
     */
    static async getMemos(page = 1, pageSize = 10) {
        const params = new URLSearchParams({
            viewId: 'viwYoodJk54Xt',
            fieldKey: 'name',
            pageSize: pageSize.toString(),
            // 添加这个参数来获取关联字段的详细信息
            cellFormat: 'json'
        });

        if (page > 1) {
            // Vika API使用pageToken进行分页，这里简化处理
            params.append('pageNum', page.toString());
        }

        return fetchAPI(`/datasheets/dstCo3t04QBY3RweuR/records?${params}`);
    }

    /**
     * 创建新的备忘录
     * @param {Object} memoData - 备忘录数据
     * @returns {Promise} 创建结果
     */
    static async createMemo(memoData) {
        const now = new Date().toISOString();
        
        // 处理图片URL - 如果有多个图片，用逗号分隔
        const picurls = memoData.images && memoData.images.length > 0 
            ? memoData.images.join(',') 
            : '';
        
        return fetchAPI('/datasheets/dstCo3t04QBY3RweuR/records', {
            method: 'POST',
            body: JSON.stringify({
                records: [{
                    fields: {
                        content: memoData.content,
                        picurls: picurls, // 使用 picurls 字段存储图片URL
                        tags: memoData.tags || [],
                        user_id: memoData.userId ? [memoData.userId] : null, // 关联字段需要数组格式
                        status: memoData.status || '公开', // 添加状态字段
                        posttime: memoData.posttime || now, // 添加发布时间字段
                        createdAt: now // 创建时间
                    }
                }]
            })
        });
    }

    /**
     * 更新备忘录
     * @param {string} recordId - 记录ID
     * @param {Object} memoData - 更新数据
     * @returns {Promise} 更新结果
     */
    static async updateMemo(recordId, memoData) {
        // 处理图片URL - 如果有多个图片，用逗号分隔
        const picurls = memoData.images && memoData.images.length > 0 
            ? memoData.images.join(',') 
            : '';
            
        return fetchAPI(`/datasheets/dstCo3t04QBY3RweuR/records/${recordId}`, {
            method: 'PATCH',
            body: JSON.stringify({
                fields: {
                    content: memoData.content,
                    picurls: picurls, // 使用 picurls 字段存储图片URL
                    tags: memoData.tags || [],
                    updatedAt: new Date().toISOString()
                }
            })
        });
    }

    /**
     * 删除备忘录
     * @param {string} recordId - 记录ID
     * @returns {Promise} 删除结果
     */
    static async deleteMemo(recordId) {
        return fetchAPI(`/datasheets/dstCo3t04QBY3RweuR/records/${recordId}`, {
            method: 'DELETE'
        });
    }

    /**
     * 上传图片或视频文件
     * @param {File} file - 要上传的文件(图片或视频)
     * @param {Object} [options] - 上传选项
     * @param {string} [options.token] - 验证令牌(默认使用系统token)
     * @param {string} [options.format] - 响应格式(string/url_only)
     * @returns {Promise} 上传结果
     */
    static async uploadImage(file, options = {}) {
        const formData = new FormData();
        formData.append('image', file);
        formData.append('token', options.token || '8e7057ee0ba0be565301980fb3e52763');
        
        if (options.format) {
            formData.append('format', options.format);
        }

        return fetch('https://www.junwei.bid:89/web/13/index.php', {
            method: 'POST',
            body: formData
        }).then(response => {
            if (!response.ok) {
                throw new Error(`上传失败: ${response.statusText}`);
            }
            return response.json();
        }).then(data => {
            if (data.code !== 200) {
                throw new Error(`上传错误: ${data.message}`);
            }
            return options.format === 'url_only' 
                ? data.data.url 
                : data;
        });
    }

    /**
     * 搜索备忘录
     * @param {string} keyword - 搜索关键词
     * @param {number} page - 页码
     * @param {number} pageSize - 每页数量
     * @returns {Promise} 搜索结果
     */
    static async searchMemos(keyword, page = 1, pageSize = 10) {
        const params = new URLSearchParams({
            viewId: 'viwYoodJk54Xt',
            fieldKey: 'name',
            filterByFormula: `SEARCH("${keyword}", {content})`,
            pageSize: pageSize.toString()
        });

        return fetchAPI(`/datasheets/dstCo3t04QBY3RweuR/records?${params}`);
    }

    /**
     * 获取用户列表
     * @param {number} pageSize - 每页数量
     * @returns {Promise} 用户列表数据
     */
    static async getUsers(pageSize = 100) {
        const params = new URLSearchParams({
            viewId: 'viwHzmgPteBwK',
            fieldKey: 'name',
            pageSize: pageSize.toString()
        });

        return fetchAPI(`/datasheets/dst13vlcxlMi1EanCl/records?${params}`);
    }

    /**
     * 根据用户名查找用户
     * @param {string} username - 用户名
     * @returns {Promise} 用户信息
     */
    static async getUserByUsername(username) {
        const params = new URLSearchParams({
            viewId: 'viwHzmgPteBwK',
            fieldKey: 'name',
            filterByFormula: `{用户名}="${username}"`
        });

        return fetchAPI(`/datasheets/dst13vlcxlMi1EanCl/records?${params}`);
    }

    /**
     * 根据用户ID获取用户信息
     * @param {string} userId - 用户记录ID
     * @returns {Promise} 用户信息
     */
    static async getUserById(userId) {
        return fetchAPI(`/datasheets/dst13vlcxlMi1EanCl/records/${userId}?fieldKey=name`);
    }

    /**
     * 创建新用户
     * @param {Object} userData - 用户数据
     * @returns {Promise} 创建结果
     */
    static async createUser(userData) {
        return fetchAPI('/datasheets/dst13vlcxlMi1EanCl/records', {
            method: 'POST',
            body: JSON.stringify({
                records: [{
                    fields: {
                        用户名: userData.username,
                        密码: userData.password, // 注意：实际应用中应该加密
                        昵称: userData.nickname || '',
                        头像: userData.avatar || '',
                        手机号: userData.phone || '',
                        createdAt: new Date().toISOString()
                    }
                }]
            })
        });
    }
}