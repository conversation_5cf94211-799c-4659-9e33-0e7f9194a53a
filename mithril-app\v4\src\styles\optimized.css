/* 性能优化相关样式 */

/* 优化的备忘录列表 */
.optimized-memos-list {
    /* 启用硬件加速 */
    transform: translateZ(0);
    will-change: scroll-position;
    /* 优化滚动性能 */
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
}

.memos-content {
    /* 启用硬件加速 */
    transform: translateZ(0);
}

.memo-item-wrapper {
    /* 启用硬件加速 */
    transform: translateZ(0);
    will-change: transform;
    /* 优化重绘性能 */
    contain: layout style paint;
}

.memo-card.optimized {
    /* 优化重绘和重排 */
    contain: layout style paint;
    /* 启用硬件加速 */
    transform: translateZ(0);
    /* 减少重绘 */
    backface-visibility: hidden;
    /* 优化动画性能 */
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.memo-card.optimized:hover {
    transform: translateY(-2px) translateZ(0);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 懒加载图片样式 */
.lazy-image-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border: var(--border-width) solid var(--border);
    border-radius: var(--radius);
    min-height: 150px;
    color: var(--muted);
    position: relative;
    overflow: hidden;
    /* 优化加载动画 */
    animation: pulse 1.5s ease-in-out infinite;
}

.lazy-image-placeholder::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    animation: shimmer 2s infinite;
}

.lazy-image-placeholder .loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-xs);
    z-index: 1;
}

.lazy-image-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #fef2f2;
    border: var(--border-width) solid #fecaca;
    border-radius: var(--radius);
    min-height: 150px;
    color: #dc2626;
}

/* 懒加载图片本身的样式 */
.lazy-image {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.lazy-image.loaded {
    opacity: 1;
}

.error-icon {
    font-size: 2rem;
    margin-bottom: var(--space-xs);
    opacity: 0.5;
}

.error-text {
    font-size: var(--font-size-sm);
    text-align: center;
}

.loading-text {
    font-size: var(--font-size-sm);
    margin-top: var(--space-xs);
}

/* 脉冲动画 */
@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        opacity: 1;
    }
}

/* 闪烁动画 */
@keyframes shimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* 优化的滚动条 */
.optimized-memos-list::-webkit-scrollbar {
    width: 8px;
}

.optimized-memos-list::-webkit-scrollbar-track {
    background: var(--surface);
    border-radius: 4px;
}

.optimized-memos-list::-webkit-scrollbar-thumb {
    background: var(--border);
    border-radius: 4px;
    transition: background 0.2s ease;
}

.optimized-memos-list::-webkit-scrollbar-thumb:hover {
    background: var(--secondary);
}

/* 内容渲染优化 */
.markdown-rendered {
    /* 优化文本渲染 */
    text-rendering: optimizeSpeed;
    /* 启用硬件加速 */
    transform: translateZ(0);
    /* 优化重绘 */
    contain: layout style paint;
}

.markdown-rendered img {
    /* 优化图片渲染 */
    image-rendering: -webkit-optimize-contrast;
    image-rendering: optimize-contrast;
    /* 启用硬件加速 */
    transform: translateZ(0);
}

.markdown-rendered pre,
.markdown-rendered code {
    /* 优化代码块渲染 */
    text-rendering: optimizeSpeed;
    font-feature-settings: "liga" 0;
}

/* 媒体文件优化 */
.memo-media {
    /* 优化网格布局性能 */
    contain: layout style;
    /* 启用硬件加速 */
    transform: translateZ(0);
}

.memo-image {
    /* 启用硬件加速 */
    transform: translateZ(0);
    /* 优化过渡效果 */
    transition: transform 0.2s ease, filter 0.2s ease;
    /* 优化图片渲染 */
    image-rendering: -webkit-optimize-contrast;
    image-rendering: optimize-contrast;
}

.memo-image:hover {
    transform: scale(1.02) translateZ(0);
    filter: brightness(1.05);
}

.memo-video {
    /* 优化视频渲染 */
    transform: translateZ(0);
    /* 优化视频控件样式 */
    outline: none;
}

/* 响应式优化 */
@media (max-width: 768px) {
    .memo-item-wrapper {
        /* 移动端减少动画以提升性能 */
        transition: none;
    }
    
    .memo-card.optimized {
        /* 移动端禁用悬停效果 */
        transition: none;
    }
    
    .memo-card.optimized:hover {
        transform: none;
        box-shadow: none;
    }
    
    .memo-image:hover {
        transform: none;
        filter: none;
    }
    
    /* 移动端优化滚动 */
    .optimized-memos-list {
        -webkit-overflow-scrolling: touch;
        scroll-snap-type: y proximity;
    }
    
    .memo-item-wrapper {
        scroll-snap-align: start;
    }
}

/* 高DPI屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .markdown-rendered img {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* 减少动画的用户偏好 */
@media (prefers-reduced-motion: reduce) {
    .memo-card.optimized,
    .memo-image,
    .lazy-image-placeholder {
        transition: none;
        animation: none;
    }
    
    .memo-card.optimized:hover,
    .memo-image:hover {
        transform: none;
    }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .lazy-image-placeholder,
    .lazy-image-error {
        border-width: 2px;
        border-color: var(--primary);
    }
    
    .memo-card.optimized {
        border-width: 2px;
    }
}

/* 性能监控样式 */
.performance-monitor {
    position: fixed;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: var(--space-xs);
    border-radius: var(--radius);
    font-family: monospace;
    font-size: 12px;
    z-index: 9999;
    max-width: 200px;
    display: none;
}

.performance-monitor.show {
    display: block;
}

.performance-monitor .metric {
    display: flex;
    justify-content: space-between;
    margin-bottom: 2px;
}

.performance-monitor .metric-name {
    color: #ccc;
}

.performance-monitor .metric-value {
    color: #0f0;
    font-weight: bold;
}

.performance-monitor .metric-value.warning {
    color: #ff0;
}

.performance-monitor .metric-value.error {
    color: #f00;
}

/* 内存优化提示 */
.memory-warning {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: #ff9800;
    color: white;
    padding: var(--space-sm);
    border-radius: var(--radius);
    font-size: var(--font-size-sm);
    z-index: 1000;
    display: none;
    animation: slideUp 0.3s ease-out;
}

.memory-warning.show {
    display: block;
}

@keyframes slideUp {
    from {
        transform: translateX(-50%) translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateX(-50%) translateY(0);
        opacity: 1;
    }
}

/* GPU加速优化 */
.gpu-accelerated {
    transform: translateZ(0);
    will-change: transform;
    backface-visibility: hidden;
    perspective: 1000px;
}

/* 批量更新优化 */
.batch-update {
    contain: layout style paint;
    isolation: isolate;
}

/* 文本选择优化 */
.optimized-text {
    user-select: text;
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    /* 优化文本渲染 */
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}