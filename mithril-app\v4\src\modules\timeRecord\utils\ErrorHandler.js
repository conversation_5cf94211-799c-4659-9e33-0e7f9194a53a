/**
 * 全局错误处理工具
 * 需求: 6.1, 6.2 - 实现API错误的统一处理机制，添加网络状态检测和提示
 */

import { TIME_RECORD_ERRORS, TIME_RECORD_MESSAGES } from '../../../config/timeRecordConfig.js';

export class ErrorHandler {
    constructor() {
        this.errorListeners = [];
        this.networkStatus = {
            isOnline: typeof navigator !== 'undefined' ? navigator.onLine : true,
            lastCheck: Date.now()
        };
        
        // 初始化网络状态监听
        this.initNetworkMonitoring();
        
        // 初始化全局错误监听
        this.initGlobalErrorHandling();
    }
    
    /**
     * 初始化网络状态监听
     * 需求: 6.2 - 添加网络状态检测和提示
     */
    initNetworkMonitoring() {
        if (typeof window !== 'undefined') {
            // 监听网络状态变化
            window.addEventListener('online', () => {
                this.networkStatus.isOnline = true;
                this.networkStatus.lastCheck = Date.now();
                this.notifyNetworkStatusChange(true);
            });
            
            window.addEventListener('offline', () => {
                this.networkStatus.isOnline = false;
                this.networkStatus.lastCheck = Date.now();
                this.notifyNetworkStatusChange(false);
            });
            
            // 定期检查网络连接
            setInterval(() => {
                this.checkNetworkConnection();
            }, 30000); // 每30秒检查一次
        }
    }
    
    /**
     * 初始化全局错误处理
     * 需求: 6.1 - 实现API错误的统一处理机制
     */
    initGlobalErrorHandling() {
        if (typeof window !== 'undefined') {
            // 捕获未处理的Promise错误
            window.addEventListener('unhandledrejection', (event) => {
                console.error('未处理的Promise错误:', event.reason);
                this.handleGlobalError(event.reason, 'promise');
                event.preventDefault(); // 阻止默认的错误处理
            });
            
            // 捕获全局JavaScript错误
            window.addEventListener('error', (event) => {
                console.error('全局JavaScript错误:', event.error);
                this.handleGlobalError(event.error, 'javascript');
            });
        }
    }
    
    /**
     * 检查网络连接状态
     */
    async checkNetworkConnection() {
        try {
            // 尝试发送一个轻量级的网络请求来检测连接
            const response = await fetch('/favicon.ico', {
                method: 'HEAD',
                cache: 'no-cache',
                timeout: 5000
            });
            
            const isOnline = response.ok;
            if (this.networkStatus.isOnline !== isOnline) {
                this.networkStatus.isOnline = isOnline;
                this.networkStatus.lastCheck = Date.now();
                this.notifyNetworkStatusChange(isOnline);
            }
        } catch (error) {
            if (this.networkStatus.isOnline) {
                this.networkStatus.isOnline = false;
                this.networkStatus.lastCheck = Date.now();
                this.notifyNetworkStatusChange(false);
            }
        }
    }
    
    /**
     * 通知网络状态变化
     */
    notifyNetworkStatusChange(isOnline) {
        const message = isOnline ? '网络连接已恢复' : '网络连接已断开';
        const type = isOnline ? 'success' : 'warning';
        
        this.notifyError({
            type: 'network_status',
            message,
            level: type,
            persistent: !isOnline, // 离线状态持续显示
            timestamp: Date.now()
        });
        
        console.log(`网络状态变化: ${message}`);
    }
    
    /**
     * 处理全局错误
     */
    handleGlobalError(error, source) {
        const errorInfo = this.classifyError(error);
        
        // 记录错误日志
        this.logError(error, source, errorInfo);
        
        // 通知错误监听器
        this.notifyError({
            ...errorInfo,
            source,
            originalError: error,
            timestamp: Date.now()
        });
    }
    
    /**
     * 分类错误类型
     * 需求: 6.1 - 实现API错误的统一处理机制
     */
    classifyError(error) {
        if (!error) {
            return {
                type: 'unknown',
                level: 'error',
                message: TIME_RECORD_ERRORS.UNKNOWN_ERROR,
                suggestion: '请刷新页面重试',
                retryable: false
            };
        }
        
        const errorMessage = (error.message || error.toString()).toLowerCase();
        
        // 网络相关错误
        if (this.isNetworkError(errorMessage)) {
            return {
                type: 'network',
                level: 'error',
                message: TIME_RECORD_ERRORS.NETWORK_ERROR,
                suggestion: '请检查网络连接后重试',
                retryable: true,
                retryDelay: 3000
            };
        }
        
        // API限流错误
        if (this.isRateLimitError(errorMessage)) {
            return {
                type: 'rate_limit',
                level: 'warning',
                message: TIME_RECORD_ERRORS.API_LIMIT_ERROR,
                suggestion: '请稍等片刻后重试',
                retryable: true,
                retryDelay: 5000
            };
        }
        
        // 权限错误
        if (this.isPermissionError(errorMessage)) {
            return {
                type: 'permission',
                level: 'error',
                message: TIME_RECORD_ERRORS.PERMISSION_ERROR,
                suggestion: '请检查登录状态或联系管理员',
                retryable: false
            };
        }
        
        // 验证错误
        if (this.isValidationError(errorMessage)) {
            return {
                type: 'validation',
                level: 'warning',
                message: error.message,
                suggestion: '请检查输入内容是否正确',
                retryable: false
            };
        }
        
        // 服务器错误
        if (this.isServerError(errorMessage)) {
            return {
                type: 'server',
                level: 'error',
                message: '服务器暂时不可用',
                suggestion: '请稍后重试，如问题持续请联系技术支持',
                retryable: true,
                retryDelay: 10000
            };
        }
        
        // 超时错误
        if (this.isTimeoutError(errorMessage)) {
            return {
                type: 'timeout',
                level: 'warning',
                message: '请求超时',
                suggestion: '请检查网络连接或稍后重试',
                retryable: true,
                retryDelay: 3000
            };
        }
        
        // 默认错误
        return {
            type: 'unknown',
            level: 'error',
            message: error.message || TIME_RECORD_ERRORS.UNKNOWN_ERROR,
            suggestion: '请稍后重试，如问题持续请联系技术支持',
            retryable: false
        };
    }
    
    /**
     * 判断是否为网络错误
     */
    isNetworkError(errorMessage) {
        const networkKeywords = [
            'network', 'fetch', 'connection', 'timeout', 'offline',
            '网络', '连接', '超时', '离线'
        ];
        return networkKeywords.some(keyword => errorMessage.includes(keyword));
    }
    
    /**
     * 判断是否为限流错误
     */
    isRateLimitError(errorMessage) {
        const rateLimitKeywords = [
            'rate limit', '429', 'too many requests', 'quota exceeded',
            '限流', '请求过多', '频率限制'
        ];
        return rateLimitKeywords.some(keyword => errorMessage.includes(keyword));
    }
    
    /**
     * 判断是否为权限错误
     */
    isPermissionError(errorMessage) {
        const permissionKeywords = [
            'permission', 'unauthorized', 'forbidden', '401', '403',
            '权限', '未授权', '禁止访问'
        ];
        return permissionKeywords.some(keyword => errorMessage.includes(keyword));
    }
    
    /**
     * 判断是否为验证错误
     */
    isValidationError(errorMessage) {
        const validationKeywords = [
            'validation', 'invalid', 'required', 'format',
            '验证失败', '无效', '必填', '格式错误'
        ];
        return validationKeywords.some(keyword => errorMessage.includes(keyword));
    }
    
    /**
     * 判断是否为服务器错误
     */
    isServerError(errorMessage) {
        const serverKeywords = [
            '500', '502', '503', '504', 'internal server error',
            'service unavailable', '服务器错误', '服务不可用'
        ];
        return serverKeywords.some(keyword => errorMessage.includes(keyword));
    }
    
    /**
     * 判断是否为超时错误
     */
    isTimeoutError(errorMessage) {
        const timeoutKeywords = [
            'timeout', 'timed out', 'request timeout',
            '超时', '请求超时'
        ];
        return timeoutKeywords.some(keyword => errorMessage.includes(keyword));
    }
    
    /**
     * 记录错误日志
     */
    logError(error, source, errorInfo) {
        const logEntry = {
            timestamp: new Date().toISOString(),
            source,
            type: errorInfo.type,
            level: errorInfo.level,
            message: errorInfo.message,
            originalMessage: error.message || error.toString(),
            stack: error.stack,
            userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'unknown',
            url: typeof window !== 'undefined' ? window.location.href : 'unknown',
            networkStatus: this.networkStatus
        };
        
        // 输出到控制台
        console.group(`🚨 错误处理 [${errorInfo.level.toUpperCase()}]`);
        console.error('错误信息:', errorInfo.message);
        console.error('原始错误:', error);
        console.error('错误分类:', errorInfo.type);
        console.error('完整日志:', logEntry);
        console.groupEnd();
        
        // 在实际应用中，这里可以发送错误日志到服务器
        // this.sendErrorToServer(logEntry);
    }
    
    /**
     * 发送错误日志到服务器（可选实现）
     */
    async sendErrorToServer(logEntry) {
        try {
            // 这里可以实现发送错误日志到服务器的逻辑
            // 例如发送到错误监控服务如Sentry、LogRocket等
            console.log('发送错误日志到服务器:', logEntry);
        } catch (error) {
            console.error('发送错误日志失败:', error);
        }
    }
    
    /**
     * 添加错误监听器
     */
    addErrorListener(listener) {
        if (typeof listener === 'function') {
            this.errorListeners.push(listener);
        }
    }
    
    /**
     * 移除错误监听器
     */
    removeErrorListener(listener) {
        const index = this.errorListeners.indexOf(listener);
        if (index > -1) {
            this.errorListeners.splice(index, 1);
        }
    }
    
    /**
     * 通知所有错误监听器
     */
    notifyError(errorInfo) {
        this.errorListeners.forEach(listener => {
            try {
                listener(errorInfo);
            } catch (error) {
                console.error('错误监听器执行失败:', error);
            }
        });
    }
    
    /**
     * 获取网络状态
     */
    getNetworkStatus() {
        return { ...this.networkStatus };
    }
    
    /**
     * 获取用户友好的错误信息
     * 需求: 6.1 - 实现API错误的统一处理机制
     */
    getUserFriendlyError(error) {
        const errorInfo = this.classifyError(error);
        
        return {
            title: this.getErrorTitle(errorInfo.type),
            message: errorInfo.message,
            suggestion: errorInfo.suggestion,
            type: errorInfo.type,
            level: errorInfo.level,
            retryable: errorInfo.retryable,
            retryDelay: errorInfo.retryDelay,
            icon: this.getErrorIcon(errorInfo.type)
        };
    }
    
    /**
     * 获取错误标题
     */
    getErrorTitle(errorType) {
        const titles = {
            network: '网络连接问题',
            rate_limit: 'API请求限制',
            permission: '权限验证失败',
            validation: '输入验证错误',
            server: '服务器错误',
            timeout: '请求超时',
            unknown: '未知错误'
        };
        
        return titles[errorType] || titles.unknown;
    }
    
    /**
     * 获取错误图标
     */
    getErrorIcon(errorType) {
        const icons = {
            network: '🌐',
            rate_limit: '⏱️',
            permission: '🔒',
            validation: '⚠️',
            server: '🔧',
            timeout: '⏰',
            unknown: '❌'
        };
        
        return icons[errorType] || icons.unknown;
    }
    
    /**
     * 处理API错误的统一方法
     * 需求: 6.1 - 实现API错误的统一处理机制
     */
    async handleApiError(error, context = {}) {
        const errorInfo = this.classifyError(error);
        
        // 记录错误
        this.logError(error, 'api', errorInfo);
        
        // 如果是网络错误且当前在线，检查网络状态
        if (errorInfo.type === 'network' && this.networkStatus.isOnline) {
            await this.checkNetworkConnection();
        }
        
        // 通知错误
        this.notifyError({
            ...errorInfo,
            context,
            source: 'api',
            originalError: error,
            timestamp: Date.now()
        });
        
        return errorInfo;
    }
    
    /**
     * 清理资源
     */
    destroy() {
        this.errorListeners = [];
        
        if (typeof window !== 'undefined') {
            window.removeEventListener('online', this.notifyNetworkStatusChange);
            window.removeEventListener('offline', this.notifyNetworkStatusChange);
        }
    }
}

// 创建全局错误处理器实例
export const globalErrorHandler = new ErrorHandler();

// 导出错误处理相关的工具函数
export const ErrorUtils = {
    /**
     * 包装异步函数，自动处理错误
     */
    wrapAsync(asyncFn, context = {}) {
        return async (...args) => {
            try {
                return await asyncFn(...args);
            } catch (error) {
                await globalErrorHandler.handleApiError(error, context);
                throw error;
            }
        };
    },
    
    /**
     * 包装Promise，自动处理错误
     */
    wrapPromise(promise, context = {}) {
        return promise.catch(async (error) => {
            await globalErrorHandler.handleApiError(error, context);
            throw error;
        });
    },
    
    /**
     * 安全执行函数，捕获并处理错误
     */
    safeExecute(fn, fallback = null, context = {}) {
        try {
            const result = fn();
            if (result && typeof result.then === 'function') {
                return result.catch(async (error) => {
                    await globalErrorHandler.handleApiError(error, context);
                    return fallback;
                });
            }
            return result;
        } catch (error) {
            globalErrorHandler.handleGlobalError(error, 'safe_execute');
            return fallback;
        }
    }
};