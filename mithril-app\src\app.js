// 使用全局的 Mithril 实例
const m = window.m;

// 导入模块
import { AppState } from './models/AppState.js';
import { Auth } from './models/Auth.js';
import { Memos } from './models/Memos.js';
import { Layout } from './components/Layout.js';
import { createTimeRecordModule, SimpleTimeRecord } from './modules/timeRecord/index.js';

// 初始化全局状态
const appState = new AppState();
const auth = new Auth();
const memos = new Memos();

// 创建时间记录模块实例
const timeRecordModule = createTimeRecordModule();

// 将模型实例添加到全局属性中
window.appState = appState;
window.auth = auth;
window.memos = memos;
window.timeRecord = timeRecordModule.component;

// 定义路由
const routes = {
    '/': {
        render: function() {
            return m(Layout);
        }
    },
    '/time-records': {
        render: function() {
            return m(Layout, {
                component: SimpleTimeRecord
            });
        }
    }
};

// 初始化应用
function initApp() {
    console.log('初始化应用...');
    
    const appContainer = document.getElementById('app');
    if (!appContainer) {
        console.error('找不到应用容器');
        return;
    }
    
    console.log('可用路由:', Object.keys(routes));
    
    // 挂载路由
    m.route(appContainer, '/', routes);
    
    console.log('应用初始化完成，当前路由:', m.route.get());
}

// 等待DOM加载完成
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initApp);
} else {
    initApp();
}

// 监听认证事件
window.addEventListener('auth:login', () => m.redraw());
window.addEventListener('auth:logout', () => m.redraw());