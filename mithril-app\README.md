# 时间记录应用

一个基于Mithril.js的简化版时间记录Web应用，用于记录和查看工作时间。

## 功能特性

### 核心功能
- ⏱️ **计时功能**：精确计时，支持开始、暂停、重置
- 📝 **记录保存**：保存工作内容、开始时间、结束时间、持续时间
- 📊 **历史记录**：显示所有时间记录，按时间倒序排列
- ⌨️ **键盘快捷键**：支持空格(开始/暂停)、Esc(重置)、Enter(保存)

### 技术特性
- **Mithril.js 2.2.2**：轻量级现代前端框架
- **API集成**：集成维格表API进行数据存储
- **响应式设计**：适配不同屏幕尺寸
- **性能优化**：定时器清理、事件监听器管理
- **错误处理**：完善的错误提示和异常处理

## 技术架构

### 项目结构
```
src/
├── modules/timeRecord/          # 时间记录模块
│   ├── TimeRecord.js             # 主组件文件
│   └── index.js                  # 模块入口
├── services/                     # API服务
│   └── TimeRecordService.js      # 时间记录API服务
├── utils/                        # 工具函数
│   ├── format.js                 # 格式化工具
│   └── renderOptimizer.js        # 渲染优化
└── app.js                        # 应用主入口
```

### 状态管理
- 使用Mithril的vnode.state进行组件级状态管理
- 遵循不可变更新原则，使用Object.assign进行状态更新
- 自动清理机制防止内存泄漏

### API配置
```javascript
const CONFIG = {
  API: {
    DATASHEET_ID: 'your-datasheet-id',
    TOKEN: 'your-api-token',
    BASE_URL: 'https://api.vika.cn/fusion/v1'
  },
  MAX_CONTENT_LENGTH: 1000,
  PAGE_SIZE: 20
};
```

## 使用说明

### 键盘快捷键
- **空格键**：开始/暂停计时
- **Esc键**：重置计时器
- **Enter键**：保存记录（计时器停止且有内容时）

### 操作流程
1. 点击"开始计时"按钮或按空格键开始计时
2. 工作完成后点击"结束计时"或再次按空格键
3. 在文本框中输入工作内容
4. 点击"保存记录"或按Enter键保存

## 开发指南

### 环境要求
- Node.js 14+
- 现代浏览器支持

### 本地开发
```bash
# 安装依赖
npm install

# 启动开发服务器
npx serve . -p 3000 --single
```

### 构建部署
项目为纯前端应用，可直接部署到任何静态文件服务器。

## 更新日志

### v1.0.1 (2024-08-12)
- ✅ 修复Mithril `vnode.state` 修改错误
- ✅ 添加键盘快捷键支持
- ✅ 优化定时器清理机制
- ✅ 添加内存泄漏防护
- ✅ 改进错误处理和用户反馈

### v1.0.0 (2024-08-11)
- ✅ 基础计时功能
- ✅ 数据持久化存储
- ✅ 历史记录展示
- ✅ 响应式界面设计

## 贡献指南

欢迎提交Issue和Pull Request来改进这个项目。

## 许可证

MIT License