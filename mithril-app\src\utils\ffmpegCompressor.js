// 使用 FFmpeg.wasm 进行视频压缩
// 需要安装: npm install @ffmpeg/ffmpeg @ffmpeg/util

import { FFmpeg } from '@ffmpeg/ffmpeg';
import { fetchFile, toBlobURL } from '@ffmpeg/util';

export class FFmpegCompressor {
    constructor() {
        this.ffmpeg = null;
        this.loaded = false;
    }

    // 初始化 FFmpeg
    async init() {
        if (this.loaded) return;

        try {
            this.ffmpeg = new FFmpeg();
            
            // 加载 FFmpeg 核心文件
            const baseURL = 'https://unpkg.com/@ffmpeg/core@0.12.6/dist/umd';
            await this.ffmpeg.load({
                coreURL: await toBlobURL(`${baseURL}/ffmpeg-core.js`, 'text/javascript'),
                wasmURL: await toBlobURL(`${baseURL}/ffmpeg-core.wasm`, 'application/wasm'),
            });

            this.loaded = true;
            console.log('FFmpeg 加载完成');
        } catch (error) {
            console.error('FFmpeg 加载失败:', error);
            throw error;
        }
    }

    // 压缩视频
    async compressVideo(file, options = {}) {
        const {
            scale = '720:-2',      // 缩放尺寸，-2表示自动计算保持比例
            crf = 28,              // 质量参数，越小质量越好(18-28)
            preset = 'medium',     // 编码速度预设
            maxSizeMB = 10         // 最大文件大小
        } = options;

        try {
            await this.init();

            console.log(`开始FFmpeg压缩: ${file.name}`);

            // 写入输入文件
            const inputName = 'input.mp4';
            const outputName = 'output.mp4';
            
            await this.ffmpeg.writeFile(inputName, await fetchFile(file));

            // 构建FFmpeg命令
            const command = [
                '-i', inputName,
                '-vf', `scale=${scale}`,
                '-c:v', 'libx264',
                '-crf', crf.toString(),
                '-preset', preset,
                '-c:a', 'aac',
                '-b:a', '128k',
                '-movflags', '+faststart',
                outputName
            ];

            console.log('FFmpeg命令:', command.join(' '));

            // 执行压缩
            await this.ffmpeg.exec(command);

            // 读取输出文件
            const data = await this.ffmpeg.readFile(outputName);
            const compressedBlob = new Blob([data.buffer], { type: 'video/mp4' });

            // 清理临时文件
            await this.ffmpeg.deleteFile(inputName);
            await this.ffmpeg.deleteFile(outputName);

            // 创建压缩后的文件
            const compressedFile = new File(
                [compressedBlob],
                file.name.replace(/\.[^/.]+$/, '_compressed.mp4'),
                { type: 'video/mp4' }
            );

            console.log(`FFmpeg压缩完成: ${(compressedFile.size / 1024 / 1024).toFixed(2)}MB`);
            return compressedFile;

        } catch (error) {
            console.error('FFmpeg压缩失败:', error);
            throw error;
        }
    }

    // 微信风格压缩
    async wechatStyleCompress(file) {
        return this.compressVideo(file, {
            scale: '720:-2',
            crf: 25,
            preset: 'fast',
            maxSizeMB: 10
        });
    }
}

export const ffmpegCompressor = new FFmpegCompressor();