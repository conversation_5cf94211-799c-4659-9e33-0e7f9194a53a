// 使用全局的 Mithril 实例而不是导入
const m = window.m;

export class AppState {
    constructor() {
        // 主题状态
        this.theme = localStorage.getItem('theme') || 'light';
        
        // UI 状态
        this.isLoading = false;
        this.notification = null;
        this.showLoginModal = false;
        this.showChangelogModal = false;
        
        // 分页状态
        this.currentPage = 1;
        this.hasMore = true;
        
        // 全局计时器状态
        this.timerRunning = false;
        this.startTime = null;
        this.accumulatedDuration = 0;
        this.timerInterval = null;
    }
    
    /**
     * 开始全局计时器
     */
    startTimer() {
        if (this.timerRunning) return;
        
        this.timerRunning = true;
        this.startTime = Date.now() - (this.accumulatedDuration * 1000);
        
        // 清除现有定时器
        if (this.timerInterval) clearInterval(this.timerInterval);
        
        // 创建新定时器，每秒更新一次
        this.timerInterval = setInterval(() => {
            this.accumulatedDuration = Math.floor((Date.now() - this.startTime) / 1000);
            m.redraw();
        }, 1000);
        
        m.redraw();
    }
    
    /**
     * 停止全局计时器
     */
    stopTimer() {
        if (!this.timerRunning) return;
        
        this.timerRunning = false;
        this.accumulatedDuration = Math.floor((Date.now() - this.startTime) / 1000);
        clearInterval(this.timerInterval);
        this.timerInterval = null;
        m.redraw();
    }
    
    /**
     * 重置全局计时器
     */
    resetTimer() {
        this.timerRunning = false;
        this.startTime = null;
        this.accumulatedDuration = 0;
        clearInterval(this.timerInterval);
        this.timerInterval = null;
        m.redraw();
    }
    
    // 切换主题
    toggleTheme(theme) {
        this.theme = theme;
        localStorage.setItem('theme', theme);
        document.body.className = `main-container theme-${theme}`;
        m.redraw();
    }
    
    // 显示通知
    showNotification(message, type = 'info', duration = type === 'error' ? 5000 : 3000) {
        this.notification = { message, type };
        m.redraw();
        
        // 自动清除通知
        setTimeout(() => {
            this.notification = null;
            m.redraw();
        }, duration);
    }
    
    // 清除通知
    clearNotification() {
        this.notification = null;
        m.redraw();
    }
    
    // 切换登录模态框
    toggleLoginModal(show) {
        this.showLoginModal = show !== undefined ? show : !this.showLoginModal;
        m.redraw();
    }
    
    // 切换更新日志模态框
    toggleChangelogModal(show) {
        this.showChangelogModal = show !== undefined ? show : !this.showChangelogModal;
        m.redraw();
    }
    
    // 更新加载状态
    setLoading(loading) {
        this.isLoading = loading;
        m.redraw();
    }
}