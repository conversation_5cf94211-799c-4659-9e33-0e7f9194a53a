/**
 * 时间记录服务类，处理与vika表的时间记录数据交互
 * 支持本地存储和云端存储两种模式
 */
import { ApiService } from './ApiService.js';

/**
 * VIKA API 配置
 * 使用统一的配置管理
 */
import { API_CONFIG } from '../config.js';

const VIKA_CONFIG = {
    baseURL: API_CONFIG.baseURL,
    datasheetId: API_CONFIG.timeSheetDatasheetId,
    viewId: 'viw5mLr0sA5d9',
    fieldKey: 'id', // 使用id作为字段键
    apiToken: API_CONFIG.token,
    maxConcurrentRequests: 5,
    requestInterval: 200 // 200ms间隔确保每秒不超过5次请求
};

export class TimeRecordService {
    constructor() {
        this.apiService = new ApiService();
        this.cache = new Map();
        this.cacheTimeout = 2 * 60 * 1000; // 2分钟缓存
    }

    /**
     * 测试API连接并获取表结构
     * @returns {Promise<boolean>} 连接是否成功
     */
    async testConnection() {
        try {
            // 获取表结构信息
            const metaResponse = await this.apiService.get(`/datasheets/${VIKA_CONFIG.datasheetId}/fields`);
            console.log('表结构信息:', metaResponse);
            if (metaResponse && metaResponse.data && metaResponse.data.fields) {
                console.log('字段详情:', metaResponse.data.fields);
                metaResponse.data.fields.forEach(field => {
                    console.log(`字段: ${field.name} (ID: ${field.id}, 类型: ${field.type})`);
                });
            }
            
            // 获取记录示例
            const response = await this.apiService.get(`/datasheets/${VIKA_CONFIG.datasheetId}/records`, {
                viewId: VIKA_CONFIG.viewId,
                pageSize: 1
            });
            
            console.log('API连接测试结果:', response);
            if (response && response.records && response.records.length > 0) {
                console.log('示例记录字段:', Object.keys(response.records[0].fields));
            }
            
            // 测试不同的字段格式
            // 测试创建记录 - 只使用文本字段
            console.log('测试创建记录 - 只使用备注字段');
            const testPayload = {
                records: [{
                    fields: {
                        'fld0znfZ7H9xM': '测试记录 - ' + new Date().toLocaleString() // 只使用备注信息字段
                    }
                }]
            };
            
            try {
                const createResult = await this.apiService.post(`/datasheets/${VIKA_CONFIG.datasheetId}/records`, testPayload);
                console.log('测试创建结果:', createResult);
                if (createResult && createResult.success) {
                    console.log('✅ 测试创建成功！');
                } else {
                    console.log('❌ 测试创建失败:', createResult);
                }
            } catch (error) {
                console.log('❌ 测试创建异常:', error.message);
            }
            
            return true;
        } catch (error) {
            console.error('API连接测试失败:', error);
            return false;
        }
    }

    /**
     * 获取时间记录列表
     * @param {number} page - 页码
     * @param {number} limit - 每页条数
     * @returns {Promise<Object>} 包含时间记录数据的Promise
     */
    async getTimeRecords(page = 1, limit = 10) {
        try {
            const cacheKey = `timerecords_${page}_${limit}`;

            // 检查缓存
            if (this.cache.has(cacheKey)) {
                const cached = this.cache.get(cacheKey);
                if (Date.now() - cached.timestamp < this.cacheTimeout) {
                    return { success: true, data: cached.data };
                }
                this.cache.delete(cacheKey);
            }

            // 简化参数，避免500错误
            const params = {
                pageSize: limit,
                pageNum: page
            };

            // 只在viewId有效时添加
            if (VIKA_CONFIG.viewId && VIKA_CONFIG.viewId !== 'viw5mLr0sA5d9') {
                params.viewId = VIKA_CONFIG.viewId;
            }

            // 使用ApiService调用，确保一致的配置和处理方式
            console.log('发起时间记录请求:', params);
            
            const data = await this.apiService.get(`/datasheets/${VIKA_CONFIG.datasheetId}/records`, params);
            console.log('时间记录响应:', data);

            if (data) {
                console.log('获取时间记录成功，数据量:', data.records?.length || 0);
                // 缓存结果
                this.cache.set(cacheKey, {
                    data: data,
                    timestamp: Date.now()
                });
                return { success: true, data: data };
            } else {
                console.error('获取时间记录失败，响应:', data);
                return { success: false, error: '获取时间记录失败' };
            }
        } catch (error) {
            console.error('获取时间记录失败:', error);
            
            // 提供详细的错误信息
            if (error.message?.includes('500')) {
                return { 
                    success: false, 
                    error: '服务器内部错误，请稍后重试' 
                };
            } else if (error.message?.includes('CORS')) {
                return { 
                    success: false, 
                    error: '跨域请求被阻止，请检查网络设置或使用代理' 
                };
            } else if (error.message?.includes('NetworkError')) {
                return { 
                    success: false, 
                    error: '网络连接失败，请检查网络连接' 
                };
            } else if (error.message?.includes('404')) {
                return { 
                    success: false, 
                    error: '数据表不存在，请联系管理员' 
                };
            }
            
            return { success: false, error: `获取数据失败: ${error.message}` };
        }
    }

    /**
     * 创建新的时间记录
     * @param {Object} recordData - 时间记录数据
     * @returns {Promise<Object>} 创建结果
     */
    async createTimeRecord(recordData) {
        console.log('createTimeRecord方法被调用，recordData:', recordData);
        try {
            // 使用正确的字段ID和数据类型
            const startTimestamp = Math.floor(new Date(`${recordData.date} ${recordData.startTime}`).getTime() / 1000);
            const endTimestamp = Math.floor(new Date(`${recordData.date} ${recordData.endTime}`).getTime() / 1000);
            
            // 先尝试只使用文本字段
            const payload = {
                records: [{
                    fields: {
                        'fld0znfZ7H9xM': `${recordData.content} (${recordData.date} ${recordData.startTime}-${recordData.endTime}, ${recordData.duration}秒)` // 备注信息
                    }
                }]
            };

            console.log('创建时间记录请求:', payload);
            
            const data = await this.apiService.post(`/datasheets/${VIKA_CONFIG.datasheetId}/records`, payload);
            console.log('创建时间记录响应:', data);

            if (data) {
                this.clearCache();
                // 安全地访问返回的数据，确保records数组存在且不为空
                const record = data.records && data.records.length > 0 ? data.records[0] : null;
                if (record) {
                    console.log('createTimeRecord: 成功创建记录:', record);
                    return { success: true, data: record };
                } else {
                    console.warn('创建时间记录成功但返回数据为空:', data);
                    return { success: false, error: '创建记录成功但返回数据为空' };
                }
            } else {
                console.error('创建时间记录失败，响应:', data);
                return { success: false, error: '创建时间记录失败' };
            }
        } catch (error) {
            console.error('创建时间记录失败:', error);
            
            // 提供详细的错误信息
            if (error.message.includes('CORS')) {
                return { 
                    success: false, 
                    error: '跨域请求被阻止，请检查网络设置' 
                };
            } else if (error.message.includes('NetworkError')) {
                return { 
                    success: false, 
                    error: '网络连接失败，请检查网络连接' 
                };
            } else if (error.message.includes('401')) {
                return { 
                    success: false, 
                    error: '认证失败，请检查API Token' 
                };
            } else if (error.message.includes('400')) {
                return { 
                    success: false, 
                    error: '请求格式错误，请检查数据格式' 
                };
            }
            
            return { success: false, error: `创建记录失败: ${error.message}` };
        }
    }

    /**
     * 清除缓存
     */
    clearCache() {
        this.cache.clear();
        this.lastFetchTime = 0;
    }


}

// 挂载到window对象，供浏览器环境使用
window.TimeRecordService = TimeRecordService;