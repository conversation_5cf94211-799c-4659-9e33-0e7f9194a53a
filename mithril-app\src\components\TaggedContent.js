// 使用全局的 Mithril 实例
const m = window.m;

// 导入搜索工具函数
import { extractTags, createTagElements } from '../utils/searchUtils.js';

/**
 * 带标签高亮的内容组件
 */
export const TaggedContent = {
    /**
     * 解析内容并创建带标签高亮的虚拟DOM
     * @param {string} content - 原始内容
     * @param {Function} onTagClick - 标签点击回调
     * @returns {Array} Mithril虚拟DOM数组
     */
    parseContent: function(content, onTagClick) {
        if (!content || typeof content !== 'string') {
            return [content || ''];
        }
        
        // 按行分割内容
        const lines = content.split('\n');
        const elements = [];
        
        lines.forEach((line, lineIndex) => {
            if (lineIndex > 0) {
                elements.push(m('br')); // 添加换行
            }
            
            // 解析每行中的标签
            const lineElements = this.parseLineWithTags(line, onTagClick);
            elements.push(...lineElements);
        });
        
        return elements;
    },
    
    /**
     * 解析单行内容中的标签
     * @param {string} line - 单行文本
     * @param {Function} onTagClick - 标签点击回调
     * @returns {Array} 该行的虚拟DOM数组
     */
    parseLineWithTags: function(line, onTagClick) {
        if (!line) {
            return [''];
        }
        
        const elements = [];
        const tagRegex = /#([^\s#]+)/g;
        let lastIndex = 0;
        let match;
        
        while ((match = tagRegex.exec(line)) !== null) {
            const startIndex = match.index;
            const endIndex = startIndex + match[0].length;
            const tag = match[1];
            
            // 添加标签前的文本
            if (startIndex > lastIndex) {
                elements.push(line.slice(lastIndex, startIndex));
            }
            
            // 添加标签元素
            elements.push(m('span.tag-highlight', {
                'data-tag': tag,
                onclick: (e) => {
                    e.stopPropagation(); // 防止触发父元素点击事件
                    if (onTagClick) {
                        onTagClick(tag);
                    }
                }
            }, `#${tag}`));
            
            lastIndex = endIndex;
        }
        
        // 添加剩余文本
        if (lastIndex < line.length) {
            elements.push(line.slice(lastIndex));
        }
        
        return elements.length > 0 ? elements : [line];
    },
    
    /**
     * 渲染带标签高亮的内容
     */
    view: function(vnode) {
        const { content, onTagClick, className = '' } = vnode.attrs;
        
        if (!content) {
            return m('div', { class: className }, '');
        }
        
        const elements = this.parseContent(content, onTagClick);
        
        return m('div', { 
            class: `tagged-content ${className}`.trim() 
        }, elements);
    }
};

/**
 * 简化的标签高亮函数，用于直接在现有组件中使用
 * @param {string} content - 内容
 * @param {Function} onTagClick - 标签点击回调
 * @returns {Array} Mithril虚拟DOM数组
 */
export function renderTaggedContent(content, onTagClick) {
    return TaggedContent.parseContent(content, onTagClick);
}

/**
 * 提取内容中的所有标签
 * @param {string} content - 内容
 * @returns {Array<string>} 标签数组
 */
export function getContentTags(content) {
    return extractTags(content);
}
