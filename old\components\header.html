<!-- 头部组件 -->
<header class="relative py-8 mb-12">
    <!-- 玻璃拟态背景 -->
    <div class="absolute inset-0 backdrop-blur-xl bg-white/20 dark:bg-gray-900/20 rounded-3xl border border-white/30 dark:border-gray-700/30 shadow-2xl">
        <!-- 内部光泽效果 -->
        <div class="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent rounded-3xl"></div>
        <!-- 边缘高光 -->
        <div class="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-white/50 to-transparent"></div>
    </div>

    <!-- 主题切换器 - 玻璃拟态样式 -->
    <div class="absolute top-4 right-4">
        <div class="backdrop-blur-md bg-white/10 dark:bg-gray-800/10 rounded-2xl border border-white/20 dark:border-gray-600/20 p-2 shadow-lg theme-switcher">
        <button id="lightThemeBtn" class="theme-btn" title="浅色模式">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-yellow-500" fill="none" viewBox="0 0 24 24"
                stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
            </svg>
        </button>
        <button id="darkThemeBtn" class="theme-btn" title="深色模式">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-purple-400" fill="none" viewBox="0 0 24 24"
                stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
            </svg>
        </button>
        <button id="eyeProtectBtn" class="theme-btn" title="护眼模式">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-600" fill="none" viewBox="0 0 24 24"
                stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
            </svg>
        </button>
        </div>
    </div>

    <!-- 居中的网站标题 -->
    <div class="relative flex flex-col items-center text-center space-y-4">
        <!-- 主标题区域 -->
        <div class="flex items-center space-x-4">
            <!-- 图标 - 玻璃拟态效果 -->
            <div class="relative">
                <div class="w-16 h-16 backdrop-blur-lg bg-gradient-to-br from-blue-500/80 to-purple-600/80 rounded-2xl flex items-center justify-center shadow-2xl border border-white/30">
                    <!-- 内部高光 -->
                    <div class="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent rounded-2xl"></div>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-white relative z-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                </div>
                <!-- 装饰性光点 - 玻璃效果 -->
                <div class="absolute -top-1 -right-1 w-5 h-5 backdrop-blur-sm bg-yellow-400/90 rounded-full animate-pulse shadow-xl border border-yellow-200/50"></div>
            </div>

            <!-- 标题文字 -->
            <div class="flex flex-col">
                <h1
                    class="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                    当记
                </h1>
            </div>
        </div>

        <!-- 副标题和装饰 - 玻璃拟态卡片 -->
        <div class="backdrop-blur-md bg-white/10 dark:bg-gray-800/10 rounded-2xl border border-white/20 dark:border-gray-600/20 px-6 py-4 shadow-lg">
            <div class="flex flex-col items-center space-y-3">
                <p class="text-lg text-gray-700 dark:text-gray-200 font-medium">记录生活点滴</p>

                <!-- 装饰性分割线 - 玻璃效果 -->
                <div class="flex items-center space-x-2">
                    <div class="w-8 h-0.5 bg-gradient-to-r from-transparent via-blue-400/60 to-blue-400/80 backdrop-blur-sm"></div>
                    <div class="w-2 h-2 bg-blue-400/80 rounded-full shadow-sm backdrop-blur-sm"></div>
                    <div class="w-12 h-0.5 bg-gradient-to-r from-blue-400/80 via-purple-400/80 to-purple-400/80 backdrop-blur-sm"></div>
                    <div class="w-2 h-2 bg-purple-400/80 rounded-full shadow-sm backdrop-blur-sm"></div>
                    <div class="w-8 h-0.5 bg-gradient-to-r from-purple-400/80 via-purple-400/60 to-transparent backdrop-blur-sm"></div>
                </div>
            </div>
        </div>
    </div>
</header>