// 使用全局的 Mithril 实例而不是导入
const m = window.m;

export const LoginModal = {
    oninit: function(vnode) {
        this.username = '';
        this.password = '';
        this.loading = false;
        this.error = '';
    },
    
    view: function(vnode) {
        if (!window.appState.showLoginModal) {
            return null;
        }
        
        return m('.modal.show', {
            onclick: (e) => {
                if (e.target.classList.contains('modal')) {
                    window.appState.toggleLoginModal(false);
                }
            }
        }, [
            m('.modal-content', [
                m('span.close', {
                    onclick: () => window.appState.toggleLoginModal(false)
                }, '×'),
                m('h2', '用户登录'),
                m('form', {
                    onsubmit: async (e) => {
                        e.preventDefault();
                        this.loading = true;
                        this.error = '';
                        m.redraw();
                        
                        const result = await window.auth.login(this.username, this.password);
                        
                        this.loading = false;
                        if (result.success) {
                            window.appState.toggleLoginModal(false);
                            window.appState.showNotification('登录成功', 'success');
                        } else {
                            this.error = result.error || '登录失败';
                        }
                        m.redraw();
                    }
                }, [
                    m('.form-group', [
                        m('label', '用户名'),
                        m('input.form-control', {
                            type: 'text',
                            placeholder: '请输入用户名',
                            value: this.username,
                            oninput: (e) => this.username = e.target.value
                        })
                    ]),
                    m('.form-group', [
                        m('label', '密码'),
                        m('input.form-control', {
                            type: 'password',
                            placeholder: '请输入密码',
                            value: this.password,
                            oninput: (e) => this.password = e.target.value
                        })
                    ]),
                    this.error ? m('.error-message', this.error) : null,
                    m('.form-actions', [
                        m('button.btn.btn-primary', {
                            type: 'submit',
                            disabled: this.loading
                        }, this.loading ? '登录中...' : '登录')
                    ])
                ])
            ])
        ]);
    }
};