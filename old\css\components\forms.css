/**

 * 表单组件样式

 */



.form-group {

  margin-bottom: var(--space-4);

}



.form-label {

  display: block;

  font-size: var(--text-sm);

  font-weight: 500;

  color: var(--color-text, var(--color-gray-900));

  margin-bottom: var(--space-2);

}



.form-label.required::after {

  content: '*';

  color: var(--color-error);

  margin-left: var(--space-1);

}



/* 输入框基础样式 */

.form-input,

.form-textarea,

.form-select {

  width: 100%;

  padding: var(--space-3);

  font-size: var(--text-base);

  line-height: 1.5;

  color: var(--color-text, var(--color-gray-900));

  background-color: var(--color-bg, white);

  border: 1px solid var(--color-border, var(--color-gray-300));

  border-radius: var(--radius-lg);

  transition: all var(--duration-fast) var(--ease-out);

}



.form-input:focus,

.form-textarea:focus,

.form-select:focus {

  outline: none;

  border-color: var(--color-primary);

  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);

}



.form-input::placeholder,

.form-textarea::placeholder {

  color: var(--color-text-tertiary, var(--color-gray-400));

}



/* 输入框尺寸 */

.form-input-sm,

.form-textarea-sm,

.form-select-sm {

  padding: var(--space-2);

  font-size: var(--text-sm);

}



.form-input-lg,

.form-textarea-lg,

.form-select-lg {

  padding: var(--space-4);

  font-size: var(--text-lg);

}



/* 文本域 */

.form-textarea {

  resize: vertical;

  min-height: 6rem;

}



.form-textarea.auto-resize {

  resize: none;

  overflow: hidden;

}



/* 选择框 */

.form-select {

  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");

  background-position: right var(--space-3) center;

  background-repeat: no-repeat;

  background-size: 1rem;

  padding-right: var(--space-10);

  appearance: none;

}



/* 文件上传 */

.form-file {

  position: relative;

  display: inline-block;

  cursor: pointer;

}



.form-file input[type="file"] {

  position: absolute;

  opacity: 0;

  width: 100%;

  height: 100%;

  cursor: pointer;

}



.form-file-label {

  display: flex;

  align-items: center;

  justify-content: center;

  gap: var(--space-2);

  padding: var(--space-3);

  border: 2px dashed var(--color-border, var(--color-gray-300));

  border-radius: var(--radius-lg);

  background-color: var(--color-bg-secondary, var(--color-gray-50));

  color: var(--color-text-secondary, var(--color-gray-600));

  transition: all var(--duration-fast) var(--ease-out);

  cursor: pointer;

}



.form-file:hover .form-file-label,

.form-file-label:hover {

  border-color: var(--color-primary);

  background-color: rgba(59, 130, 246, 0.05);

  color: var(--color-primary);

}



.form-file-preview {

  margin-top: var(--space-3);

  padding: var(--space-3);

  border: 1px solid var(--color-border-light, var(--color-gray-200));

  border-radius: var(--radius-lg);

  background-color: var(--color-bg-secondary, var(--color-gray-50));

}



.form-file-preview img {

  max-width: 100%;

  max-height: 12rem;

  border-radius: var(--radius-md);

}



/* 复选框和单选框 */

.form-checkbox,

.form-radio {

  display: flex;

  align-items: center;

  gap: var(--space-2);

  cursor: pointer;

}



.form-checkbox input[type="checkbox"],

.form-radio input[type="radio"] {

  width: 1rem;

  height: 1rem;

  border: 1px solid var(--color-border, var(--color-gray-300));

  background-color: var(--color-bg, white);

  cursor: pointer;

}



.form-checkbox input[type="checkbox"] {

  border-radius: var(--radius-sm);

}



.form-radio input[type="radio"] {

  border-radius: var(--radius-full);

}



.form-checkbox input[type="checkbox"]:checked,

.form-radio input[type="radio"]:checked {

  background-color: var(--color-primary);

  border-color: var(--color-primary);

}



/* 开关 */

.form-switch {

  display: flex;

  align-items: center;

  gap: var(--space-2);

  cursor: pointer;

}



.form-switch input[type="checkbox"] {

  position: relative;

  width: 2.5rem;

  height: 1.25rem;

  background-color: var(--color-gray-300);

  border-radius: var(--radius-full);

  border: none;

  cursor: pointer;

  transition: background-color var(--duration-fast) var(--ease-out);

  appearance: none;

}



.form-switch input[type="checkbox"]:checked {

  background-color: var(--color-primary);

}



.form-switch input[type="checkbox"]::before {

  content: '';

  position: absolute;

  top: 2px;

  left: 2px;

  width: 1rem;

  height: 1rem;

  background-color: white;

  border-radius: var(--radius-full);

  transition: transform var(--duration-fast) var(--ease-out);

  box-shadow: var(--shadow-sm);

}



.form-switch input[type="checkbox"]:checked::before {

  transform: translateX(1.25rem);

}



/* 表单状态 */

.form-input.error,

.form-textarea.error,

.form-select.error {

  border-color: var(--color-error);

  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);

}



.form-input.success,

.form-textarea.success,

.form-select.success {

  border-color: var(--color-success);

  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);

}



.form-input:disabled,

.form-textarea:disabled,

.form-select:disabled {

  background-color: var(--color-bg-tertiary, var(--color-gray-100));

  color: var(--color-text-tertiary, var(--color-gray-500));

  cursor: not-allowed;

  opacity: 0.6;

}



/* 错误和帮助文本 */

.form-error {

  display: block;

  margin-top: var(--space-1);

  font-size: var(--text-sm);

  color: var(--color-error);

}



.form-help {

  display: block;

  margin-top: var(--space-1);

  font-size: var(--text-sm);

  color: var(--color-text-tertiary, var(--color-gray-500));

}



/* 输入组 */

.input-group {

  display: flex;

  align-items: stretch;

}



.input-group .form-input {

  border-radius: 0;

  border-right: 0;

}



.input-group .form-input:first-child {

  border-top-left-radius: var(--radius-lg);

  border-bottom-left-radius: var(--radius-lg);

}



.input-group .form-input:last-child {

  border-top-right-radius: var(--radius-lg);

  border-bottom-right-radius: var(--radius-lg);

  border-right: 1px solid var(--color-border, var(--color-gray-300));

}



.input-group-addon {

  display: flex;

  align-items: center;

  padding: var(--space-3);

  background-color: var(--color-bg-secondary, var(--color-gray-50));

  border: 1px solid var(--color-border, var(--color-gray-300));

  color: var(--color-text-secondary, var(--color-gray-600));

  font-size: var(--text-sm);

  white-space: nowrap;

}



.input-group-addon:first-child {

  border-top-left-radius: var(--radius-lg);

  border-bottom-left-radius: var(--radius-lg);

  border-right: 0;

}



.input-group-addon:last-child {

  border-top-right-radius: var(--radius-lg);

  border-bottom-right-radius: var(--radius-lg);

  border-left: 0;

}



/* 浮动标签 */

.form-floating {

  position: relative;

}



.form-floating .form-input,

.form-floating .form-textarea {

  padding-top: 1.625rem;

  padding-bottom: 0.625rem;

}



.form-floating .form-label {

  position: absolute;

  top: 0;

  left: 0;

  height: 100%;

  padding: var(--space-3);

  pointer-events: none;

  border: 1px solid transparent;

  transform-origin: 0 0;

  transition: all var(--duration-fast) var(--ease-out);

}



.form-floating .form-input:focus ~ .form-label,

.form-floating .form-input:not(:placeholder-shown) ~ .form-label,

.form-floating .form-textarea:focus ~ .form-label,

.form-floating .form-textarea:not(:placeholder-shown) ~ .form-label {

  opacity: 0.65;

  transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);

}



/* 响应式设计 */

@media (max-width: 640px) {

  .form-input,

  .form-textarea,

  .form-select {

    font-size: var(--text-base);

  }

  

  .input-group {

    flex-direction: column;

  }

  

  .input-group .form-input,

  .input-group-addon {

    border-radius: var(--radius-lg);

    border: 1px solid var(--color-border, var(--color-gray-300));

  }

  

  .input-group .form-input:not(:last-child),

  .input-group-addon:not(:last-child) {

    margin-bottom: var(--space-2);

  }

}