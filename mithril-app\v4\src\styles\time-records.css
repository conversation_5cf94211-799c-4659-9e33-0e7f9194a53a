/**
 * 时间记录模块样式表
 * 提供现代化、响应式的时间记录界面
 */

/* 主容器样式 */
.time-record-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
}

.time-record-container .container {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  padding: 30px;
  margin-bottom: 20px;
}

.time-record-container h2 {
  color: #2c3e50;
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 30px;
  text-align: center;
  position: relative;
}

.time-record-container h2::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
  border-radius: 2px;
}

/* 计时控制区域样式 */
.timer-control {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 16px;
  padding: 40px;
  margin-bottom: 30px;
  text-align: center;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.timer-control.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
}

.timer-display {
  margin-bottom: 30px;
}

.timer-display .duration {
  font-size: 48px;
  font-weight: 700;
  font-family: 'Courier New', monospace;
  letter-spacing: 2px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.timer-control.active .duration {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.timer-buttons {
  display: flex;
  justify-content: center;
  gap: 15px;
  flex-wrap: wrap;
}

.timer-buttons .btn {
  min-width: 120px;
  padding: 12px 24px;
  font-size: 16px;
  font-weight: 600;
  border: none;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.timer-buttons .btn-primary {
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.timer-buttons .btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
}

.timer-buttons .btn-danger {
  background: linear-gradient(45deg, #ff6b6b 0%, #ee5a24 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
}

.timer-buttons .btn-danger:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 107, 107, 0.6);
}

.timer-buttons .btn-secondary {
  background: linear-gradient(45deg, #a8a8a8 0%, #8b8b8b 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(168, 168, 168, 0.4);
}

.timer-buttons .btn-secondary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(168, 168, 168, 0.6);
}

/* 记录表单样式 */
.record-form {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 25px;
  margin-bottom: 30px;
  border: 1px solid #e9ecef;
}

.record-form .form-control {
  width: 100%;
  padding: 15px;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  font-size: 16px;
  font-family: inherit;
  resize: vertical;
  transition: border-color 0.3s ease;
  margin-bottom: 15px;
}

.record-form .form-control:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.record-form .btn-success {
  background: linear-gradient(45deg, #28a745 0%, #20c997 100%);
  color: white;
  border: none;
  padding: 12px 30px;
  border-radius: 50px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(40, 167, 69, 0.4);
}

.record-form .btn-success:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(40, 167, 69, 0.6);
}

.record-form .btn-success:disabled {
  background: #6c757d;
  cursor: not-allowed;
  opacity: 0.6;
  box-shadow: none;
}

/* 记录列表样式 */
.records-list {
  margin-top: 40px;
}

.records-list h3 {
  color: #2c3e50;
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.records-list h3::before {
  content: '📊';
  font-size: 24px;
}

.list-group {
  list-style: none;
  padding: 0;
  margin: 0;
}

.list-group-item {
  background: #ffffff;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 15px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.list-group-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  border-color: #667eea;
}

.record-date {
  color: #667eea;
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 8px;
}

.record-time {
  color: #495057;
  font-size: 14px;
  margin-bottom: 8px;
  font-family: 'Courier New', monospace;
}

.record-duration {
  color: #28a745;
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 12px;
}

.record-content {
  margin-bottom: 12px;
}

.record-content p {
  color: #2c3e50;
  line-height: 1.6;
  margin: 0;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.record-content em {
  color: #6c757d;
  font-style: italic;
}

.record-created {
  color: #6c757d;
  font-size: 12px;
  text-align: right;
  margin-top: 10px;
}

.record-item .icon {
  margin-right: 5px;
  font-size: 14px;
}

/* 计时器脉冲动画 */
.timer-control.active .timer-display .duration {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}

/* 成功提示动画 */
.success-animation {
  animation: successPop 0.6s ease-out;
}

@keyframes successPop {
  0% {
    transform: scale(0.8);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 按钮点击波纹效果 */
.btn {
  position: relative;
  overflow: hidden;
}

.btn::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.3s, height 0.3s;
}

.btn:active::after {
  width: 300px;
  height: 300px;
}

.btn.saving {
  background: linear-gradient(45deg, #ffc107 0%, #ff8f00 100%) !important;
  cursor: not-allowed;
  animation: savingPulse 1.5s infinite;
}

@keyframes savingPulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  color: #6c757d;
  font-size: 18px;
  padding: 40px;
  background: #f8f9fa;
  border-radius: 12px;
  border: 2px dashed #dee2e6;
}

.empty-state::before {
  content: '⏰';
  display: block;
  font-size: 48px;
  margin-bottom: 15px;
  opacity: 0.5;
}

/* 加载状态样式 */
.loading-container,
.error-container {
  text-align: center;
  padding: 60px 20px;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 骨架屏加载效果 */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 4px;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.skeleton-timer {
  height: 120px;
  margin-bottom: 30px;
  border-radius: 16px;
}

.skeleton-record {
  height: 150px;
  margin-bottom: 15px;
  border-radius: 12px;
}

.loading-text {
  color: #667eea;
  font-size: 18px;
  font-weight: 500;
}

.error-message {
  color: #dc3545;
  font-size: 16px;
  margin-bottom: 20px;
}

.retry-button {
  background: #667eea;
  color: white;
  border: none;
  padding: 10px 25px;
  border-radius: 25px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.retry-button:hover {
  background: #5a6fd8;
  transform: translateY(-1px);
}

/* 按钮样式统一 */
.btn-outline-primary,
.btn-outline-secondary {
  padding: 10px 20px;
  border-radius: 25px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid;
  background: transparent;
}

.btn-outline-primary {
  color: #667eea;
  border-color: #667eea;
}

.btn-outline-primary:hover {
  background: #667eea;
  color: white;
  transform: translateY(-1px);
}

.btn-outline-secondary {
  color: #6c757d;
  border-color: #6c757d;
}

.btn-outline-secondary:hover {
  background: #6c757d;
  color: white;
  transform: translateY(-1px);
}

.loading-indicator {
  text-align: center;
  color: #667eea;
  font-size: 16px;
  padding: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .time-record-container {
    padding: 10px;
  }
  
  .time-record-container .container {
    padding: 20px;
    margin: 10px;
  }
  
  .timer-control {
    padding: 25px;
  }
  
  .timer-display .duration {
    font-size: 36px;
  }
  
  .timer-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .timer-buttons .btn {
    width: 100%;
    max-width: 200px;
  }
  
  .list-group-item {
    padding: 15px;
  }
}

@media (max-width: 480px) {
  .time-record-container h2 {
    font-size: 24px;
  }
  
  .timer-display .duration {
    font-size: 28px;
  }
  
  .record-form {
    padding: 15px;
  }
}

/* 连接测试样式 */
.connection-test {
    margin: 1rem 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.success-text {
    color: #28a745;
    font-size: 0.9rem;
    font-weight: 500;
}

.error-text {
    color: #dc3545;
    font-size: 0.9rem;
    font-weight: 500;
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
  .time-record-container .container {
    background: #1e1e1e;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  }
  
  .time-record-container h2 {
    color: #ffffff;
  }
  
  .time-record-container h2::after {
    background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
  }
  
  .timer-control {
    background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
  }
  
  .timer-control.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }
  
  .record-form {
    background: #2d3748;
    border-color: #4a5568;
  }
  
  .record-form .form-control {
    background: #1e1e1e;
    border-color: #4a5568;
    color: #ffffff;
  }
  
  .record-form .form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
  }
  
  .list-group-item {
    background: #1e1e1e;
    border-color: #4a5568;
    color: #ffffff;
  }
  
  .list-group-item:hover {
    border-color: #667eea;
  }
  
  .record-date {
    color: #667eea;
  }
  
  .record-time {
    color: #cbd5e0;
  }
  
  .record-duration {
    color: #68d391;
  }
  
  .record-content p {
    background: #2d3748;
    color: #ffffff;
  }
  
  .record-created {
    color: #a0aec0;
  }
  
  .empty-state {
    background: #2d3748;
    border-color: #4a5568;
    color: #a0aec0;
  }
  
  .skeleton {
    background: linear-gradient(90deg, #2d3748 25%, #4a5568 50%, #2d3748 75%);
  }
}