/**
 * 应用启动工具
 * 用于初始化和测试 Markdown 功能
 */

// 检查必要的依赖
function checkDependencies() {
    const checks = [
        { name: 'Mithril', check: () => window.m },
        { name: 'AppState', check: () => window.appState },
        { name: 'Auth', check: () => window.auth },
        { name: 'Memos', check: () => window.memos }
    ];

    console.log('🔍 检查依赖项...');
    
    const results = checks.map(({ name, check }) => {
        const available = check();
        console.log(`${available ? '✅' : '❌'} ${name}: ${available ? '可用' : '不可用'}`);
        return { name, available };
    });

    const allAvailable = results.every(r => r.available);
    
    if (allAvailable) {
        console.log('🎉 所有依赖项检查通过！');
    } else {
        console.warn('⚠️ 部分依赖项不可用，某些功能可能无法正常工作');
    }

    return allAvailable;
}

// 测试 Markdown 功能
async function testMarkdownFeatures() {
    console.log('🧪 测试 Markdown 功能...');
    
    try {
        // 动态导入 Markdown 相关模块
        const { markdownRenderer, MarkdownHelper } = await import('./markdown.js');
        
        // 测试基本渲染
        const testContent = `# 测试标题
        
这是 **粗体** 和 *斜体* 文本。

\`\`\`javascript
console.log("Hello, Markdown!");
\`\`\`

- 列表项 1
- 列表项 2

> 这是引用文本
`;

        const rendered = markdownRenderer.render(testContent);
        console.log('✅ Markdown 渲染测试通过');
        
        // 测试工具栏按钮
        const buttons = MarkdownHelper.getToolbarButtons();
        console.log(`✅ 工具栏按钮加载完成 (${buttons.length} 个按钮)`);
        
        return true;
    } catch (error) {
        console.error('❌ Markdown 功能测试失败:', error);
        return false;
    }
}

// 测试性能优化功能
async function testPerformanceFeatures() {
    console.log('⚡ 测试性能优化功能...');
    
    try {
        const { debounce, throttle } = await import('./renderOptimizer.js');
        
        // 测试防抖函数
        let counter = 0;
        const debouncedFn = debounce(() => counter++, 100);
        debouncedFn();
        debouncedFn();
        debouncedFn();
        
        setTimeout(() => {
            if (counter === 1) {
                console.log('✅ 防抖函数测试通过');
            } else {
                console.warn('⚠️ 防抖函数测试异常');
            }
        }, 150);
        
        return true;
    } catch (error) {
        console.error('❌ 性能优化功能测试失败:', error);
        return false;
    }
}

// 显示功能状态
function showFeatureStatus() {
    console.log('\n📊 功能状态总览:');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    
    const features = [
        { name: 'Markdown 编辑器', status: '✅ 已启用' },
        { name: 'Markdown 渲染器', status: '✅ 已启用' },
        { name: '实时预览', status: '✅ 已启用' },
        { name: '工具栏', status: '✅ 已启用' },
        { name: '键盘快捷键', status: '✅ 已启用' },
        { name: '性能优化', status: '✅ 已启用' },
        { name: '虚拟滚动', status: '🔄 按需启用' },
        { name: '懒加载', status: '✅ 已启用' },
        { name: '缓存系统', status: '✅ 已启用' }
    ];
    
    features.forEach(({ name, status }) => {
        console.log(`${name.padEnd(20)} ${status}`);
    });
    
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
}

// 显示使用提示
function showUsageTips() {
    console.log('\n💡 使用提示:');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    console.log('📝 编辑器快捷键:');
    console.log('   Ctrl+B     - 粗体');
    console.log('   Ctrl+I     - 斜体');
    console.log('   Ctrl+K     - 插入链接');
    console.log('   Ctrl+Enter - 快速发布');
    console.log('   Tab        - 插入缩进');
    console.log('');
    console.log('🎛️ URL 参数:');
    console.log('   ?optimized=true - 启用优化模式');
    console.log('   ?debug=true     - 启用调试模式');
    console.log('   ?cache=false    - 禁用缓存');
    console.log('');
    console.log('🧪 测试页面:');
    console.log('   test-markdown.html - Markdown 功能测试');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
}

// 主启动函数
async function startup() {
    console.log('🚀 当记 Markdown 增强版启动中...\n');
    
    // 检查依赖
    const depsOk = checkDependencies();
    
    if (depsOk) {
        // 测试功能
        await testMarkdownFeatures();
        await testPerformanceFeatures();
    }
    
    // 显示状态和提示
    showFeatureStatus();
    showUsageTips();
    
    console.log('\n🎉 启动完成！开始享受 Markdown 编辑体验吧！');
    
    // 如果在开发环境，提供一些调试工具
    if (window.location.hostname === 'localhost' || window.location.search.includes('debug=true')) {
        window.debugTools = {
            markdownRenderer: null,
            clearCache: () => {
                if (window.debugTools.markdownRenderer) {
                    window.debugTools.markdownRenderer.clearCache();
                    console.log('✅ Markdown 缓存已清除');
                }
            }
        };
        
        // 异步加载调试工具
        import('./markdown.js').then(({ markdownRenderer }) => {
            window.debugTools.markdownRenderer = markdownRenderer;
        });
        
        // 移除了renderProfiler相关代码
        
        console.log('🔧 调试工具已加载到 window.debugTools');
    }
}

export { startup, checkDependencies, testMarkdownFeatures, testPerformanceFeatures };