// 使用全局的 Mithril 实例而不是导入
const m = window.m;

// 导入视频压缩工具和Markdown编辑器
import { videoCompressor } from '../utils/videoCompressor.js';
import { MarkdownEditor } from './MarkdownEditor.js';

export const PostSection = {
    oninit: function () {
        this.content = '';
        this.files = [];
        this.isPosting = false;
        this.uploadProgress = 0;
        this.previewUrls = [];
        this.status = '公开'; // 默认为公开
        this.compressVideos = true; // 是否压缩视频
        this.compressionProgress = 0; // 压缩进度
        this.compressionMode = 'smart'; // 压缩模式：smart, quick, high
    },

    // 处理文件选择
    handleFileChange: function (e) {
        const files = Array.from(e.target.files);
        console.log(`选择了 ${files.length} 个文件:`, files.map(f => `${f.name} (${f.type})`)); // 调试日志

        const maxSize = 50 * 1024 * 1024; // 50MB (视频文件更大)

        // 过滤和验证文件
        const validFiles = files.filter(file => {
            console.log(`验证文件: ${file.name}, 类型: ${file.type}, 大小: ${file.size}`); // 调试日志

            // 检查文件大小
            if (file.size > maxSize) {
                window.appState.showNotification(`文件 ${file.name} 超过50MB限制`, 'error');
                return false;
            }

            // 检查文件类型 - 使用更宽松的检查
            const isImage = file.type.startsWith('image/');
            const isVideo = file.type.startsWith('video/');

            console.log(`文件类型检查: ${file.name} - 是图片: ${isImage}, 是视频: ${isVideo}`); // 调试日志

            if (!isImage && !isVideo) {
                window.appState.showNotification(`不支持的文件类型: ${file.type}。请选择图片或视频文件。`, 'error');
                return false;
            }

            // 额外的安全检查 - 检查文件扩展名
            const fileName = file.name.toLowerCase();
            const imageExts = ['.jpg', '.jpeg', '.png', '.gif', '.webp'];
            const videoExts = ['.mp4', '.webm', '.ogg', '.mov', '.avi'];
            const allExts = [...imageExts, ...videoExts];

            const hasValidExt = allExts.some(ext => fileName.endsWith(ext));
            console.log(`文件扩展名检查: ${fileName} - 有效扩展名: ${hasValidExt}`); // 调试日志

            if (!hasValidExt) {
                window.appState.showNotification(`不支持的文件格式: ${fileName}`, 'error');
                return false;
            }

            return true;
        });

        console.log(`验证通过的文件数量: ${validFiles.length}`); // 调试日志
        this.files = validFiles;
        this.generatePreviews();
        m.redraw();
    },

    // 生成预览图
    generatePreviews: function () {
        this.previewUrls = [];

        this.files.forEach((file, index) => {
            console.log(`处理文件 ${index}: ${file.name}, 类型: ${file.type}`); // 调试日志

            if (file.type.startsWith('image/')) {
                // 图片预览
                const reader = new FileReader();
                reader.onload = (e) => {
                    this.previewUrls[index] = {
                        type: 'image',
                        url: e.target.result,
                        file: file
                    };
                    console.log(`图片预览生成完成: ${file.name}`); // 调试日志
                    m.redraw();
                };
                reader.onerror = (e) => {
                    console.error(`图片预览生成失败: ${file.name}`, e); // 错误日志
                };
                reader.readAsDataURL(file);
            } else if (file.type.startsWith('video/')) {
                // 视频预览
                const reader = new FileReader();
                reader.onload = (e) => {
                    this.previewUrls[index] = {
                        type: 'video',
                        url: e.target.result,
                        file: file
                    };
                    console.log(`视频预览生成完成: ${file.name}`); // 调试日志
                    m.redraw();
                };
                reader.onerror = (e) => {
                    console.error(`视频预览生成失败: ${file.name}`, e); // 错误日志
                };
                reader.readAsDataURL(file);
            } else {
                console.warn(`不支持的文件类型: ${file.type}`); // 警告日志
            }
        });
    },

    // 移除文件
    removeFile: function (index) {
        this.files.splice(index, 1);
        this.previewUrls.splice(index, 1);

        // 更新文件输入框
        const fileInput = document.getElementById('fileInput');
        if (fileInput && this.files.length === 0) {
            fileInput.value = '';
        }

        m.redraw();
    },

    // 压缩视频文件
    compressVideoFile: async function (file) {
        if (!file.type.startsWith('video/') || !this.compressVideos) {
            return file;
        }

        try {
            console.log(`开始压缩视频: ${file.name}`);
            this.compressionProgress = 0;
            m.redraw();

            // 检查文件大小，如果小于15MB就不压缩（提高阈值）
            if (file.size < 15 * 1024 * 1024) {
                console.log('视频文件较小，跳过压缩');
                return file;
            }

            // 根据用户选择的压缩模式进行压缩
            let compressedFile;
            switch (this.compressionMode) {
                case 'quick':
                    compressedFile = await videoCompressor.quickCompress(file);
                    break;
                case 'high':
                    compressedFile = await videoCompressor.highQualityCompress(file);
                    break;
                case 'smart':
                default:
                    compressedFile = await videoCompressor.smartCompress(file);
                    break;
            }

            this.compressionProgress = 100;
            m.redraw();

            // 显示压缩结果
            const originalSizeMB = (file.size / 1024 / 1024).toFixed(2);
            const compressedSizeMB = (compressedFile.size / 1024 / 1024).toFixed(2);
            const compressionRatio = ((1 - compressedFile.size / file.size) * 100).toFixed(1);

            window.appState.showNotification(
                `视频压缩完成: ${originalSizeMB}MB → ${compressedSizeMB}MB (节省${compressionRatio}%)`,
                'success'
            );

            return compressedFile;

        } catch (error) {
            console.error('视频压缩失败:', error);
            window.appState.showNotification(`视频压缩失败: ${error.message}`, 'warning');
            return file; // 压缩失败时返回原文件
        }
    },

    // 上传文件到服务器（支持图片和视频）
    uploadFiles: async function () {
        if (this.files.length === 0) return [];

        const uploadedFiles = [];

        for (let i = 0; i < this.files.length; i++) {
            let file = this.files[i];

            try {
                // 如果是视频文件，先进行压缩
                if (file.type.startsWith('video/') && this.compressVideos) {
                    file = await this.compressVideoFile(file);
                }

                // 更新上传进度
                this.uploadProgress = Math.round(((i + 0.5) / this.files.length) * 100);
                m.redraw();

                // 上传单个文件
                const result = await this.uploadSingleFile(file);
                uploadedFiles.push(result);

                // 更新进度
                this.uploadProgress = Math.round(((i + 1) / this.files.length) * 100);
                m.redraw();

            } catch (error) {
                console.error(`上传文件 ${file.name} 失败:`, error);
                throw new Error(`上传文件 ${file.name} 失败: ${error.message}`);
            }
        }

        return uploadedFiles;
    },

    // 上传单个文件（图片或视频）
    uploadSingleFile: async function (file) {
        const formData = new FormData();
        // 根据文件类型选择字段名
        const fieldName = file.type.startsWith('image/') ? 'image' : 'file';
        formData.append(fieldName, file);
        formData.append('token', '8e7057ee0ba0be565301980fb3e52763');
        formData.append('format', 'json'); // 改为 json 格式，更容易解析

        const response = await fetch('https://www.junwei.bid:89/web/13/index.php', {
            method: 'POST',
            body: formData
        });

        if (!response.ok) {
            throw new Error(`上传失败: ${response.statusText}`);
        }

        const data = await response.text();
        console.log('服务器响应:', data); // 调试日志

        // 如果返回的是纯 URL（format=url_only 的情况）
        if (data.startsWith('http') && !data.includes('<') && !data.includes('{')) {
            return {
                url: data.trim(),
                type: file.type.startsWith('image/') ? 'image' : 'video',
                name: file.name,
                size: file.size
            };
        }

        // 处理包含 PHP 警告的响应
        let jsonStr = data;

        // 如果响应包含 HTML 错误信息，尝试提取 JSON 部分
        if (data.includes('<br />') || data.includes('<b>')) {
            // 尝试多种方法提取 JSON

            // 方法1: 查找最后一个完整的 JSON 对象
            const lastBraceIndex = data.lastIndexOf('}');
            if (lastBraceIndex !== -1) {
                // 从最后一个 } 向前查找对应的 {
                let braceCount = 1;
                let startIndex = lastBraceIndex - 1;

                while (startIndex >= 0 && braceCount > 0) {
                    if (data[startIndex] === '}') {
                        braceCount++;
                    } else if (data[startIndex] === '{') {
                        braceCount--;
                    }
                    startIndex--;
                }

                if (braceCount === 0) {
                    jsonStr = data.substring(startIndex + 1, lastBraceIndex + 1);
                    console.log('提取的完整 JSON:', jsonStr);
                } else {
                    // 方法2: 使用正则表达式查找 JSON 模式
                    const jsonMatch = data.match(/\{[^{}]*"code"[^{}]*"url"[^{}]*\}/);
                    if (jsonMatch) {
                        jsonStr = jsonMatch[0];
                        console.log('正则匹配的 JSON:', jsonStr);
                    } else {
                        // 方法3: 查找包含 "url" 的 JSON 片段
                        const urlJsonMatch = data.match(/\{[^{}]*"url"[^{}]*\}/);
                        if (urlJsonMatch) {
                            jsonStr = urlJsonMatch[0];
                            console.log('URL JSON 匹配:', jsonStr);
                        }
                    }
                }
            }
        }

        // 解析 JSON
        try {
            // 尝试修复可能的 JSON 格式问题
            let cleanJsonStr = jsonStr;

            // 检查是否有重复的 url 字段导致的格式问题
            if (jsonStr.includes('"},"url":"')) {
                // 找到 data.url 的完整值，然后重构 JSON
                const dataUrlMatch = jsonStr.match(/"data":\s*\{\s*"url":\s*"([^"]+)"/);
                if (dataUrlMatch) {
                    const url = dataUrlMatch[1];
                    cleanJsonStr = `{"code":200,"message":"success","data":{"url":"${url}"}}`;
                    console.log('修复后的 JSON:', cleanJsonStr);
                } else {
                    // 如果找不到 data.url，尝试找根级别的 url
                    const rootUrlMatch = jsonStr.match(/"url":\s*"([^"]+)"/);
                    if (rootUrlMatch) {
                        const url = rootUrlMatch[1];
                        cleanJsonStr = `{"code":200,"message":"success","url":"${url}"}`;
                        console.log('修复后的 JSON (根级别):', cleanJsonStr);
                    }
                }
            }

            const jsonData = JSON.parse(cleanJsonStr);
            if (jsonData.code === 200) {
                let url = jsonData.data?.url || jsonData.url;
                if (url) {
                    // 清理URL：移除反斜杠转义，确保使用HTTPS
                    url = url.replace(/\\\//g, '/'); // 移除 \/ 转义
                    if (url.startsWith('http://')) {
                        url = url.replace('http://', 'https://'); // 强制使用HTTPS
                    }
                    console.log('清理后的URL:', url);

                    return {
                        url: url,
                        type: file.type.startsWith('image/') ? 'image' : 'video',
                        name: file.name,
                        size: file.size
                    };
                } else {
                    throw new Error('服务器返回的数据中没有找到文件URL');
                }
            } else {
                throw new Error(jsonData.message || '上传失败');
            }
        } catch (parseError) {
            console.error('JSON 解析失败:', parseError);
            console.error('原始响应:', data);
            console.error('尝试解析的 JSON:', jsonStr);

            // 如果 JSON 解析失败，但响应中包含成功的 URL，尝试提取
            const urlMatch = data.match(/https?:\/\/[^\s"<>\\]+\.(jpg|jpeg|png|gif|webp|mp4|webm|ogg|mov|avi)/i);
            if (urlMatch) {
                let url = urlMatch[0];
                // 清理URL：移除反斜杠转义，确保使用HTTPS
                url = url.replace(/\\\//g, '/');
                if (url.startsWith('http://')) {
                    url = url.replace('http://', 'https://');
                }
                console.log('从响应中提取并清理的 URL:', url);
                return {
                    url: url,
                    type: file.type.startsWith('image/') ? 'image' : 'video',
                    name: file.name,
                    size: file.size
                };
            }

            throw new Error(`上传可能成功但响应解析失败。请检查服务器配置。原始错误: ${parseError.message}`);
        }
    },

    view: function () {
        // 未登录时不显示发布区域
        if (!window.auth.isAuthenticated) {
            return null;
        }

        return m('.post-section', [
            // 使用 Markdown 编辑器替代普通文本框
            m(MarkdownEditor, {
                value: this.content,
                placeholder: '记录你的想法... (支持 Markdown 语法)',
                minHeight: '120px',
                maxHeight: '300px',
                showToolbar: true,
                autoResize: true,
                oninput: (e) => {
                    this.content = e.target.value;
                },
                onsubmit: () => {
                    // Ctrl+Enter 快速发布
                    const publishBtn = document.querySelector('.post-section .btn-primary');
                    if (publishBtn && !publishBtn.disabled) {
                        publishBtn.click();
                    }
                }
            }),

            // 文件预览区域（支持图片和视频）
            this.previewUrls.length > 0 ? m('.upload-preview', [
                m('.preview-title', `已选择 ${this.files.length} 个文件:`),
                m('.preview-grid', this.previewUrls.map((preview, index) =>
                    m('.preview-item', {
                        key: index
                    }, [
                        // 根据文件类型显示不同的预览
                        preview?.type === 'image' ?
                            m('img', {
                                src: preview.url,
                                alt: `预览图 ${index + 1}`,
                                class: 'preview-image'
                            }) :
                            preview?.type === 'video' ?
                                m('video', {
                                    src: preview.url,
                                    class: 'preview-video',
                                    controls: true,
                                    preload: 'metadata'
                                }) :
                                m('.preview-placeholder', '文件预览'),

                        m('button.remove-btn', {
                            onclick: () => this.removeFile(index),
                            title: '移除文件'
                        }, '×'),
                        m('.file-info', [
                            m('.file-name', this.files[index]?.name || ''),
                            m('.file-size', this.formatFileSize(this.files[index]?.size || 0)),
                            m('.file-type', this.getFileTypeLabel(this.files[index]?.type || ''))
                        ])
                    ])
                ))
            ]) : null,

            // 压缩进度条
            this.compressionProgress > 0 && this.compressionProgress < 100 ? m('.compression-progress', [
                m('.progress-bar', [
                    m('.progress-fill', {
                        style: { width: `${this.compressionProgress}%` }
                    })
                ]),
                m('.progress-text', `视频压缩中: ${this.compressionProgress}%`)
            ]) : null,

            // 上传进度条
            this.isPosting && this.files.length > 0 ? m('.upload-progress', [
                m('.progress-bar', [
                    m('.progress-fill', {
                        style: { width: `${this.uploadProgress}%` }
                    })
                ]),
                m('.progress-text', `上传进度: ${this.uploadProgress}%`)
            ]) : null,

            m('.post-actions', [
                m('.post-options', [
                    m('.file-upload', [
                        m('input', {
                            type: 'file',
                            id: 'fileInput',
                            multiple: true,
                            accept: 'image/*,video/*',
                            style: { display: 'none' },
                            onchange: (e) => this.handleFileChange(e)
                        }),
                        m('label.btn.btn-secondary.btn-sm', {
                            for: 'fileInput'
                        }, [
                            '📎 图片/视频',
                            this.files.length > 0 ? ` (${this.files.length})` : ''
                        ])
                    ]),

                    // 视频压缩选项
                    this.files.some(f => f.type.startsWith('video/')) ? m('.compression-options', [
                        m('label.checkbox-label', [
                            m('input', {
                                type: 'checkbox',
                                checked: this.compressVideos,
                                onchange: (e) => this.compressVideos = e.target.checked
                            }),
                            ' 🗜️ 压缩视频'
                        ]),
                        this.compressVideos ? m('select.compression-mode', {
                            value: this.compressionMode,
                            onchange: (e) => this.compressionMode = e.target.value,
                            title: '选择压缩质量'
                        }, [
                            m('option', { value: 'smart' }, '🧠 智能压缩'),
                            m('option', { value: 'high' }, '🎯 高质量'),
                            m('option', { value: 'quick' }, '⚡ 快速压缩')
                        ]) : null
                    ]) : null,

                    m('select.status-select', {
                        value: this.status,
                        onchange: (e) => this.status = e.target.value
                    }, [
                        m('option', { value: '公开' }, '🌍 公开'),
                        m('option', { value: '私密' }, '🔒 私密')
                    ])
                ]),
                m('button.btn.btn-primary', {
                    onclick: async () => {
                        // 检查是否有内容或文件
                        if (!this.content.trim() && this.files.length === 0) {
                            window.appState.showNotification('请输入内容或选择文件', 'error');
                            return;
                        }

                        this.isPosting = true;
                        this.uploadProgress = 0;
                        m.redraw();

                        try {
                            // 先上传文件（图片和视频）
                            let uploadedFiles = [];
                            if (this.files.length > 0) {
                                uploadedFiles = await this.uploadFiles();
                            }

                            // 创建备忘录
                            const result = await window.memos.createMemo(this.content, uploadedFiles, this.status);

                            if (result.success) {
                                window.appState.showNotification('发布成功', 'success');
                                this.content = '';
                                this.files = [];
                                this.previewUrls = [];
                                this.uploadProgress = 0;
                                this.status = '公开'; // 重置为默认状态

                                // 清空文件输入框
                                const fileInput = document.getElementById('fileInput');
                                if (fileInput) fileInput.value = '';
                                
                                // 强制重新渲染界面，确保新备忘录正确显示
                                setTimeout(() => {
                                    m.redraw();
                                }, 100);
                            } else {
                                window.appState.showNotification(result.error || '发布失败', 'error');
                            }
                        } catch (error) {
                            console.error('发布失败:', error);
                            window.appState.showNotification(error.message || '发布失败', 'error');
                        } finally {
                            this.isPosting = false;
                            m.redraw();
                        }
                    },
                    disabled: this.isPosting
                }, this.isPosting ? (this.files.length > 0 ? '上传中...' : '发布中...') : '✨ 发布')
            ])
        ]);
    },

    // 格式化文件大小
    formatFileSize: function (bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    },

    // 获取文件类型标签
    getFileTypeLabel: function (type) {
        if (type.startsWith('image/')) {
            return '🖼️ 图片';
        } else if (type.startsWith('video/')) {
            return '🎬 视频';
        }
        return '📄 文件';
    }
};