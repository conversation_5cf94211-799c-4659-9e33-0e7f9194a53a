/**
 * 渲染优化工具
 * 包含懒加载组件和其他渲染优化功能
 */

// 使用全局的 Mithril 实例而不是导入
const m = window.m;

/**
 * 防抖函数
 * 限制函数执行频率，只在最后一次调用后延迟执行
 * @param {Function} func - 要防抖的函数
 * @param {number} wait - 延迟毫秒数
 * @returns {Function} 防抖后的函数
 */
export function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func.apply(this, args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * 懒加载图片组件
 * 只有当图片进入视口时才加载图片
 */
export const LazyImage = {
    oninit: function(vnode) {
        vnode.state.loaded = false;
        vnode.state.observer = null;
    },
    
    oncreate: function(vnode) {
        // 创建交叉观察器实现懒加载
        if ('IntersectionObserver' in window) {
            vnode.state.observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        vnode.state.loaded = true;
                        m.redraw();
                        // 加载后停止观察
                        if (vnode.state.observer) {
                            vnode.state.observer.unobserve(entry.target);
                            vnode.state.observer.disconnect();
                        }
                    }
                });
            }, {
                rootMargin: '50px' // 提前50px开始加载
            });
            
            vnode.state.observer.observe(vnode.dom);
        } else {
            // 如果不支持 IntersectionObserver，则直接加载
            vnode.state.loaded = true;
            m.redraw();
        }
    },
    
    onremove: function(vnode) {
        // 清理观察器
        if (vnode.state.observer) {
            vnode.state.observer.disconnect();
            vnode.state.observer = null;
        }
    },
    
    view: function(vnode) {
        const { src, alt, className = '', ...attrs } = vnode.attrs;
        
        return m('img', {
            ...attrs,
            src: vnode.state.loaded ? src : 'data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"%3E%3Crect width="100" height="100" fill="%23f0f0f0"/%3E%3C/svg%3E',
            alt: alt || '',
            class: `${className} lazy-image ${vnode.state.loaded ? 'loaded' : 'loading'}`,
            style: {
                ...attrs.style,
                transition: 'opacity 0.3s ease',
                opacity: vnode.state.loaded ? '1' : '0.5'
            }
        });
    }
};