/**

 * 页面状态管理器

 * 用于保存和恢复页面状态，避免不必要的重新加载

 */



export class PageStateManager {

    constructor() {

        this.stateKey = 'pageState';

        this.scrollKey = 'scrollPosition';

    }



    /**

     * 保存页面状态

     */

    saveState(state) {

        try {

            const stateData = {

                ...state,

                timestamp: Date.now(),

                url: window.location.href

            };

            sessionStorage.setItem(this.stateKey, JSON.stringify(stateData));

            console.log('页面状态已保存:', stateData);

        } catch (error) {

            console.warn('保存页面状态失败:', error);

        }

    }



    /**

     * 获取保存的页面状态

     */

    getState() {

        try {

            const stateData = sessionStorage.getItem(this.stateKey);

            if (stateData) {

                const parsed = JSON.parse(stateData);

                // 检查状态是否过期（30分钟）

                if (Date.now() - parsed.timestamp < 30 * 60 * 1000) {

                    console.log('恢复页面状态:', parsed);

                    return parsed;

                } else {

                    console.log('页面状态已过期，清除');

                    this.clearState();

                }

            }

        } catch (error) {

            console.warn('获取页面状态失败:', error);

        }

        return null;

    }



    /**

     * 清除页面状态

     */

    clearState() {

        try {

            sessionStorage.removeItem(this.stateKey);

            sessionStorage.removeItem(this.scrollKey);

        } catch (error) {

            console.warn('清除页面状态失败:', error);

        }

    }



    /**

     * 保存滚动位置

     */

    saveScrollPosition() {

        try {

            const scrollData = {

                x: window.scrollX,

                y: window.scrollY,

                timestamp: Date.now()

            };

            sessionStorage.setItem(this.scrollKey, JSON.stringify(scrollData));

        } catch (error) {

            console.warn('保存滚动位置失败:', error);

        }

    }



    /**

     * 恢复滚动位置

     */

    restoreScrollPosition() {

        try {

            const scrollData = sessionStorage.getItem(this.scrollKey);

            if (scrollData) {

                const parsed = JSON.parse(scrollData);

                // 延迟恢复滚动位置，确保内容已渲染

                setTimeout(() => {

                    window.scrollTo(parsed.x, parsed.y);

                    console.log('滚动位置已恢复:', parsed);

                }, 100);

            }

        } catch (error) {

            console.warn('恢复滚动位置失败:', error);

        }

    }



    /**

     * 检查是否应该恢复状态

     */

    shouldRestoreState() {

        // 检查是否从详情页返回

        const referrer = document.referrer;

        if (referrer && referrer.includes('content.html')) {

            return true;

        }

        

        // 首次访问不恢复状态

        if (!referrer || referrer === '') {

            return false;

        }

        

        // 检查是否有保存的状态且来源是同一域名

        const state = this.getState();

        if (state && referrer) {

            try {

                const referrerUrl = new URL(referrer);

                const currentUrl = new URL(window.location.href);

                return referrerUrl.origin === currentUrl.origin;

            } catch (error) {

                console.warn('URL解析失败:', error);

                return false;

            }

        }

        

        return false;

    }



    /**

     * 保存备忘录列表状态

     */

    saveMemosState(memos, currentPage, hasMore) {

        this.saveState({

            type: 'memos',

            memos: memos,

            currentPage: currentPage,

            hasMore: hasMore,

            memosCount: memos.length

        });

    }



    /**

     * 获取备忘录列表状态

     */

    getMemosState() {

        const state = this.getState();

        return state && state.type === 'memos' ? state : null;

    }

}