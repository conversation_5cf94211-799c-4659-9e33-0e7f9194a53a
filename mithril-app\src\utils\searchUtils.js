/**
 * 搜索和标签功能工具函数
 */

/**
 * 标签正则表达式：#开头，跟着文字，空格或行尾结束
 */
const TAG_REGEX = /#([^\s#]+)/g;

/**
 * 从文本中提取所有标签
 * @param {string} text - 要解析的文本
 * @returns {Array<string>} 标签数组（不包含#号）
 */
export function extractTags(text) {
    if (!text || typeof text !== 'string') {
        return [];
    }
    
    const tags = [];
    const matches = text.matchAll(TAG_REGEX);
    
    for (const match of matches) {
        const tag = match[1].trim();
        if (tag && !tags.includes(tag)) {
            tags.push(tag);
        }
    }
    
    return tags;
}

/**
 * 在文本中高亮显示标签
 * @param {string} text - 原始文本
 * @param {Function} onTagClick - 标签点击回调函数
 * @returns {string} 包含高亮标签的HTML字符串
 */
export function highlightTags(text, onTagClick) {
    if (!text || typeof text !== 'string') {
        return text;
    }
    
    return text.replace(TAG_REGEX, (match, tag) => {
        const clickHandler = onTagClick ? `onclick="(${onTagClick.toString()})('${tag}')"` : '';
        return `<span class="tag-highlight" data-tag="${tag}" ${clickHandler}>#${tag}</span>`;
    });
}

/**
 * 创建可点击的标签元素（用于Mithril组件）
 * @param {string} text - 原始文本
 * @param {Function} onTagClick - 标签点击回调函数
 * @returns {Array} Mithril虚拟DOM数组
 */
export function createTagElements(text, onTagClick) {
    if (!text || typeof text !== 'string') {
        return [text];
    }
    
    const elements = [];
    let lastIndex = 0;
    const matches = [...text.matchAll(TAG_REGEX)];
    
    for (const match of matches) {
        const startIndex = match.index;
        const endIndex = startIndex + match[0].length;
        const tag = match[1];
        
        // 添加标签前的文本
        if (startIndex > lastIndex) {
            elements.push(text.slice(lastIndex, startIndex));
        }
        
        // 添加标签元素
        elements.push({
            tag: 'span',
            attrs: {
                class: 'tag-highlight',
                'data-tag': tag,
                onclick: () => onTagClick && onTagClick(tag)
            },
            children: [`#${tag}`]
        });
        
        lastIndex = endIndex;
    }
    
    // 添加剩余文本
    if (lastIndex < text.length) {
        elements.push(text.slice(lastIndex));
    }
    
    return elements.length > 0 ? elements : [text];
}

/**
 * 搜索备忘录
 * @param {Array} memos - 备忘录数组
 * @param {string} query - 搜索查询
 * @returns {Array} 匹配的备忘录数组
 */
export function searchMemos(memos, query) {
    if (!query || !query.trim()) {
        return memos;
    }
    
    const searchTerm = query.trim().toLowerCase();
    
    return memos.filter(memo => {
        // 搜索内容
        const contentMatch = memo.content && memo.content.toLowerCase().includes(searchTerm);
        
        // 搜索标签
        const tags = extractTags(memo.content || '');
        const tagMatch = tags.some(tag => tag.toLowerCase().includes(searchTerm.replace('#', '')));
        
        // 搜索时间（如果查询是日期格式）
        const timeMatch = memo.created_at && memo.created_at.includes(searchTerm);
        
        return contentMatch || tagMatch || timeMatch;
    });
}

/**
 * 按标签过滤备忘录
 * @param {Array} memos - 备忘录数组
 * @param {string} tag - 标签名（不包含#号）
 * @returns {Array} 包含该标签的备忘录数组
 */
export function filterMemosByTag(memos, tag) {
    if (!tag || !tag.trim()) {
        return memos;
    }
    
    const targetTag = tag.trim().toLowerCase();
    
    return memos.filter(memo => {
        const tags = extractTags(memo.content || '');
        return tags.some(t => t.toLowerCase() === targetTag);
    });
}

/**
 * 获取所有备忘录中的标签统计
 * @param {Array} memos - 备忘录数组
 * @returns {Object} 标签统计对象 {tag: count}
 */
export function getTagStatistics(memos) {
    const tagCounts = {};
    
    memos.forEach(memo => {
        const tags = extractTags(memo.content || '');
        tags.forEach(tag => {
            const lowerTag = tag.toLowerCase();
            tagCounts[lowerTag] = (tagCounts[lowerTag] || 0) + 1;
        });
    });
    
    return tagCounts;
}

/**
 * 获取热门标签
 * @param {Array} memos - 备忘录数组
 * @param {number} limit - 返回的标签数量限制
 * @returns {Array} 热门标签数组，按使用频率排序
 */
export function getPopularTags(memos, limit = 10) {
    const tagCounts = getTagStatistics(memos);
    
    return Object.entries(tagCounts)
        .sort(([, a], [, b]) => b - a)
        .slice(0, limit)
        .map(([tag, count]) => ({ tag, count }));
}

/**
 * 高亮搜索结果
 * @param {string} text - 原始文本
 * @param {string} query - 搜索查询
 * @returns {string} 高亮后的HTML字符串
 */
export function highlightSearchResults(text, query) {
    if (!text || !query || typeof text !== 'string') {
        return text;
    }
    
    const searchTerm = query.trim();
    if (!searchTerm) {
        return text;
    }
    
    // 创建正则表达式，忽略大小写
    const regex = new RegExp(`(${searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
    
    return text.replace(regex, '<mark class="search-highlight">$1</mark>');
}

/**
 * 验证标签格式
 * @param {string} tag - 标签文本
 * @returns {boolean} 是否为有效标签
 */
export function isValidTag(tag) {
    if (!tag || typeof tag !== 'string') {
        return false;
    }
    
    // 标签不能包含空格、特殊字符等
    return /^[a-zA-Z0-9\u4e00-\u9fa5_-]+$/.test(tag);
}

/**
 * 格式化标签显示
 * @param {string} tag - 标签名
 * @returns {string} 格式化后的标签
 */
export function formatTag(tag) {
    if (!tag) return '';
    return tag.startsWith('#') ? tag : `#${tag}`;
}
