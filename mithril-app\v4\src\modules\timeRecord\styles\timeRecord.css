/**
 * 时间记录模块样式文件
 * 基础样式定义和计时器视觉设计
 * 需求: 4.1, 4.2 - 定义组件的基础样式和布局，实现计时器的视觉设计
 */

/* 全局变量定义 */
:root {
    --primary-color: #007bff;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --secondary-color: #6c757d;
    
    --border-radius: 8px;
    --border-radius-lg: 12px;
    --border-radius-sm: 4px;
    
    --shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-md: 0 4px 12px rgba(0,0,0,0.15);
    --shadow-lg: 0 8px 24px rgba(0,0,0,0.2);
    
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
    
    --font-family-base: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
    --font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
    
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    --spacing-xxl: 48px;
}

/* 基础页面容器 */
.time-record-page {
    max-width: 900px;
    margin: 0 auto;
    padding: var(--spacing-lg);
    font-family: var(--font-family-base);
    line-height: 1.6;
    color: var(--dark-color);
    background: var(--background);
    min-height: 100vh;
    position: relative;
}

/* 页面背景装饰 */
.time-record-page::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.1) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

/* 页面头部样式 */
.page-header {
    margin-bottom: var(--spacing-xxl);
    text-align: center;
    position: relative;
    z-index: 1;
}

.page-title {
    color: var(--dark-color);
    font-size: 3rem;
    margin: 0 0 var(--spacing-md) 0;
    font-weight: 700;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--info-color) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 4px 8px rgba(0,0,0,0.1);
    position: relative;
}

.page-title::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--info-color));
    border-radius: 2px;
}

.page-description {
    color: var(--secondary-color);
    font-size: 1.2rem;
    margin: 0;
    font-weight: 400;
    opacity: 0.9;
}

/* 消息提示样式 */
.message {
    padding: 12px 16px;
    border-radius: 6px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
}

.message.error {
    background-color: #fee;
    border: 1px solid #fcc;
    color: #c33;
}

.message.success {
    background-color: #efe;
    border: 1px solid #cfc;
    color: #363;
}

.message-text {
    flex: 1;
}

.message-close {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    padding: 0;
    margin-left: 10px;
    color: inherit;
    opacity: 0.7;
}

.message-close:hover {
    opacity: 1;
}

/* 通用按钮样式 */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: #0056b3;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background: #545b62;
}

.btn-start {
    background: #28a745;
    color: white;
}

.btn-start:hover:not(:disabled) {
    background: #218838;
}

.btn-stop {
    background: #dc3545;
    color: white;
}

.btn-stop:hover:not(:disabled) {
    background: #c82333;
}

.btn-reset {
    background: #6c757d;
    color: white;
}

.btn-reset:hover:not(:disabled) {
    background: #545b62;
}

.btn-outline {
    background: transparent;
    border: 2px solid #007bff;
    color: #007bff;
}

.btn-outline:hover:not(:disabled) {
    background: #007bff;
    color: white;
}

/* 计时器主要区域 - 增强视觉设计 */
.timer-section {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xxl) var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    text-align: center;
    border: 2px solid rgba(255, 255, 255, 0.3);
    box-shadow: var(--shadow-lg);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.timer-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    pointer-events: none;
}

.timer-section.timer-running {
    background: rgba(232, 245, 232, 0.95);
    border-color: var(--success-color);
    box-shadow: 
        var(--shadow-lg),
        0 0 30px rgba(40, 167, 69, 0.2),
        inset 0 1px 0 rgba(255,255,255,0.3);
    transform: translateY(-2px);
}

.timer-section.timer-running::before {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(40, 167, 69, 0.05) 100%);
}

.timer-section.timer-stopped {
    background: rgba(248, 249, 250, 0.95);
    border-color: rgba(233, 236, 239, 0.5);
}

/* 计时器状态指示器 */
.timer-status {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    gap: 8px;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.status-indicator.running {
    background: #28a745;
    animation: pulse 2s infinite;
    box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
}

.status-indicator.stopped {
    background: #6c757d;
}

@keyframes pulse {
    0% {
        transform: scale(0.95);
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
    }
    70% {
        transform: scale(1);
        box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
    }
    100% {
        transform: scale(0.95);
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
    }
}

.status-text {
    font-size: 0.9em;
    color: #666;
    font-weight: 500;
}

/* 主要时间显示 - 增强视觉效果 */
.timer-display {
    margin-bottom: var(--spacing-lg);
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
    padding: var(--spacing-lg) 0;
}

.time-text {
    font-size: 4.5rem;
    font-weight: 200;
    color: var(--dark-color);
    font-family: var(--font-family-mono);
    letter-spacing: 0.1em;
    transition: all var(--transition-normal);
    text-shadow: 0 2px 8px rgba(0,0,0,0.1);
    position: relative;
    background: linear-gradient(135deg, var(--dark-color) 0%, var(--secondary-color) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.timer-display.active .time-text {
    background: linear-gradient(135deg, var(--success-color) 0%, #20c997 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
    transform: scale(1.05);
}

.pulse-dot {
    font-size: 2rem;
    color: var(--success-color);
    animation: pulse-glow 1.5s infinite;
    text-shadow: 0 0 10px rgba(40, 167, 69, 0.5);
}

@keyframes pulse-glow {
    0%, 100% { 
        opacity: 1; 
        transform: scale(1);
        text-shadow: 0 0 10px rgba(40, 167, 69, 0.5);
    }
    50% { 
        opacity: 0.6; 
        transform: scale(1.2);
        text-shadow: 0 0 20px rgba(40, 167, 69, 0.8);
    }
}

/* 计时器信息 */
.timer-info {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-bottom: 25px;
    font-size: 0.9em;
}

.timer-start, .timer-end {
    display: flex;
    align-items: center;
    gap: 5px;
}

.info-label {
    color: #666;
    font-weight: 500;
}

.info-value {
    color: #333;
    font-family: 'Courier New', monospace;
    font-weight: 600;
}

/* 控制按钮 */
.timer-controls {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.timer-controls .btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    font-size: 14px;
    font-weight: 600;
    border-radius: 8px;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.timer-controls .btn:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.timer-controls .btn:hover:not(:disabled):before {
    left: 100%;
}

.btn-icon {
    font-size: 16px;
    line-height: 1;
}

.btn-text {
    font-size: 14px;
}

.btn-start {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border: none;
}

.btn-start:hover:not(:disabled) {
    background: linear-gradient(135deg, #218838 0%, #1ea085 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.btn-stop {
    background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
    color: white;
    border: none;
}

.btn-stop:hover:not(:disabled) {
    background: linear-gradient(135deg, #c82333 0%, #e8650e 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
}

.btn-reset {
    background: linear-gradient(135deg, #6c757d 0%, #adb5bd 100%);
    color: white;
    border: none;
}

.btn-reset:hover:not(:disabled) {
    background: linear-gradient(135deg, #545b62 0%, #95a0a8 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
}

.timer-controls .btn:disabled {
    background: #e9ecef;
    color: #6c757d;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.timer-controls .btn:disabled:before {
    display: none;
}

/* 按钮激活状态 */
.timer-controls .btn:active:not(:disabled) {
    transform: translateY(1px);
    box-shadow: 0 2px 6px rgba(0,0,0,0.2);
}

/* 按钮焦点状态 */
.timer-controls .btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(0,123,255,0.25);
}

.timer-controls .btn-start:focus {
    box-shadow: 0 0 0 3px rgba(40,167,69,0.25);
}

.timer-controls .btn-stop:focus {
    box-shadow: 0 0 0 3px rgba(220,53,69,0.25);
}

.timer-controls .btn-reset:focus {
    box-shadow: 0 0 0 3px rgba(108,117,125,0.25);
}

/* 按钮加载状态 */
.timer-controls .btn.loading {
    position: relative;
    color: transparent;
}

.timer-controls .btn.loading:after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid transparent;
    border-top-color: #ffffff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 按钮禁用状态的详细样式 */
.timer-controls .btn:disabled {
    background: #e9ecef !important;
    color: #6c757d !important;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
    opacity: 0.65;
}

.timer-controls .btn:disabled .btn-icon {
    opacity: 0.5;
}

/* 按钮工具提示样式 */
.timer-controls .btn[title]:hover:after {
    content: attr(title);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 1000;
    margin-bottom: 5px;
}

.timer-controls .btn[title]:hover:before {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: rgba(0,0,0,0.8);
    z-index: 1000;
    margin-bottom: 1px;
}

.input-section {
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.input-header {
    margin-bottom: 15px;
}

.input-header h3 {
    margin: 0 0 5px 0;
    color: #333;
    font-size: 1.2em;
}

.input-hint {
    color: #666;
    font-size: 0.9em;
}

.content-input {
    width: 100%;
    min-height: 100px;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 6px;
    resize: vertical;
    font-family: inherit;
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 15px;
    transition: border-color 0.2s ease;
}

.content-input:focus {
    outline: none;
    border-color: #007bff;
}

.content-input:disabled {
    background-color: #f8f9fa;
    opacity: 0.7;
}

.input-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    margin-top: 20px;
}

/* 输入区域增强样式 */
.input-title {
    margin: 0 0 8px 0;
    color: #333;
    font-size: 1.3em;
    font-weight: 600;
}

.input-subtitle {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.duration-badge {
    background: #e3f2fd;
    color: #1976d2;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 0.85em;
    font-weight: 500;
}

.input-container {
    position: relative;
    margin-bottom: 15px;
}

.content-input.error {
    border-color: #dc3545;
    box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.25);
}

.input-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 8px;
    font-size: 0.85em;
}

.input-validation {
    flex: 1;
}

.validation-error {
    display: flex;
    align-items: center;
    gap: 5px;
    color: #dc3545;
}

.error-icon {
    font-size: 14px;
}

.error-text {
    font-weight: 500;
}

.input-stats {
    display: flex;
    gap: 15px;
    color: #666;
}

.character-count {
    font-family: 'Courier New', monospace;
    font-weight: 500;
}

.character-count.near-limit {
    color: #fd7e14;
    font-weight: 600;
}

.word-count {
    color: #999;
}

/* 快速输入建议 */
.quick-suggestions {
    margin-bottom: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.suggestions-label {
    display: block;
    margin-bottom: 10px;
    color: #666;
    font-size: 0.9em;
    font-weight: 500;
}

.suggestion-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.suggestion-btn {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 16px;
    padding: 6px 12px;
    font-size: 0.85em;
    color: #495057;
    cursor: pointer;
    transition: all 0.2s ease;
}

.suggestion-btn:hover {
    background: #007bff;
    color: white;
    border-color: #007bff;
    transform: translateY(-1px);
}

/* 加载状态 */
.btn.loading {
    position: relative;
}

.loading-spinner {
    display: inline-block;
    width: 14px;
    height: 14px;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 8px;
}

.btn.loading .btn-text {
    opacity: 0.8;
}

.records-section {
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.records-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.records-header h3 {
    margin: 0;
    color: #333;
    font-size: 1.3em;
}

.records-count {
    color: #666;
    font-size: 0.9em;
}

.loading {
    text-align: center;
    padding: 40px 20px;
    color: #666;
    font-size: 16px;
}

.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.empty-icon {
    font-size: 3em;
    margin-bottom: 15px;
}

.empty-text {
    font-size: 1.1em;
    margin: 0 0 10px 0;
    color: #333;
}

.empty-hint {
    margin: 0;
    font-size: 0.9em;
}

.records-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.record-item {
    border: 1px solid #eee;
    border-radius: 6px;
    padding: 15px;
    background: #fafafa;
    transition: box-shadow 0.2s ease;
}

.record-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.record-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.record-date {
    font-weight: 600;
    color: #333;
    font-size: 0.95em;
}

.record-duration {
    background: #e3f2fd;
    color: #1976d2;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.85em;
    font-weight: 500;
}

.record-time {
    margin-bottom: 10px;
}

.time-range {
    color: #666;
    font-size: 0.9em;
    font-family: 'Courier New', monospace;
}

.record-content {
    color: #333;
    line-height: 1.4;
    margin-bottom: 8px;
    font-size: 0.95em;
}

.record-category {
    display: flex;
    justify-content: flex-end;
}

.category-label {
    background: #f0f0f0;
    color: #666;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 0.8em;
}

.load-more-section {
    text-align: center;
    margin-top: 25px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

.load-more-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0 auto;
}

/* 记录标题区域增强 */
.records-title-area {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.records-title {
    margin: 0;
    color: #333;
    font-size: 1.4em;
    font-weight: 600;
}

/* 统计信息 */
.records-stats {
    display: flex;
    gap: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 20px;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.stat-label {
    font-size: 0.85em;
    color: #666;
    font-weight: 500;
}

.stat-value {
    font-size: 1.1em;
    color: #333;
    font-weight: 600;
}

/* 加载状态增强 */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 40px 20px;
    gap: 15px;
}

.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.loading-text {
    color: #666;
    font-size: 0.95em;
}

/* 加载进度条 */
.loading-progress {
    width: 200px;
    height: 4px;
    background: #f0f0f0;
    border-radius: 2px;
    overflow: hidden;
    position: relative;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #007bff, #0056b3);
    border-radius: 2px;
    width: 0%;
}

@keyframes progress-fill {
    0% {
        width: 0%;
        opacity: 0.8;
    }
    50% {
        width: 70%;
        opacity: 1;
    }
    100% {
        width: 100%;
        opacity: 0.8;
    }
}

/* 网络状态警告 */
.network-status-warning {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 10px;
    padding: 8px 12px;
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    font-size: 0.85em;
}

.warning-icon {
    font-size: 16px;
}

.warning-text {
    font-weight: 500;
}

/* 空状态增强 */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.empty-illustration {
    margin-bottom: 20px;
}

.empty-icon {
    font-size: 4em;
    margin-bottom: 10px;
    display: block;
}

.empty-decoration {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-bottom: 20px;
}

.decoration-dot {
    width: 8px;
    height: 8px;
    background: #ddd;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.decoration-dot:nth-child(2) {
    animation-delay: 0.5s;
}

.decoration-dot:nth-child(3) {
    animation-delay: 1s;
}

.empty-title {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 1.3em;
    font-weight: 600;
}

.empty-actions {
    margin-top: 25px;
}

.btn-small {
    padding: 8px 16px;
    font-size: 0.9em;
}

/* 记录分组 */
.record-group {
    margin-bottom: 30px;
}

.group-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 2px solid #e9ecef;
    margin-bottom: 15px;
}

.group-date {
    font-size: 1.1em;
    font-weight: 600;
    color: #333;
}

.group-summary {
    font-size: 0.9em;
    color: #666;
    background: #f8f9fa;
    padding: 4px 12px;
    border-radius: 12px;
}

.group-records {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

/* 记录项增强 */
.record-item {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 16px;
    background: #fafafa;
    transition: all 0.2s ease;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.record-item:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    border-color: #007bff;
    background: white;
}

.record-main {
    flex: 1;
}

.record-time-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.time-range {
    color: #666;
    font-size: 0.9em;
    font-family: 'Courier New', monospace;
    background: #f1f3f4;
    padding: 2px 8px;
    border-radius: 4px;
}

.record-duration {
    background: #e8f5e8;
    color: #2e7d32;
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 0.85em;
    font-weight: 600;
}

.record-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 15px;
}

.content-text {
    margin: 0;
    color: #333;
    line-height: 1.5;
    flex: 1;
    font-size: 0.95em;
}

.category-tag {
    background: #fff3cd;
    color: #856404;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 0.8em;
    font-weight: 500;
    white-space: nowrap;
}

/* 记录操作按钮 */
.record-actions {
    display: flex;
    flex-direction: column;
    gap: 5px;
    margin-left: 15px;
}

.action-btn {
    background: none;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 6px 8px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
    opacity: 0.7;
}

.action-btn:hover {
    background: #f8f9fa;
    border-color: #007bff;
    opacity: 1;
    transform: scale(1.1);
}

/* 底部区域 */
.records-footer {
    text-align: center;
    padding: 20px;
    border-top: 1px solid #eee;
    margin-top: 20px;
    color: #666;
}

.footer-text {
    font-size: 0.9em;
    margin-right: 15px;
}

.footer-action {
    background: none;
    border: none;
    color: #007bff;
    cursor: pointer;
    font-size: 0.9em;
    text-decoration: underline;
}

.footer-action:hover {
    color: #0056b3;
}

/* ========================================
   响应式设计 - 移动端和平板端适配
   需求: 4.4 - 确保所有设备上的可用性
   ======================================== */

/* 大屏幕设备 (1200px+) */
@media (min-width: 1200px) {
    .time-record-page {
        max-width: 1100px;
        padding: var(--spacing-xxl);
    }
    
    .page-title {
        font-size: 3.5rem;
    }
    
    .time-text {
        font-size: 5rem;
    }
    
    .timer-section {
        padding: var(--spacing-xxl) var(--spacing-xxl);
    }
    
    .records-section {
        display: grid;
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
        align-items: start;
        max-width: 800px;
        margin: 0 auto;
    }
    
    .records-header {
        grid-column: 1 / -1;
    }
}

/* 平板端适配 (768px - 1199px) */
@media (min-width: 768px) and (max-width: 1199px) {
    .time-record-page {
        max-width: 100%;
        padding: var(--spacing-lg);
    }
    
    .page-title {
        font-size: 2.8rem;
    }
    
    .time-text {
        font-size: 4rem;
    }
    
    .timer-section {
        padding: var(--spacing-xl) var(--spacing-lg);
    }
    
    .timer-controls {
        gap: var(--spacing-md);
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .timer-controls .btn {
        min-width: 140px;
        padding: var(--spacing-md) var(--spacing-lg);
    }
    
    .input-section {
        padding: var(--spacing-lg);
    }
    
    .content-input {
        min-height: 120px;
        font-size: 16px; /* 防止iOS缩放 */
    }
    
    .records-stats {
        justify-content: space-around;
        gap: var(--spacing-lg);
    }
    
    .record-item {
        padding: var(--spacing-lg);
    }
    
    .record-time-info {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }
    
    .record-content {
        flex-direction: column;
        gap: var(--spacing-sm);
    }
}

/* 移动端适配 (320px - 767px) */
@media (max-width: 767px) {
    .time-record-page {
        padding: var(--spacing-md);
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    }
    
    .page-header {
        margin-bottom: var(--spacing-lg);
    }
    
    .page-title {
        font-size: 2.2rem;
        margin-bottom: var(--spacing-sm);
    }
    
    .page-description {
        font-size: 1rem;
    }
    
    /* 计时器区域移动端优化 */
    .timer-section {
        padding: var(--spacing-lg) var(--spacing-md);
        margin-bottom: var(--spacing-lg);
        border-radius: var(--border-radius);
    }
    
    .timer-status {
        margin-bottom: var(--spacing-md);
        gap: var(--spacing-sm);
    }
    
    .status-text {
        font-size: 0.85rem;
    }
    
    .timer-display {
        margin-bottom: var(--spacing-md);
        padding: var(--spacing-md) 0;
    }
    
    .time-text {
        font-size: 2.8rem;
        letter-spacing: 0.05em;
    }
    
    .timer-display.active .time-text {
        transform: scale(1.02);
    }
    
    .pulse-dot {
        font-size: 1.5rem;
    }
    
    .timer-info {
        flex-direction: column;
        gap: var(--spacing-sm);
        margin-bottom: var(--spacing-lg);
    }
    
    .timer-start, .timer-end {
        justify-content: center;
        font-size: 0.85rem;
    }
    
    /* 控制按钮移动端布局 */
    .timer-controls {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: stretch;
    }
    
    .timer-controls .btn {
        width: 100%;
        padding: var(--spacing-md) var(--spacing-lg);
        font-size: 1rem;
        font-weight: 600;
        border-radius: var(--border-radius);
        min-height: 48px; /* 触摸友好的最小高度 */
    }
    
    .btn-icon {
        font-size: 18px;
    }
    
    .btn-text {
        font-size: 1rem;
    }
    
    /* 输入区域移动端优化 */
    .input-section {
        padding: var(--spacing-md);
        margin-bottom: var(--spacing-md);
        border-radius: var(--border-radius);
    }
    
    .input-title {
        font-size: 1.1rem;
    }
    
    .input-subtitle {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }
    
    .duration-badge {
        align-self: flex-start;
    }
    
    .content-input {
        min-height: 100px;
        font-size: 16px; /* 防止iOS自动缩放 */
        padding: var(--spacing-md);
        border-radius: var(--border-radius-sm);
    }
    
    .input-footer {
        flex-direction: column;
        gap: var(--spacing-sm);
        align-items: flex-start;
    }
    
    .input-stats {
        align-self: flex-end;
        gap: var(--spacing-md);
    }
    
    .quick-suggestions {
        padding: var(--spacing-md);
        margin-bottom: var(--spacing-md);
    }
    
    .suggestion-buttons {
        gap: var(--spacing-sm);
    }
    
    .suggestion-btn {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: 0.8rem;
        border-radius: var(--border-radius-lg);
    }
    
    .input-actions {
        flex-direction: column-reverse;
        gap: var(--spacing-md);
    }
    
    .input-actions .btn {
        width: 100%;
        min-height: 48px;
        font-size: 1rem;
    }
    
    /* 历史记录移动端优化 */
    .records-section {
        padding: var(--spacing-md);
        border-radius: var(--border-radius);
    }
    
    .records-header {
        margin-bottom: var(--spacing-md);
        padding-bottom: var(--spacing-sm);
    }
    
    .records-title-area {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }
    
    .records-title {
        font-size: 1.2rem;
    }
    
    .records-count {
        font-size: 0.8rem;
    }
    
    .records-stats {
        flex-direction: row;
        justify-content: space-around;
        padding: var(--spacing-md);
        gap: var(--spacing-md);
    }
    
    .stat-item {
        text-align: center;
    }
    
    .stat-label {
        font-size: 0.75rem;
    }
    
    .stat-value {
        font-size: 0.95rem;
    }
    
    /* 记录组移动端布局 */
    .record-group {
        margin-bottom: var(--spacing-lg);
    }
    
    .group-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
        padding: var(--spacing-sm) 0;
    }
    
    .group-date {
        font-size: 1rem;
    }
    
    .group-summary {
        font-size: 0.8rem;
        padding: var(--spacing-xs) var(--spacing-sm);
    }
    
    .group-records {
        gap: var(--spacing-md);
    }
    
    /* 记录项移动端布局 */
    .record-item {
        flex-direction: column;
        padding: var(--spacing-md);
        border-radius: var(--border-radius-sm);
        gap: var(--spacing-md);
    }
    
    .record-main {
        width: 100%;
    }
    
    .record-time-info {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-xs);
        margin-bottom: var(--spacing-sm);
    }
    
    .time-range {
        font-size: 0.8rem;
        padding: var(--spacing-xs) var(--spacing-sm);
        background-color: rgba(0, 123, 255, 0.1);
        border-radius: var(--border-radius-xs);
    }

    .record-duration {
        font-size: 0.75rem;
        padding: var(--spacing-xs) var(--spacing-sm);
        background-color: rgba(40, 167, 69, 0.1);
        border-radius: var(--border-radius-xs);
        font-weight: 500;
    }
    
    .record-content {
        flex-direction: column;
        gap: var(--spacing-sm);
        align-items: flex-start;
    }
    
    .content-text {
        font-size: 0.9rem;
        line-height: 1.4;
    }
    
    .category-tag {
        font-size: 0.7rem;
        padding: var(--spacing-xs) var(--spacing-sm);
    }
    
    .record-actions {
        flex-direction: row;
        gap: var(--spacing-sm);
        align-self: flex-end;
        margin-left: 0;
    }
    
    .action-btn {
        padding: var(--spacing-sm);
        font-size: 16px;
        min-width: 36px;
        min-height: 36px;
    }
    
    /* 加载更多按钮移动端 */
    .load-more-section {
        margin-top: var(--spacing-lg);
        padding-top: var(--spacing-md);
    }
    
    .load-more-btn {
        width: 100%;
        min-height: 48px;
        font-size: 1rem;
    }
    
    /* 空状态移动端 */
    .empty-state {
        padding: var(--spacing-xl) var(--spacing-md);
    }
    
    .empty-icon {
        font-size: 3rem;
    }
    
    .empty-title {
        font-size: 1.1rem;
    }
    
    .empty-text {
        font-size: 0.9rem;
    }
    
    .empty-actions .btn {
        width: 100%;
        min-height: 48px;
    }
    
    /* 消息提示移动端 */
    .message {
        padding: var(--spacing-md);
        margin-bottom: var(--spacing-md);
        border-radius: var(--border-radius-sm);
        font-size: 0.9rem;
    }
    
    .message-close {
        font-size: 20px;
        min-width: 24px;
        min-height: 24px;
    }
}

/* 小屏幕移动设备 (320px - 480px) */
@media (max-width: 480px) {
    .time-record-page {
        padding: var(--spacing-sm);
    }
    
    .page-title {
        font-size: 1.8rem;
    }
    
    .time-text {
        font-size: 2.2rem;
    }
    
    .timer-section {
        padding: var(--spacing-md);
    }
    
    .timer-controls .btn {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: 0.9rem;
        min-height: 44px;
    }
    
    .input-section,
    .records-section {
        padding: var(--spacing-sm);
    }
    
    .content-input {
        min-height: 80px;
        padding: var(--spacing-sm);
    }
    
    .record-item {
        padding: var(--spacing-md);
        margin-bottom: var(--spacing-md);
    }
    
    .records-stats {
        flex-direction: column;
        gap: var(--spacing-sm);
    }
    
    .stat-item {
        flex-direction: row;
        justify-content: space-between;
        padding: var(--spacing-xs) 0;
    }
}

/* 横屏模式优化 */
@media (max-width: 767px) and (orientation: landscape) {
    .timer-section {
        padding: var(--spacing-md);
    }
    
    .timer-display {
        margin-bottom: var(--spacing-sm);
    }
    
    .time-text {
        font-size: 2.5rem;
    }
    
    .timer-info {
        flex-direction: row;
        justify-content: center;
        gap: var(--spacing-lg);
        margin-bottom: var(--spacing-md);
    }
    
    .timer-controls {
        flex-direction: row;
        justify-content: center;
        flex-wrap: wrap;
        gap: var(--spacing-sm);
    }
    
    .timer-controls .btn {
        width: auto;
        min-width: 120px;
        padding: var(--spacing-sm) var(--spacing-md);
    }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
    .btn,
    .action-btn,
    .suggestion-btn,
    .message-close {
        min-height: 44px;
        min-width: 44px;
    }
    
    .timer-controls .btn {
        min-height: 48px;
    }
    
    .content-input {
        font-size: 16px; /* 防止iOS自动缩放 */
    }
    
    /* 增加触摸目标间距 */
    .timer-controls {
        gap: var(--spacing-md);
    }
    
    .input-actions {
        gap: var(--spacing-md);
    }
    
    .record-actions {
        gap: var(--spacing-md);
    }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .time-record-page {
        background: white;
    }
    
    .timer-section {
        background: white;
        border: 2px solid black;
    }
    
    .timer-section.timer-running {
        border-color: #000;
        background: #f0f0f0;
    }
    
    .time-text {
        color: black;
        -webkit-text-fill-color: black;
    }
    
    .btn {
        border: 2px solid black;
    }
}

/* ========================================
   交互动画和视觉反馈
   需求: 4.2, 4.3 - 添加计时状态的动画效果，实现按钮点击的视觉反馈
   ======================================== */

/* 页面加载动画 */
.time-record-page {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 计时器状态变化动画 */
.timer-section {
    transition: all var(--transition-normal), transform var(--transition-fast);
}

.timer-section.timer-running {
    animation: timerActivate 0.5s ease-out;
}

@keyframes timerActivate {
    0% {
        transform: scale(1);
        box-shadow: var(--shadow-md);
    }
    50% {
        transform: scale(1.02);
        box-shadow: var(--shadow-lg), 0 0 40px rgba(40, 167, 69, 0.3);
    }
    100% {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg), 0 0 30px rgba(40, 167, 69, 0.2);
    }
}

/* 状态指示器增强动画 */
.status-indicator {
    transition: all var(--transition-normal);
    position: relative;
}

.status-indicator.running {
    animation: statusPulse 2s infinite;
}

@keyframes statusPulse {
    0% {
        transform: scale(0.95);
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
    }
    70% {
        transform: scale(1);
        box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
    }
    100% {
        transform: scale(0.95);
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
    }
}

.status-indicator.running::after {
    content: '';
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    border: 2px solid var(--success-color);
    border-radius: 50%;
    animation: ripple 2s infinite;
    opacity: 0;
}

@keyframes ripple {
    0% {
        transform: scale(0.8);
        opacity: 1;
    }
    100% {
        transform: scale(2);
        opacity: 0;
    }
}

/* 时间显示数字滚动效果 */
.time-text {
    position: relative;
    overflow: hidden;
}

.time-text.updating {
    animation: numberFlip 0.3s ease-in-out;
}

@keyframes numberFlip {
    0% { transform: rotateX(0deg); }
    50% { transform: rotateX(90deg); }
    100% { transform: rotateX(0deg); }
}

/* 按钮交互动画增强 */
.btn {
    position: relative;
    transition: all 0.2s ease;
}

.btn:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.btn:active:not(:disabled) {
    transform: translateY(0);
}

.btn:focus:not(:disabled) {
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
    outline: none;
}

/* 特定按钮类型的动画 */
.btn-start:hover:not(:disabled) {
    background: linear-gradient(135deg, #218838 0%, #1ea085 100%);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
    animation: startButtonGlow 2s infinite;
}

@keyframes startButtonGlow {
    0%, 100% { box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4); }
    50% { box-shadow: 0 6px 25px rgba(40, 167, 69, 0.6); }
}

.btn-stop:hover:not(:disabled) {
    background: linear-gradient(135deg, #c82333 0%, #e8650e 100%);
    box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
    animation: stopButtonPulse 1s infinite;
}

@keyframes stopButtonPulse {
    0%, 100% { transform: translateY(-2px) scale(1); }
    50% { transform: translateY(-2px) scale(1.05); }
}

.btn-reset:hover:not(:disabled) {
    background: linear-gradient(135deg, #545b62 0%, #95a0a8 100%);
    animation: resetButtonShake 0.5s ease-in-out;
}

@keyframes resetButtonShake {
    0%, 100% { transform: translateY(-2px) translateX(0); }
    25% { transform: translateY(-2px) translateX(-2px); }
    75% { transform: translateY(-2px) translateX(2px); }
}

/* 按钮禁用状态动画 */
.btn:disabled {
    animation: disabledFade 0.3s ease-out;
}

@keyframes disabledFade {
    from { opacity: 1; }
    to { opacity: 0.65; }
}

/* 加载状态动画增强 */
.btn.loading {
    pointer-events: none;
}

.btn.loading::before {
    display: none;
}

.loading-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 输入框焦点动画 */
.content-input {
    position: relative;
    transition: all var(--transition-normal);
}

.content-input:focus {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 123, 255, 0.15);
    animation: inputFocusGlow 0.3s ease-out;
}

@keyframes inputFocusGlow {
    0% {
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    100% {
        box-shadow: 0 8px 25px rgba(0, 123, 255, 0.15);
    }
}

/* 输入验证动画 */
.content-input.error {
    animation: inputError 0.5s ease-in-out;
}

@keyframes inputError {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* 消息提示动画 */
.message {
    animation: messageSlideIn 0.4s ease-out;
    position: relative;
    overflow: hidden;
}

@keyframes messageSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
        max-height: 0;
    }
    to {
        opacity: 1;
        transform: translateY(0);
        max-height: 100px;
    }
}

.message.success {
    animation: messageSlideIn 0.4s ease-out, successPulse 0.6s ease-out 0.4s;
}

@keyframes successPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.02); }
    100% { transform: scale(1); }
}

.message.error {
    animation: messageSlideIn 0.4s ease-out, errorShake 0.5s ease-out 0.4s;
}

@keyframes errorShake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-3px); }
    75% { transform: translateX(3px); }
}

/* 记录项悬停动画 */
.record-item {
    transition: all var(--transition-normal);
    position: relative;
}

.record-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0,123,255,0.05) 0%, rgba(0,123,255,0.02) 100%);
    opacity: 0;
    transition: opacity var(--transition-normal);
    border-radius: inherit;
}

.record-item:hover::before {
    opacity: 1;
}

.record-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.12);
    border-color: var(--primary-color);
}

/* 记录加载动画 */
.records-list {
    animation: recordsSlideIn 0.6s ease-out;
}

@keyframes recordsSlideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 记录项逐个显示动画 */
.record-item {
    animation: recordItemFadeIn 0.4s ease-out;
    animation-fill-mode: both;
}

.record-item:nth-child(1) { animation-delay: 0.1s; }
.record-item:nth-child(2) { animation-delay: 0.2s; }
.record-item:nth-child(3) { animation-delay: 0.3s; }
.record-item:nth-child(4) { animation-delay: 0.4s; }
.record-item:nth-child(5) { animation-delay: 0.5s; }

@keyframes recordItemFadeIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 空状态动画 */
.empty-state {
    animation: emptyStateFadeIn 0.8s ease-out;
}

@keyframes emptyStateFadeIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.empty-icon {
    animation: emptyIconFloat 3s ease-in-out infinite;
}

@keyframes emptyIconFloat {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
}

/* 装饰点动画 */
.decoration-dot {
    animation: decorationPulse 2s infinite;
}

.decoration-dot:nth-child(1) { animation-delay: 0s; }
.decoration-dot:nth-child(2) { animation-delay: 0.5s; }
.decoration-dot:nth-child(3) { animation-delay: 1s; }

@keyframes decorationPulse {
    0%, 100% { 
        opacity: 0.3; 
        transform: scale(1);
    }
    50% { 
        opacity: 1; 
        transform: scale(1.2);
    }
}

/* 加载更多按钮动画 */
.load-more-btn:hover:not(:disabled) {
    animation: loadMoreBounce 0.6s ease-in-out;
}

@keyframes loadMoreBounce {
    0%, 100% { transform: translateY(-2px); }
    50% { transform: translateY(-6px); }
}

/* 统计数据动画 */
.stat-value {
    transition: all var(--transition-normal);
}

.stat-item:hover .stat-value {
    transform: scale(1.1);
    color: var(--primary-color);
}

/* 操作按钮悬停动画 */
.action-btn {
    transition: all var(--transition-fast);
}

.action-btn:hover {
    transform: scale(1.2) rotate(5deg);
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* 建议按钮动画 */
.suggestion-btn {
    transition: all var(--transition-fast);
    position: relative;
}

.suggestion-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,123,255,0.2);
}

.suggestion-btn:active {
    transform: translateY(0);
    animation: suggestionClick 0.2s ease-out;
}

@keyframes suggestionClick {
    0% { transform: scale(1); }
    50% { transform: scale(0.95); }
    100% { transform: scale(1); }
}

/* 字符计数动画 */
.character-count {
    transition: all var(--transition-normal);
}

.character-count.near-limit {
    animation: characterWarning 1s ease-in-out infinite;
}

@keyframes characterWarning {
    0%, 100% { 
        color: var(--warning-color);
        transform: scale(1);
    }
    50% { 
        color: var(--danger-color);
        transform: scale(1.05);
    }
}

/* 页面切换动画 */
.page-transition-enter {
    animation: pageEnter 0.5s ease-out;
}

.page-transition-leave {
    animation: pageLeave 0.3s ease-in;
}

@keyframes pageEnter {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pageLeave {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(-20px);
    }
}

/* 数据保存成功动画 */
.save-success {
    animation: saveSuccess 0.8s ease-out;
}

@keyframes saveSuccess {
    0% {
        transform: scale(1);
        background: var(--primary-color);
    }
    50% {
        transform: scale(1.05);
        background: var(--success-color);
        box-shadow: 0 0 20px rgba(40, 167, 69, 0.5);
    }
    100% {
        transform: scale(1);
        background: var(--success-color);
    }
}

/* 网络状态指示动画 */
.network-status {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.8rem;
    z-index: 1000;
    animation: networkStatusSlide 0.3s ease-out;
}

@keyframes networkStatusSlide {
    from {
        opacity: 0;
        transform: translateX(100px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.network-status.online {
    background: var(--success-color);
    color: white;
}

.network-status.offline {
    background: var(--danger-color);
    color: white;
    animation: networkOfflinePulse 1s ease-in-out infinite;
}

@keyframes networkOfflinePulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .pulse-dot,
    .status-indicator.running,
    .decoration-dot,
    .empty-icon,
    .character-count.near-limit,
    .network-status.offline {
        animation: none !important;
    }
    
    .btn:hover:not(:disabled),
    .record-item:hover,
    .content-input:focus {
        transform: none !important;
    }
}

/* 验证反馈样式 */
.validation-feedback {
    margin: 10px 0;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.validation-feedback.has-errors {
    border-left: 4px solid #e74c3c;
    background-color: #fdf2f2;
}

.validation-feedback.has-warnings {
    border-left: 4px solid #f39c12;
    background-color: #fefbf3;
}

.validation-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background-color: rgba(0, 0, 0, 0.05);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.validation-title {
    font-weight: 600;
    margin-left: 8px;
}

.validation-close {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s;
}

.validation-close:hover {
    background-color: rgba(0, 0, 0, 0.1);
}

.validation-content {
    padding: 16px;
}

.validation-message {
    margin-bottom: 12px;
    line-height: 1.5;
}

.validation-suggestions {
    margin-top: 12px;
}

.suggestions-title {
    font-weight: 600;
    margin-bottom: 8px;
    color: #2c3e50;
}

.suggestions-list {
    margin: 0;
    padding-left: 20px;
}

.suggestion-item {
    margin-bottom: 4px;
    color: #34495e;
}

/* 内联验证反馈 */
.inline-validation {
    margin-top: 4px;
    font-size: 0.875rem;
}

.inline-validation.compact {
    margin-top: 2px;
    font-size: 0.8rem;
}

.inline-error, .inline-warning {
    display: flex;
    align-items: center;
    gap: 4px;
}

.inline-error {
    color: #e74c3c;
}

.inline-warning {
    color: #f39c12;
}

.inline-icon {
    font-size: 0.875em;
}

/* 字符计数器 */
.character-counter {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 0.875rem;
    color: #666;
    margin-top: 4px;
}

.character-counter.near-limit {
    color: #f39c12;
}

.character-counter.over-limit {
    color: #e74c3c;
}

.counter-message {
    font-size: 0.8rem;
    font-weight: 500;
}

/* 验证状态指示器 */
.validation-status {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.875rem;
    font-weight: 500;
}

.validation-status.status-error {
    background-color: #fdf2f2;
    color: #e74c3c;
}

.validation-status.status-warning {
    background-color: #fefbf3;
    color: #f39c12;
}

.validation-status.status-success {
    background-color: #f0f9f0;
    color: #27ae60;
}

/* 实时验证提示 */
.live-validation-tip {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 10;
    margin-top: 4px;
    background: white;
    border: 1px solid #e74c3c;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    animation: fadeIn 0.3s ease-out;
}

.tip-content {
    padding: 12px;
}

.tip-message {
    display: block;
    color: #e74c3c;
    font-weight: 500;
    margin-bottom: 8px;
}

.tip-suggestions {
    margin-top: 8px;
}

.tip-suggestion {
    font-size: 0.875rem;
    color: #666;
    margin-bottom: 4px;
}

/* 验证摘要 */
.validation-summary {
    border-radius: 6px;
    overflow: hidden;
    margin: 16px 0;
}

.validation-summary.success {
    background-color: #f0f9f0;
    border: 1px solid #27ae60;
}

.validation-summary.has-errors {
    background-color: #fdf2f2;
    border: 1px solid #e74c3c;
}

.validation-summary.has-warnings {
    background-color: #fefbf3;
    border: 1px solid #f39c12;
}

.summary-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background-color: rgba(0, 0, 0, 0.05);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.summary-title {
    font-weight: 600;
    margin-left: 8px;
}

.summary-stats {
    font-size: 0.875rem;
    color: #666;
}

.summary-section-title {
    font-weight: 600;
    margin: 12px 16px 8px;
    color: #2c3e50;
}

.summary-list {
    margin: 0;
    padding: 0;
    list-style: none;
}

.summary-item {
    display: flex;
    align-items: center;
    padding: 8px 16px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.summary-item:last-child {
    border-bottom: none;
}

.item-field {
    font-weight: 500;
    margin-right: 12px;
    min-width: 80px;
    color: #2c3e50;
}

.item-message {
    flex: 1;
    color: #34495e;
}

.summary-item.error .item-field {
    color: #e74c3c;
}

.summary-item.warning .item-field {
    color: #f39c12;
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.3s ease-out;
}

/* 增强的输入区域样式（支持验证状态） */
.input-section {
    position: relative;
}

.input-section.has-validation-error .content-input {
    border-color: #e74c3c;
    box-shadow: 0 0 0 2px rgba(231, 76, 60, 0.2);
}

.input-section.has-validation-warning .content-input {
    border-color: #f39c12;
    box-shadow: 0 0 0 2px rgba(243, 156, 18, 0.2);
}

.input-section.has-validation-success .content-input {
    border-color: #27ae60;
    box-shadow: 0 0 0 2px rgba(39, 174, 96, 0.2);
}

/* 验证增强的按钮样式 */
.btn.validation-disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
}

.btn.validation-warning {
    background-color: #f39c12;
    border-color: #f39c12;
}

.btn.validation-warning:hover {
    background-color: #e67e22;
    border-color: #e67e22;
}