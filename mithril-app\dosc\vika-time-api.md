时间记录分类Field ID: fld9Mole3W9eR
维格列类型
单行文本
string
描述
单行文本，适合保存不带换行符的文本，例如文章的标题。
示例值
"ff"
 备注信息Field ID: fld0znfZ7H9xM
维格列类型
多行文本
string
描述
多行文本，可用于存放较长的文本内容，例如一篇学术论文。
示例值
"ff"
 开始时间戳(秒)Field ID: fld3e659QaQ11
维格列类型
数字
number
描述
数值，支持负值  通过api调用返回的值，不受列配置里指定的精度影响，只会原样返回。
示例值
8
 结束时间戳(秒)Field ID: flddjkaHotlMJ
维格列类型
数字
number
描述
数值，支持负值  通过api调用返回的值，不受列配置里指定的精度影响，只会原样返回。
示例值
8
 持续时间文本描述Field ID: fldNY5MabI72y
维格列类型
单行文本
string
描述
单行文本，适合保存不带换行符的文本，例如文章的标题。
示例值
单行文本内容
 记录创建时间Field ID: fld1pawhrPs9x
维格列类型
创建时间
number | string
描述
日期和时间，以毫秒(ms)为单位返回时间戳
示例值
1754771710956
 用户Field ID: fldS1LvP1QVhN
维格列类型
单向关联
描述
示例值




获取记录
curl "https://api.vika.cn/fusion/v1/datasheets/dsta8guxXYK3pCYsVe/records?viewId=viw5mLr0sA5d9&fieldKey=id" \
  -H "Authorization: Bearer uskcZUvxWXvLIPXN0hUC6DK"

示例返回值

{
  "code": 200,
  "success": true,
  "message": "Request successful",
  "data": {
    "total": 1,
    "pageNum": 1,
    "pageSize": 1,
    "records": [
      {
        "recordId": "recmjxF1Wi9N3",
        "fields": {
          "fld9Mole3W9eR": "ff",
          "fld0znfZ7H9xM": "ff",
          "fld1pawhrPs9x": 1754771710956
        }
      }
    ]
  }
}

其他参数或提示
/**
 * 注意：每张维格表获取数据最大并发量限制为 1 秒钟 5 次
 * 全部可配置的参数，可通过 URL Query Params 发送，
 */
{
  /**
   * （选填）视图ID。默认为维格表中第一个视图。请求会返回视图中经过视图中筛选/排序后的结果，可以搭配使用fields参数过滤不需要的字段数据
   */
  viewId: 'viewId1',
  /**
   * （选填）指定分页的页码，默认为 1，与参数pageSize配合使用。
   */
  pageNum: 1,
  /**
   * （选填）指定每页返回的记录总数，默认为100。此参数只接受1-1000的整数。
   */
  pageSize: 100,
  /**
   * （选填）对返回的记录进行排序。sort 是由多个排序对象 (sort object) 组成的数组。单个排序对象的结构为 {"order":"asc 或 desc", "field":"字段名称或字段 ID"}。查询示例：sort[][field]=客户名称&sort[][order]=asc，即按照「客户名称」列的字母升序来排列返回的记录。如果 sort 与 viewId 同时使用，则 sort 指定的排序条件将会覆盖视图里的排序条件。
   */
  sort: { field: 'field1', order: 'desc' },
  /**
   * （选填）recordIds 数组。如果附带此参数，则返回参数中指定的records数组。 返回值按照传入数组的顺序排序。此时无视筛选、排序。无分页，每次最多查询 1000 条
   */
  recordIds: ['recordId1', 'recordId2'],
  /**
   * （选填）指定要返回的字段（默认为字段名, 也可以通过 fieldKey 指定为字段 Id）。如果附带此参数，则返回的记录合集将会被过滤，只有指定的字段会返回。
   */
  fields: ['标题', '详情', '引用次数'],
  /**
   * （选填）使用公式作为筛选条件，返回匹配的记录，访问 https://help.vika.cn/docs/guide/tutorial-getting-started-with-formulas 了解公式使用方式
   */
  filterByFormula: '{引用次数} >  0',
  /**
   * （选填）限制返回记录的总数量。如果该值小于表中实际的记录总数，则返回的记录总数会被限制为该值。
   */
  maxRecords: 5000,
  /**
   * （选填）单元格值类型，默认为 'json'，指定为 'string' 时所有值都将被自动转换为 string 格式。
   */
  cellFormat: 'json',
  /**
   * （选填）指定 field 的查询和返回的 key。默认使用列名  'name' 。指定为 'id' 时将以 fieldId 作为查询和返回方式（使用 id 可以避免列名的修改导致代码失效问题）
   */
  fieldKey: 'name',
}


新增记录
curl -X POST "https://api.vika.cn/fusion/v1/datasheets/dsta8guxXYK3pCYsVe/records?viewId=viw5mLr0sA5d9&fieldKey=id"  \
  -H "Authorization: Bearer uskcZUvxWXvLIPXN0hUC6DK" \
  -H "Content-Type: application/json" \
  --data '{
  "records": [
  {
    "fields": {
      "fld9Mole3W9eR": "ff",
      "fld0znfZ7H9xM": "ff"
    }
  }
],
  "fieldKey": "id"
}'

示例返回值
{
  "code": 200,
  "success": true,
  "message": "Request successful",
  "data": {
    "records": [
      {
        "recordId": "recmjxF1Wi9N3",
        "fields": {
          "fld9Mole3W9eR": "ff",
          "fld0znfZ7H9xM": "ff",
          "fld1pawhrPs9x": 1754771710956
        }
      }
    ]
  }
}
其他参数或提示
add 接口接收一个数组值，可以同时创建多条 record，单次请求可最多创建10条 record


更新记录
curl -X PATCH "https://api.vika.cn/fusion/v1/datasheets/dsta8guxXYK3pCYsVe/records?viewId=viw5mLr0sA5d9&fieldKey=id" \
  -H "Authorization: Bearer uskcZUvxWXvLIPXN0hUC6DK" \
  -H "Content-Type: application/json" \
  --data '{
  "records": [
  {
    "recordId": "recmjxF1Wi9N3",
    "fields": {
      "fld9Mole3W9eR": "ff",
      "fld0znfZ7H9xM": "ff"
    }
  }
],
  "fieldKey": "id"
}'
示例返回值
{
  "code": 200,
  "success": true,
  "message": "Request successful",
  "data": {
    "records": [
      {
        "recordId": "recmjxF1Wi9N3",
        "fields": {
          "fld9Mole3W9eR": "ff",
          "fld0znfZ7H9xM": "ff",
          "fld1pawhrPs9x": 1754771710956
        }
      }
    ]
  }
}
其他参数或提示
update 接口接收一个数组值，可以同时更新多条 record，单次请求可最多更新10条 record 特别注意： update 只会更新你传入的 fields 下的数据，未传入的不会影响，如果需要清空值，请显式传入 null


删除记录
curl -X DELETE "https://api.vika.cn/fusion/v1/datasheets/dsta8guxXYK3pCYsVe/records?recordIds=recmjxF1Wi9N3" \
  -H "Authorization: Bearer uskcZUvxWXvLIPXN0hUC6DK"

示例返回值
{
  "code": 200,
  "success": true,
  "message": "请求成功"
}
其他参数或提示
delete 接口接收一个数组值，可以同时删除多条 record，单次请求可最多删除10条 record