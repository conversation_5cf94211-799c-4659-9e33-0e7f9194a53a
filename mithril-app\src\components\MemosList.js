// 使用全局的 Mithril 实例而不是导入
const m = window.m;

// 导入工具函数
import { formatDate } from '../utils/format.js';
import { markdownRenderer } from '../utils/markdown.js';
import { LazyImage } from '../utils/renderOptimizer.js';

export const MemosList = {
    view: function(vnode) {
        if (window.memos.loading && window.memos.list.length === 0) {
            return m('.loading-container', [
                m('.loading-spinner'),
                m('p', '正在加载精彩内容...')
            ]);
        }
        
        if (window.memos.list.length === 0 && !window.memos.loading) {
            return m('.empty-state', [
                m('h3', '还没有任何记录'),
                m('p', '开始记录你的第一个想法吧！')
            ]);
        }
        
        return m('.memos-list', window.memos.list.map((memo, index) => 
            m('.memo-card', {
                key: memo.id || index
            }, [
                m('.memo-header', [
                    m('.memo-time', formatDate(memo.createdTime || new Date().toISOString())),
                    memo.status === '私密' ? m('.memo-status.private', '🔒 私密') : null
                ]),
                // 使用 Markdown 渲染内容
                m('.memo-content.markdown-rendered', {
                    innerHTML: memo.content ? markdownRenderer.render(memo.content) : ''
                }),
                
                // 显示媒体文件（图片和视频）
                (memo.mediaFiles && memo.mediaFiles.length > 0) ? 
                    m('.memo-media', memo.mediaFiles.map((media, mediaIndex) => 
                        media.type === 'image' ?
                            m(LazyImage, {
                                key: mediaIndex,
                                src: media.url || '',
                                alt: '备忘录图片',
                                className: 'memo-image',
                                onclick: function(e) {
                                    // 优化的图片预览功能
                                    const modal = document.createElement('div');
                                    modal.className = 'modal show image-preview-modal';
                                    modal.innerHTML = `
                                        <div class="modal-content image-preview-content">
                                            <span class="close" onclick="this.parentElement.parentElement.remove()">&times;</span>
                                            <div class="image-container">
                                                <div class="image-loading">
                                                    <div class="loading-spinner"></div>
                                                    <p>加载中...</p>
                                                </div>
                                                <img src="${media.url}" alt="图片预览" class="preview-image" style="display: none;">
                                            </div>
                                        </div>
                                    `;
                                    document.body.appendChild(modal);
                                    
                                    // 图片加载处理
                                    const img = modal.querySelector('.preview-image');
                                    const loading = modal.querySelector('.image-loading');
                                    
                                    img.onload = function() {
                                        loading.style.display = 'none';
                                        img.style.display = 'block';
                                        img.style.opacity = '0';
                                        img.style.transition = 'opacity 0.3s ease';
                                        setTimeout(() => {
                                            img.style.opacity = '1';
                                        }, 10);
                                    };
                                    
                                    img.onerror = function() {
                                        loading.innerHTML = '<p style="color: var(--muted);">图片加载失败</p>';
                                    };
                                    
                                    // 点击模态框背景关闭
                                    modal.addEventListener('click', function(e) {
                                        if (e.target === modal) {
                                            modal.remove();
                                        }
                                    });
                                    
                                    // ESC键关闭
                                    const handleEsc = function(e) {
                                        if (e.key === 'Escape') {
                                            modal.remove();
                                            document.removeEventListener('keydown', handleEsc);
                                        }
                                    };
                                    document.addEventListener('keydown', handleEsc);
                                }
                            }) :
                        media.type === 'video' ?
                            m('video', {
                                key: mediaIndex,
                                src: media.url || '',
                                class: 'memo-video',
                                controls: true,
                                preload: 'metadata',
                                onclick: function(e) {
                                    e.stopPropagation(); // 防止触发卡片点击事件
                                }
                            }) :
                            null
                    )) : 
                    // 向后兼容：如果没有 mediaFiles 但有 images
                    (memo.images && memo.images.length > 0) ? 
                        m('.memo-images', memo.images.map((image, imgIndex) => 
                            m(LazyImage, {
                                key: imgIndex,
                                src: image || '',
                                alt: '备忘录图片',
                                className: 'memo-image',
                                onclick: function(e) {
                                // 优化的图片预览功能
                                const modal = document.createElement('div');
                                modal.className = 'modal show image-preview-modal';
                                modal.innerHTML = `
                                    <div class="modal-content image-preview-content">
                                        <span class="close" onclick="this.parentElement.parentElement.remove()">&times;</span>
                                        <div class="image-container">
                                            <div class="image-loading">
                                                <div class="loading-spinner"></div>
                                                <p>加载中...</p>
                                            </div>
                                            <img src="${image}" alt="图片预览" class="preview-image" style="display: none;">
                                        </div>
                                    </div>
                                `;
                                document.body.appendChild(modal);
                                
                                // 图片加载处理
                                const img = modal.querySelector('.preview-image');
                                const loading = modal.querySelector('.image-loading');
                                
                                img.onload = function() {
                                    loading.style.display = 'none';
                                    img.style.display = 'block';
                                    img.style.opacity = '0';
                                    img.style.transition = 'opacity 0.3s ease';
                                    setTimeout(() => {
                                        img.style.opacity = '1';
                                    }, 10);
                                };
                                
                                img.onerror = function() {
                                    loading.innerHTML = '<p style="color: var(--muted);">图片加载失败</p>';
                                };
                                
                                // 点击模态框背景关闭
                                modal.addEventListener('click', function(e) {
                                    if (e.target === modal) {
                                        modal.remove();
                                    }
                                });
                                
                                // ESC键关闭
                                const handleEsc = function(e) {
                                    if (e.key === 'Escape') {
                                        modal.remove();
                                        document.removeEventListener('keydown', handleEsc);
                                    }
                                };
                                document.addEventListener('keydown', handleEsc);
                            }
                        })
                    )) : 
                    null
            ])
        ));
    }
};