// 使用全局的 Mithril 实例而不是导入
const m = window.m;

// 导入工具函数
import { formatDate } from '../utils/format.js';
import { markdownRenderer } from '../utils/markdown.js';
import { LazyImage } from '../utils/renderOptimizer.js';
import videoEnhancer from '../utils/videoEnhancer.js';
import { searchMemos, highlightSearchResults } from '../utils/searchUtils.js';
import { renderTaggedContent } from '../components/TaggedContent.js';

export const MemosList = {
    oninit: function(vnode) {
        this.searchQuery = '';
        this.filteredMemos = [];
        this.updateFilteredMemos();

        // 设置全局标签点击处理函数
        window.handleTagClick = (tag) => this.handleTagClick(tag);
    },

    updateFilteredMemos: function() {
        if (!window.memos || !window.memos.list) {
            this.filteredMemos = [];
            return;
        }

        this.filteredMemos = searchMemos(window.memos.list, this.searchQuery);
    },

    handleSearch: function(query) {
        this.searchQuery = query;
        this.updateFilteredMemos();
        m.redraw();
    },

    handleTagClick: function(tag) {
        this.searchQuery = `#${tag}`;
        this.updateFilteredMemos();
        m.redraw();
    },

    renderContentWithHighlights: function(content) {
        if (!content) return '';

        // 先高亮搜索结果
        let highlightedContent = highlightSearchResults(content, this.searchQuery);

        // 然后处理标签高亮（避免与搜索高亮冲突）
        const tagRegex = /#([^\s#]+)/g;
        highlightedContent = highlightedContent.replace(tagRegex, (match, tag) => {
            // 如果已经被搜索高亮包围，就不再处理
            if (match.includes('<mark')) {
                return match;
            }
            return `<span class="tag-highlight" data-tag="${tag}" onclick="window.handleTagClick('${tag}')">#${tag}</span>`;
        });

        return highlightedContent;
    },

    view: function(vnode) {
        // 更新过滤结果（以防数据变化）
        this.updateFilteredMemos();

        if (window.memos.loading && window.memos.list.length === 0) {
            return m('.loading-container', [
                m('.loading-spinner'),
                m('p', '正在加载精彩内容...')
            ]);
        }

        if (window.memos.list.length === 0 && !window.memos.loading) {
            return m('.empty-state', [
                m('h3', '还没有任何记录'),
                m('p', '开始记录你的第一个想法吧！')
            ]);
        }

        // 显示搜索结果为空的状态
        if (this.searchQuery && this.filteredMemos.length === 0) {
            return m('.empty-search-state', [
                m('h3', '没有找到匹配的记录'),
                m('p', `搜索 "${this.searchQuery}" 没有结果`),
                m('button.btn.btn-secondary', {
                    onclick: () => this.handleSearch('')
                }, '清除搜索')
            ]);
        }

        return m('.memos-list', this.filteredMemos.map((memo, index) =>
            m('.memo-card', {
                key: memo.id || index
            }, [
                m('.memo-header', [
                    m('.memo-time', formatDate(memo.createdTime || new Date().toISOString())),
                    memo.status === '私密' ? m('.memo-status.private', '🔒 私密') : null
                ]),
                // 渲染带标签高亮的内容
                m('.memo-content', [
                    // 如果有搜索查询，先高亮搜索结果，然后处理标签
                    this.searchQuery ?
                        m('.memo-text', {
                            innerHTML: this.renderContentWithHighlights(memo.content)
                        }) :
                        m('.memo-text', renderTaggedContent(memo.content, (tag) => this.handleTagClick(tag)))
                ]),
                
                // 显示媒体文件（图片和视频）
                (memo.mediaFiles && memo.mediaFiles.length > 0) ? 
                    m('.memo-media', memo.mediaFiles.map((media, mediaIndex) => 
                        media.type === 'image' ?
                            m(LazyImage, {
                                key: mediaIndex,
                                src: media.url || '',
                                alt: '备忘录图片',
                                className: 'memo-image',
                                onclick: function(e) {
                                    // 优化的图片预览功能
                                    const modal = document.createElement('div');
                                    modal.className = 'modal show image-preview-modal';
                                    modal.innerHTML = `
                                        <div class="modal-content image-preview-content">
                                            <span class="close" onclick="this.parentElement.parentElement.remove()">&times;</span>
                                            <div class="image-container">
                                                <div class="image-loading">
                                                    <div class="loading-spinner"></div>
                                                    <p>加载中...</p>
                                                </div>
                                                <img src="${media.url}" alt="图片预览" class="preview-image" style="display: none;">
                                            </div>
                                        </div>
                                    `;
                                    document.body.appendChild(modal);
                                    
                                    // 图片加载处理
                                    const img = modal.querySelector('.preview-image');
                                    const loading = modal.querySelector('.image-loading');
                                    
                                    img.onload = function() {
                                        loading.style.display = 'none';
                                        img.style.display = 'block';
                                        img.style.opacity = '0';
                                        img.style.transition = 'opacity 0.3s ease';
                                        setTimeout(() => {
                                            img.style.opacity = '1';
                                        }, 10);
                                    };
                                    
                                    img.onerror = function() {
                                        loading.innerHTML = '<p style="color: var(--muted);">图片加载失败</p>';
                                    };
                                    
                                    // 点击模态框背景关闭
                                    modal.addEventListener('click', function(e) {
                                        if (e.target === modal) {
                                            modal.remove();
                                        }
                                    });
                                    
                                    // ESC键关闭
                                    const handleEsc = function(e) {
                                        if (e.key === 'Escape') {
                                            modal.remove();
                                            document.removeEventListener('keydown', handleEsc);
                                        }
                                    };
                                    document.addEventListener('keydown', handleEsc);
                                }
                            }) :
                        media.type === 'video' ?
                            m('video', {
                                key: mediaIndex,
                                src: media.url || '',
                                class: 'memo-video',
                                controls: true,
                                preload: 'metadata',
                                onclick: function(e) {
                                    e.stopPropagation(); // 防止触发卡片点击事件
                                },
                                oncreate: function(vnode) {
                                    // 为视频添加放大播放功能
                                    videoEnhancer.enhanceVideo(vnode.dom);
                                }
                            }) :
                            null
                    )) : 
                    // 向后兼容：如果没有 mediaFiles 但有 images
                    (memo.images && memo.images.length > 0) ? 
                        m('.memo-images', memo.images.map((image, imgIndex) => 
                            m(LazyImage, {
                                key: imgIndex,
                                src: image || '',
                                alt: '备忘录图片',
                                className: 'memo-image',
                                onclick: function(e) {
                                // 优化的图片预览功能
                                const modal = document.createElement('div');
                                modal.className = 'modal show image-preview-modal';
                                modal.innerHTML = `
                                    <div class="modal-content image-preview-content">
                                        <span class="close" onclick="this.parentElement.parentElement.remove()">&times;</span>
                                        <div class="image-container">
                                            <div class="image-loading">
                                                <div class="loading-spinner"></div>
                                                <p>加载中...</p>
                                            </div>
                                            <img src="${image}" alt="图片预览" class="preview-image" style="display: none;">
                                        </div>
                                    </div>
                                `;
                                document.body.appendChild(modal);
                                
                                // 图片加载处理
                                const img = modal.querySelector('.preview-image');
                                const loading = modal.querySelector('.image-loading');
                                
                                img.onload = function() {
                                    loading.style.display = 'none';
                                    img.style.display = 'block';
                                    img.style.opacity = '0';
                                    img.style.transition = 'opacity 0.3s ease';
                                    setTimeout(() => {
                                        img.style.opacity = '1';
                                    }, 10);
                                };
                                
                                img.onerror = function() {
                                    loading.innerHTML = '<p style="color: var(--muted);">图片加载失败</p>';
                                };
                                
                                // 点击模态框背景关闭
                                modal.addEventListener('click', function(e) {
                                    if (e.target === modal) {
                                        modal.remove();
                                    }
                                });
                                
                                // ESC键关闭
                                const handleEsc = function(e) {
                                    if (e.key === 'Escape') {
                                        modal.remove();
                                        document.removeEventListener('keydown', handleEsc);
                                    }
                                };
                                document.addEventListener('keydown', handleEsc);
                            }
                        })
                    )) : 
                    null
            ])
        ));
    }
};