/**
 * 功能验证工具
 * 用于快速检查 Markdown 功能是否正常工作
 */

async function verifyMarkdownFeatures() {
    console.log('🔍 开始验证 Markdown 功能...\n');
    
    const results = [];
    
    // 1. 验证 Markdown 解析器
    try {
        const { markdownRenderer } = await import('./markdown.js');
        
        const testCases = [
            {
                name: '标题渲染',
                input: '# 测试标题',
                expected: '<h1 id="测试标题" class="heading heading-1">测试标题</h1>'
            },
            {
                name: '粗体渲染',
                input: '**粗体文本**',
                expected: '<strong class="bold">粗体文本</strong>'
            },
            {
                name: '代码块渲染',
                input: '```js\nconsole.log("test");\n```',
                expected: '<pre class="code-block" data-lang="js"><code>console.log("test");</code></pre>'
            },
            {
                name: '链接渲染',
                input: '[链接](https://example.com)',
                expected: '<a href="https://example.com" class="markdown-link" target="_blank" rel="noopener noreferrer">链接</a>'
            },
            {
                name: '表格渲染',
                input: '| 列1 | 列2 |\n|-----|-----|\n| 数据1 | 数据2 |',
                expected: '<table class="markdown-table">'
            },
            {
                name: '引用渲染',
                input: '> 引用文本',
                expected: '<blockquote'
            }
        ];
        
        let passed = 0;
        for (const testCase of testCases) {
            const result = markdownRenderer.render(testCase.input);
            const success = result.includes(testCase.expected.split('>')[0]);
            
            console.log(`${success ? '✅' : '❌'} ${testCase.name}: ${success ? '通过' : '失败'}`);
            if (success) passed++;
        }
        
        results.push({
            category: 'Markdown 解析器',
            passed,
            total: testCases.length,
            success: passed === testCases.length
        });
        
    } catch (error) {
        console.error('❌ Markdown 解析器加载失败:', error);
        results.push({
            category: 'Markdown 解析器',
            passed: 0,
            total: 1,
            success: false,
            error: error.message
        });
    }
    
    // 2. 验证编辑器组件
    try {
        const { MarkdownEditor } = await import('../components/MarkdownEditor.js');
        
        if (MarkdownEditor && typeof MarkdownEditor.view === 'function') {
            console.log('✅ Markdown 编辑器组件: 通过');
            results.push({
                category: 'Markdown 编辑器',
                passed: 1,
                total: 1,
                success: true
            });
        } else {
            throw new Error('编辑器组件结构异常');
        }
        
    } catch (error) {
        console.error('❌ Markdown 编辑器组件加载失败:', error);
        results.push({
            category: 'Markdown 编辑器',
            passed: 0,
            total: 1,
            success: false,
            error: error.message
        });
    }
    
    // 3. 验证性能优化组件
    try {
        const { LazyImage, debounce } = await import('./renderOptimizer.js');
        
        let passed = 0;
        const total = 2;
        
        if (LazyImage && typeof LazyImage.view === 'function') {
            console.log('✅ 懒加载图片组件: 通过');
            passed++;
        } else {
            console.log('❌ 懒加载图片组件: 失败');
        }
        
        if (typeof debounce === 'function') {
            console.log('✅ 防抖函数: 通过');
            passed++;
        } else {
            console.log('❌ 防抖函数: 失败');
        }
        
        results.push({
            category: '性能优化组件',
            passed,
            total,
            success: passed === total
        });
        
    } catch (error) {
        console.error('❌ 性能优化组件加载失败:', error);
        results.push({
            category: '性能优化组件',
            passed: 0,
            total: 2,
            success: false,
            error: error.message
        });
    }
    
    // 4. 验证样式文件
    const styleSheets = [
        'styles.css',
        'src/styles/markdown.css',
        'src/styles/optimized.css'
    ];
    
    let stylesLoaded = 0;
    for (const sheet of styleSheets) {
        const link = document.querySelector(`link[href="${sheet}"]`);
        if (link) {
            console.log(`✅ 样式文件 ${sheet}: 已加载`);
            stylesLoaded++;
        } else {
            console.log(`❌ 样式文件 ${sheet}: 未找到`);
        }
    }
    
    results.push({
        category: '样式文件',
        passed: stylesLoaded,
        total: styleSheets.length,
        success: stylesLoaded === styleSheets.length
    });
    
    // 输出总结
    console.log('\n📊 验证结果总结:');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    
    let totalPassed = 0;
    let totalTests = 0;
    
    results.forEach(result => {
        const status = result.success ? '✅ 通过' : '❌ 失败';
        const score = `${result.passed}/${result.total}`;
        console.log(`${result.category.padEnd(20)} ${status} (${score})`);
        
        if (result.error) {
            console.log(`   错误: ${result.error}`);
        }
        
        totalPassed += result.passed;
        totalTests += result.total;
    });
    
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    
    const overallSuccess = totalPassed === totalTests;
    const percentage = Math.round((totalPassed / totalTests) * 100);
    
    console.log(`总体结果: ${overallSuccess ? '✅ 全部通过' : '⚠️ 部分失败'} (${totalPassed}/${totalTests} - ${percentage}%)`);
    
    if (overallSuccess) {
        console.log('\n🎉 所有功能验证通过！Markdown 增强版已准备就绪！');
    } else {
        console.log('\n⚠️ 部分功能验证失败，请检查相关组件。');
    }
    
    return {
        success: overallSuccess,
        results,
        score: { passed: totalPassed, total: totalTests, percentage }
    };
}

// 验证浏览器兼容性
function verifyBrowserCompatibility() {
    console.log('\n🌐 浏览器兼容性检查:');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    
    const features = [
        { name: 'ES6 Modules', check: () => 'import' in document.createElement('script') },
        { name: 'Intersection Observer', check: () => 'IntersectionObserver' in window },
        { name: 'Fetch API', check: () => 'fetch' in window },
        { name: 'Promise', check: () => 'Promise' in window },
        { name: 'Map/Set', check: () => 'Map' in window && 'Set' in window },
        { name: 'requestAnimationFrame', check: () => 'requestAnimationFrame' in window },
        { name: 'CSS Grid', check: () => CSS.supports('display', 'grid') },
        { name: 'CSS Flexbox', check: () => CSS.supports('display', 'flex') }
    ];
    
    let supported = 0;
    features.forEach(({ name, check }) => {
        const isSupported = check();
        console.log(`${isSupported ? '✅' : '❌'} ${name}: ${isSupported ? '支持' : '不支持'}`);
        if (isSupported) supported++;
    });
    
    const compatibility = Math.round((supported / features.length) * 100);
    console.log(`\n浏览器兼容性: ${compatibility}% (${supported}/${features.length})`);
    
    if (compatibility >= 90) {
        console.log('🎉 浏览器完全兼容！');
    } else if (compatibility >= 70) {
        console.log('⚠️ 浏览器基本兼容，部分功能可能受限。');
    } else {
        console.log('❌ 浏览器兼容性较差，建议升级浏览器。');
    }
    
    return { compatibility, supported, total: features.length };
}

// 主验证函数
async function verify() {
    console.log('🔍 当记 Markdown 增强版 - 功能验证\n');
    
    const markdownResults = await verifyMarkdownFeatures();
    const browserResults = verifyBrowserCompatibility();
    
    console.log('\n🏁 验证完成！');
    
    return {
        markdown: markdownResults,
        browser: browserResults,
        overall: markdownResults.success && browserResults.compatibility >= 80
    };
}

export { verify, verifyMarkdownFeatures, verifyBrowserCompatibility };