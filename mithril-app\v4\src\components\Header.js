// 使用全局的 Mithril 实例而不是导入
const m = window.m;

export const Header = {
    view: function(vnode) {
        return m('header', [
            m('.container', [
                m('.header-content', [
                    m('.header-left', [
                        m(m.route.Link, { 
                            href: '/',
                            class: 'header-title' + (m.route.get() === '/' ? ' active' : '')
                        }, '当记'),
                        m('nav', [
                            m(m.route.Link, { 
                                href: '/time-records',
                                class: 'nav-link' + (m.route.get() === '/time-records' ? ' active' : '')
                            }, '时间')
                        ])
                    ]),
                    m('.header-right', [
                        window.auth.isAuthenticated ? 
                            m('button.btn.btn-secondary.btn-sm', {
                                onclick: () => window.auth.logout()
                            }, `退出 (${window.auth.currentUser.nickname})`) : 
                            m('button.btn.btn-primary.btn-sm', {
                                onclick: () => window.appState.toggleLoginModal(true)
                            }, '登录')
                    ])
                ])
            ])
        ]);
    }
};