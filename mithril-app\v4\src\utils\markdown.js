/**
 * 轻量级 Markdown 解析器和渲染器
 * 支持常用的 Markdown 语法，针对性能优化
 */

export class MarkdownRenderer {
    constructor() {
        // 缓存已解析的内容，提高性能
        this.cache = new Map();
        this.maxCacheSize = 100;
        
        // 预编译正则表达式，提高性能
        this.patterns = {
            // 标题
            heading: /^(#{1,6})\s+(.+)$/gm,
            // 粗体
            bold: /\*\*(.*?)\*\*/g,
            // 斜体
            italic: /\*(.*?)\*/g,
            // 删除线
            strikethrough: /~~(.*?)~~/g,
            // 行内代码
            inlineCode: /`([^`]+)`/g,
            // 代码块
            codeBlock: /```(\w*)\n([\s\S]*?)```/g,
            // 链接
            link: /\[([^\]]+)\]\(([^)]+)\)/g,
            // 图片
            image: /!\[([^\]]*)\]\(([^)]+)\)/g,
            // 无序列表
            unorderedList: /^[\s]*[-*+]\s+(.+)$/gm,
            // 有序列表
            orderedList: /^[\s]*\d+\.\s+(.+)$/gm,
            // 引用
            blockquote: /^>\s+(.+)$/gm,
            // 分割线
            hr: /^---+$/gm,
            // 表格
            table: /^\|(.+)\|\s*\n\|[-\s|:]+\|\s*\n((?:\|.+\|\s*\n?)*)/gm,
            // 换行
            lineBreak: /\n/g,
            // 段落
            paragraph: /^(?!<[h1-6]|<ul|<ol|<blockquote|<pre|<hr)(.+)$/gm
        };
    }

    /**
     * 渲染 Markdown 文本为 HTML
     * @param {string} text - Markdown 文本
     * @param {Object} options - 渲染选项
     * @returns {string} HTML 字符串
     */
    render(text, options = {}) {
        if (!text || typeof text !== 'string') return '';
        
        // 检查缓存
        const cacheKey = text + JSON.stringify(options);
        if (this.cache.has(cacheKey)) {
            return this.cache.get(cacheKey);
        }

        let html = this.escapeHtml(text);
        
        // 按顺序应用转换规则
        html = this.renderCodeBlocks(html);
        html = this.renderTables(html);
        html = this.renderHeadings(html);
        html = this.renderLists(html);
        html = this.renderBlockquotes(html);
        html = this.renderHorizontalRules(html);
        html = this.renderInlineElements(html);
        html = this.renderParagraphs(html);
        
        // 缓存结果
        this.cacheResult(cacheKey, html);
        
        return html;
    }

    /**
     * 转义 HTML 特殊字符
     */
    escapeHtml(text) {
        const htmlEscapes = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#39;'
        };
        return text.replace(/[&<>"']/g, char => htmlEscapes[char]);
    }

    /**
     * 渲染代码块
     */
    renderCodeBlocks(text) {
        return text.replace(this.patterns.codeBlock, (match, lang, code) => {
            const language = lang || 'text';
            const escapedCode = code.trim();
            return `<pre class="code-block" data-lang="${language}"><code>${escapedCode}</code></pre>`;
        });
    }

    /**
     * 渲染标题
     */
    renderHeadings(text) {
        return text.replace(this.patterns.heading, (match, hashes, content) => {
            const level = hashes.length;
            const id = this.generateId(content);
            return `<h${level} id="${id}" class="heading heading-${level}">${content.trim()}</h${level}>`;
        });
    }

    /**
     * 渲染列表
     */
    renderLists(text) {
        // 处理无序列表
        text = text.replace(/^([\s]*[-*+]\s+.+(\n[\s]*[-*+]\s+.+)*)/gm, (match) => {
            const items = match.split('\n').map(line => {
                const itemMatch = line.match(/^[\s]*[-*+]\s+(.+)$/);
                return itemMatch ? `<li>${itemMatch[1]}</li>` : '';
            }).filter(item => item).join('');
            return `<ul class="list list-unordered">${items}</ul>`;
        });

        // 处理有序列表
        text = text.replace(/^([\s]*\d+\.\s+.+(\n[\s]*\d+\.\s+.+)*)/gm, (match) => {
            const items = match.split('\n').map(line => {
                const itemMatch = line.match(/^[\s]*\d+\.\s+(.+)$/);
                return itemMatch ? `<li>${itemMatch[1]}</li>` : '';
            }).filter(item => item).join('');
            return `<ol class="list list-ordered">${items}</ol>`;
        });

        return text;
    }

    /**
     * 渲染引用
     */
    renderBlockquotes(text) {
        // 匹配连续的引用行
        return text.replace(/^(>\s*.+(\n>\s*.*)*)/gm, (match) => {
            // 移除每行开头的 > 符号和空格，保留内容
            const content = match.replace(/^>\s*/gm, '').trim();
            return `<blockquote>${content}</blockquote>`;
        });
    }

    /**
     * 渲染分割线
     */
    renderHorizontalRules(text) {
        return text.replace(this.patterns.hr, '<hr class="horizontal-rule">');
    }

    /**
     * 渲染表格
     */
    renderTables(text) {
        return text.replace(this.patterns.table, (match, headerRow, bodyRows) => {
            // 解析表头
            const headers = headerRow.split('|').map(h => h.trim()).filter(h => h);
            const headerHtml = headers.map(header => `<th>${header}</th>`).join('');
            
            // 解析表格行
            const rows = bodyRows.trim().split('\n').filter(row => row.trim());
            const bodyHtml = rows.map(row => {
                const cells = row.split('|').map(c => c.trim()).filter(c => c);
                const cellsHtml = cells.map(cell => `<td>${cell}</td>`).join('');
                return `<tr>${cellsHtml}</tr>`;
            }).join('');
            
            return `<table class="markdown-table">
                <thead><tr>${headerHtml}</tr></thead>
                <tbody>${bodyHtml}</tbody>
            </table>`;
        });
    }

    /**
     * 渲染行内元素
     */
    renderInlineElements(text) {
        // 图片（必须在链接之前处理）
        text = text.replace(this.patterns.image, (match, alt, src) => {
            return `<img src="${src}" alt="${alt}" class="markdown-image" loading="lazy">`;
        });

        // 链接
        text = text.replace(this.patterns.link, (match, text, url) => {
            const isExternal = !url.startsWith('#') && !url.startsWith('/');
            const target = isExternal ? ' target="_blank" rel="noopener noreferrer"' : '';
            return `<a href="${url}" class="markdown-link"${target}>${text}</a>`;
        });

        // 行内代码
        text = text.replace(this.patterns.inlineCode, '<code class="inline-code">$1</code>');

        // 粗体
        text = text.replace(this.patterns.bold, '<strong class="bold">$1</strong>');

        // 斜体
        text = text.replace(this.patterns.italic, '<em class="italic">$1</em>');

        // 删除线
        text = text.replace(this.patterns.strikethrough, '<del class="strikethrough">$1</del>');

        return text;
    }

    /**
     * 渲染段落
     */
    renderParagraphs(text) {
        // 分割成行
        const lines = text.split('\n');
        const result = [];
        let currentParagraph = [];

        for (const line of lines) {
            const trimmedLine = line.trim();
            
            // 空行或已经是HTML标签的行
            if (!trimmedLine || trimmedLine.startsWith('<')) {
                if (currentParagraph.length > 0) {
                    result.push(`<p class="paragraph">${currentParagraph.join(' ')}</p>`);
                    currentParagraph = [];
                }
                if (trimmedLine.startsWith('<')) {
                    result.push(trimmedLine);
                }
            } else {
                currentParagraph.push(trimmedLine);
            }
        }

        // 处理最后一个段落
        if (currentParagraph.length > 0) {
            result.push(`<p class="paragraph">${currentParagraph.join(' ')}</p>`);
        }

        return result.join('\n');
    }

    /**
     * 生成标题ID
     */
    generateId(text) {
        return text.toLowerCase()
            .replace(/[^\w\s-]/g, '')
            .replace(/\s+/g, '-')
            .trim();
    }

    /**
     * 缓存结果
     */
    cacheResult(key, value) {
        if (this.cache.size >= this.maxCacheSize) {
            // 删除最旧的缓存项
            const firstKey = this.cache.keys().next().value;
            this.cache.delete(firstKey);
        }
        this.cache.set(key, value);
    }

    /**
     * 清除缓存
     */
    clearCache() {
        this.cache.clear();
    }

    /**
     * 获取纯文本内容（用于搜索等）
     */
    getPlainText(markdown) {
        if (!markdown) return '';
        
        return markdown
            .replace(this.patterns.codeBlock, '$2')
            .replace(this.patterns.heading, '$2')
            .replace(this.patterns.bold, '$1')
            .replace(this.patterns.italic, '$1')
            .replace(this.patterns.strikethrough, '$1')
            .replace(this.patterns.inlineCode, '$1')
            .replace(this.patterns.link, '$1')
            .replace(this.patterns.image, '$1')
            .replace(this.patterns.unorderedList, '$1')
            .replace(this.patterns.orderedList, '$1')
            .replace(this.patterns.blockquote, '$1')
            .replace(this.patterns.hr, '')
            .replace(/\n+/g, ' ')
            .trim();
    }
}

// 创建全局实例
export const markdownRenderer = new MarkdownRenderer();

/**
 * Markdown 输入辅助工具
 */
export class MarkdownHelper {
    /**
     * 在文本中插入 Markdown 语法
     */
    static insertMarkdown(textarea, before, after = '', placeholder = '') {
        const start = textarea.selectionStart;
        const end = textarea.selectionEnd;
        const selectedText = textarea.value.substring(start, end);
        const replacement = before + (selectedText || placeholder) + after;
        
        textarea.value = textarea.value.substring(0, start) + replacement + textarea.value.substring(end);
        
        // 设置光标位置
        const newCursorPos = selectedText ? start + replacement.length : start + before.length;
        textarea.setSelectionRange(newCursorPos, newCursorPos);
        textarea.focus();
        
        // 触发输入事件
        textarea.dispatchEvent(new Event('input', { bubbles: true }));
    }

    /**
     * 获取常用的 Markdown 工具按钮配置
     */
    static getToolbarButtons() {
        return [
            {
                name: 'bold',
                icon: '𝐁',
                title: '粗体',
                action: (textarea) => this.insertMarkdown(textarea, '**', '**', '粗体文本')
            },
            {
                name: 'italic',
                icon: '𝐼',
                title: '斜体',
                action: (textarea) => this.insertMarkdown(textarea, '*', '*', '斜体文本')
            },
            {
                name: 'strikethrough',
                icon: '̶S̶',
                title: '删除线',
                action: (textarea) => this.insertMarkdown(textarea, '~~', '~~', '删除线文本')
            },
            {
                name: 'code',
                icon: '</>', 
                title: '行内代码',
                action: (textarea) => this.insertMarkdown(textarea, '`', '`', '代码')
            },
            {
                name: 'codeblock',
                icon: '{ }',
                title: '代码块',
                action: (textarea) => this.insertMarkdown(textarea, '```\n', '\n```', '代码块')
            },
            {
                name: 'link',
                icon: '🔗',
                title: '链接',
                action: (textarea) => this.insertMarkdown(textarea, '[', '](url)', '链接文本')
            },
            {
                name: 'heading',
                icon: 'H',
                title: '标题',
                action: (textarea) => this.insertMarkdown(textarea, '## ', '', '标题文本')
            },
            {
                name: 'list',
                icon: '•',
                title: '列表',
                action: (textarea) => this.insertMarkdown(textarea, '- ', '', '列表项')
            },
            {
                name: 'quote',
                icon: '"',
                title: '引用',
                action: (textarea) => this.insertMarkdown(textarea, '> ', '', '引用文本')
            },
            {
                name: 'table',
                icon: '⊞',
                title: '表格',
                action: (textarea) => {
                    const tableTemplate = `| 列1 | 列2 | 列3 |
|-----|-----|-----|
| 数据1 | 数据2 | 数据3 |
| 数据4 | 数据5 | 数据6 |`;
                    this.insertMarkdown(textarea, tableTemplate, '', '')
                }
            }
        ];
    }
}