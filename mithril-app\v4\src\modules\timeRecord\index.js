/**
 * 时间记录模块入口文件
 * 统一导出时间记录模块的所有组件、模型和服务
 */

// 导入模型
import { TimeRecord } from './models/TimeRecord.js';

// 导入服务
import { TimeRecordService } from './services/TimeRecordService.js';

// 导入组件
import { TimeRecordPage } from './components/TimeRecordPage.js';

// 导入配置
import { 
    TIME_RECORD_FIELDS,
    TIME_RECORD_API_CONFIG,
    TIME_RECORD_CONFIG,
    TIME_FORMAT_CONFIG,
    TIME_RECORD_ERRORS,
    TIME_RECORD_MESSAGES
} from '../../config/timeRecordConfig.js';

// 导出所有模块组件
export {
    // 模型
    TimeRecord,
    
    // 服务
    TimeRecordService,
    
    // 组件
    TimeRecordPage,
    
    // 配置
    TIME_RECORD_FIELDS,
    TIME_RECORD_API_CONFIG,
    TIME_RECORD_CONFIG,
    TIME_FORMAT_CONFIG,
    TIME_RECORD_ERRORS,
    TIME_RECORD_MESSAGES
};

// 创建模块实例的工厂函数
export function createTimeRecordModule() {
    const service = new TimeRecordService();
    const model = new TimeRecord(service);
    
    return {
        model,
        service,
        component: TimeRecordPage
    };
}

// 模块信息
export const MODULE_INFO = {
    name: 'timeRecord',
    version: '1.0.0',
    description: '时间记录模块',
    dependencies: ['mithril'],
    routes: {
        main: '/time-records'
    }
};