/**
 * 时间记录API服务
 * 处理与vika表的数据交互
 */

import { 
    TIME_RECORD_API_CONFIG, 
    TIME_RECORD_FIELDS,
    TIME_RECORD_ERRORS,
    TIME_RECORD_CONFIG
} from '../../../config/timeRecordConfig.js';
import { ErrorUtils } from '../utils/ErrorHandler.js';
import { ValidationUtils } from '../utils/ValidationUtils.js';
import { apiCache } from '../../../utils/SimpleCache.js';
import { apiBatchProcessor } from '../utils/ApiBatchProcessor.js';

export class TimeRecordService {
    constructor() {
        this.baseURL = TIME_RECORD_API_CONFIG.BASE_URL;
        this.datasheetId = TIME_RECORD_API_CONFIG.DATASHEET_ID;
        this.token = TIME_RECORD_API_CONFIG.TOKEN;
        this.viewId = TIME_RECORD_API_CONFIG.VIEW_ID;
        this.fieldKey = TIME_RECORD_API_CONFIG.FIELD_KEY;
        
        // 请求限流管理 (每秒最多5次请求)
        this.requestQueue = [];
        this.isProcessingQueue = false;
        this.requestTimes = []; // 记录最近的请求时间
        this.maxRequests = TIME_RECORD_API_CONFIG.RATE_LIMIT.MAX_REQUESTS;
        this.timeWindow = TIME_RECORD_API_CONFIG.RATE_LIMIT.TIME_WINDOW;
    }
    
    // 获取API请求的基础配置
    getRequestConfig() {
        return {
            headers: {
                'Authorization': `Bearer ${this.token}`,
                'Content-Type': 'application/json'
            }
        };
    }
    
    // 构建API URL
    buildURL(endpoint, params = {}) {
        const url = new URL(`${this.baseURL}/datasheets/${this.datasheetId}/${endpoint}`);
        
        // 添加默认参数
        url.searchParams.set('viewId', this.viewId);
        url.searchParams.set('fieldKey', this.fieldKey);
        
        // 添加其他参数
        Object.entries(params).forEach(([key, value]) => {
            if (value !== undefined && value !== null) {
                url.searchParams.set(key, value);
            }
        });
        
        return url.toString();
    }
    
    // 检查是否可以发送请求（限流检查）
    canMakeRequest() {
        const now = Date.now();
        // 清理超出时间窗口的请求记录
        this.requestTimes = this.requestTimes.filter(time => now - time < this.timeWindow);
        // 检查是否超过限制
        return this.requestTimes.length < this.maxRequests;
    }
    
    // 记录请求时间
    recordRequest() {
        this.requestTimes.push(Date.now());
    }
    
    // 计算需要等待的时间
    getWaitTime() {
        if (this.requestTimes.length === 0) return 0;
        const oldestRequest = Math.min(...this.requestTimes);
        const waitTime = this.timeWindow - (Date.now() - oldestRequest);
        return Math.max(0, waitTime);
    }
    
    // 执行带限流和重试的请求
    async makeRequest(requestFn, retryOptions = {}) {
        const options = {
            maxAttempts: retryOptions.maxAttempts || TIME_RECORD_CONFIG.RETRY.MAX_ATTEMPTS,
            delay: retryOptions.delay || TIME_RECORD_CONFIG.RETRY.DELAY,
            backoffMultiplier: retryOptions.backoffMultiplier || TIME_RECORD_CONFIG.RETRY.BACKOFF_MULTIPLIER
        };
        
        return new Promise((resolve, reject) => {
            // 将请求添加到队列
            this.requestQueue.push({ 
                requestFn, 
                resolve, 
                reject, 
                retryOptions: options,
                attempts: 0
            });
            // 处理队列
            this.processQueue();
        });
    }
    
    // 处理请求队列
    async processQueue() {
        if (this.isProcessingQueue || this.requestQueue.length === 0) {
            return;
        }
        
        this.isProcessingQueue = true;
        
        while (this.requestQueue.length > 0) {
            if (this.canMakeRequest()) {
                // 可以发送请求
                const queueItem = this.requestQueue.shift();
                const { requestFn, resolve, reject, retryOptions, attempts } = queueItem;
                this.recordRequest();
                
                try {
                    const result = await requestFn();
                    resolve(result);
                } catch (error) {
                    // 检查是否需要重试
                    if (this.shouldRetry(error, attempts, retryOptions)) {
                        // 重新加入队列进行重试
                        queueItem.attempts = attempts + 1;
                        this.requestQueue.unshift(queueItem);
                        
                        // 计算退避延迟
                        const delay = retryOptions.delay * Math.pow(retryOptions.backoffMultiplier, attempts);
                        await new Promise(resolve => setTimeout(resolve, delay));
                    } else {
                        reject(error);
                    }
                }
            } else {
                // 需要等待
                const waitTime = this.getWaitTime();
                await new Promise(resolve => setTimeout(resolve, waitTime + 100)); // 额外等待100ms确保安全
            }
        }
        
        this.isProcessingQueue = false;
    }
    
    // 判断是否应该重试
    shouldRetry(error, attempts, retryOptions) {
        // 超过最大重试次数
        if (attempts >= retryOptions.maxAttempts) {
            return false;
        }
        
        // 检查错误类型是否可重试
        const errorMessage = error.message.toLowerCase();
        
        // 网络错误 - 可重试
        if (errorMessage.includes('network') || 
            errorMessage.includes('fetch') || 
            errorMessage.includes('timeout') ||
            errorMessage.includes('connection')) {
            return true;
        }
        
        // API限流错误 - 可重试
        if (errorMessage.includes('rate limit') || 
            errorMessage.includes('429') ||
            errorMessage.includes('too many requests')) {
            return true;
        }
        
        // 服务器错误 (5xx) - 可重试
        if (errorMessage.includes('500') || 
            errorMessage.includes('502') || 
            errorMessage.includes('503') || 
            errorMessage.includes('504')) {
            return true;
        }
        
        // 其他错误不重试（如验证错误、权限错误等）
        return false;
    }
    
    // 执行HTTP请求的通用方法
    async executeRequest(url, options = {}) {
        const config = {
            ...this.getRequestConfig(),
            ...options,
            timeout: options.timeout || 15000 // 15秒超时
        };
        
        try {
            // 创建带超时的fetch请求
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), config.timeout);
            
            const response = await fetch(url, {
                ...config,
                signal: controller.signal
            });
            
            clearTimeout(timeoutId);
            
            // 尝试解析JSON响应
            let data;
            try {
                data = await response.json();
            } catch (parseError) {
                throw new Error(`响应解析失败: ${parseError.message}`);
            }
            
            // 处理HTTP状态码错误
            if (!response.ok) {
                const errorInfo = this.classifyHttpError(response.status, data);
                throw new Error(errorInfo.message);
            }
            
            // 处理业务逻辑错误
            if (!data.success) {
                throw new Error(data.message || TIME_RECORD_ERRORS.UNKNOWN_ERROR);
            }
            
            return data;
        } catch (error) {
            // 处理请求超时
            if (error.name === 'AbortError') {
                throw new Error('请求超时，请检查网络连接');
            }
            
            // 处理网络连接错误
            if (error.name === 'TypeError' && error.message.includes('fetch')) {
                throw new Error(TIME_RECORD_ERRORS.NETWORK_ERROR);
            }
            
            // 重新抛出已分类的错误
            throw error;
        }
    }
    
    // HTTP错误分类
    classifyHttpError(status, data) {
        const message = data?.message || '';
        
        switch (status) {
            case 400:
                return {
                    type: 'validation',
                    message: message || '请求参数错误',
                    retryable: false
                };
            case 401:
                return {
                    type: 'auth',
                    message: TIME_RECORD_ERRORS.PERMISSION_ERROR,
                    retryable: false
                };
            case 403:
                return {
                    type: 'permission',
                    message: TIME_RECORD_ERRORS.PERMISSION_ERROR,
                    retryable: false
                };
            case 404:
                return {
                    type: 'not_found',
                    message: message || '请求的资源不存在',
                    retryable: false
                };
            case 429:
                return {
                    type: 'rate_limit',
                    message: TIME_RECORD_ERRORS.API_LIMIT_ERROR,
                    retryable: true
                };
            case 500:
            case 502:
            case 503:
            case 504:
                return {
                    type: 'server_error',
                    message: message || '服务器错误，请稍后重试',
                    retryable: true
                };
            default:
                return {
                    type: 'unknown',
                    message: message || `HTTP ${status} 错误`,
                    retryable: status >= 500
                };
        }
    }
    
    // 获取记录列表 - 带缓存和批处理优化
    async getRecords(page = 1, pageSize = 20) {
        try {
            console.log(`开始获取第 ${page} 页记录，每页 ${pageSize} 条`);
            
            // 检查缓存
            const cacheKey = `api_records_${page}_${pageSize}`;
            const cached = apiCache.get(cacheKey);
            if (cached) {
                console.debug(`使用API缓存获取第 ${page} 页记录`);
                return cached;
            }

            const params = {
                pageNum: page,
                pageSize: Math.min(pageSize, 100) // 限制最大页面大小
            };
            
            const url = this.buildURL('records', params);
            console.log('请求URL:', url);
            
            // 使用批处理优化的请求
            const response = await apiBatchProcessor.addRequest(
                'get_records',
                () => this.executeRequest(url),
                1 // 高优先级
            );
            
            console.log('API响应:', response);
            
            // 检查响应是否有效
            if (!response || !response.data) {
                throw new Error('API响应无效：缺少数据字段');
            }
            
            // 标准化API响应数据结构
            if (response.data && response.data.records) {
                response.data.records = response.data.records.map(record => {
                    // 确保记录有正确的ID字段
                    if (record.recordId && !record.id) {
                        record.id = record.recordId;
                    }
                    // 确保fields字段存在
                    if (!record.fields) {
                        record.fields = {};
                    }
                    return record;
                });
            } else {
                // 如果没有records字段，创建空数组
                response.data.records = [];
            }
            
            // 验证API响应数据（只记录警告，不阻止处理）
            try {
                const responseValidation = ValidationUtils.validateApiResponse(response, 'records_list');
                if (!responseValidation.isValid) {
                    console.warn('API响应验证警告:', responseValidation.errors, responseValidation.warnings);
                }
            } catch (validationError) {
                console.warn('验证过程出错，但继续处理:', validationError);
            }
            
            // 转换数据格式
            const records = response.data.records.map(record => {
                try {
                    const transformedRecord = this.transformFromVikaFormat(record);
                    return transformedRecord;
                } catch (transformError) {
                    console.warn(`记录 ${record.recordId || record.id} 转换失败:`, transformError);
                    return null; // 返回null，稍后过滤掉
                }
            }).filter(record => record !== null); // 过滤掉转换失败的记录
            
            const result = {
                success: true,
                data: {
                    records,
                    total: response.data.total || 0,
                    pageNum: response.data.pageNum || page,
                    pageSize: response.data.pageSize || pageSize,
                    hasMore: (response.data.pageNum || page) * (response.data.pageSize || pageSize) < (response.data.total || 0)
                }
            };

            console.log(`成功获取 ${records.length} 条记录`);

            // 缓存结果
            apiCache.set(cacheKey, result, 120000); // 缓存2分钟
            
            return result;
        } catch (error) {
            console.error('获取记录失败:', error);
            // 返回失败结果而不是抛出异常
            return {
                success: false,
                error: error.message || '获取记录失败'
            };
        }
    }
    
    // 创建新记录 - 带批处理优化
    async createRecord(data) {
        try {
            console.log('开始创建记录:', data);
            
            // 使用增强的数据验证
            try {
                const inputValidation = ValidationUtils.validateTimeRecordInput(data);
                if (!inputValidation.isValid) {
                    const messages = ValidationUtils.formatValidationMessages(inputValidation.errors, inputValidation.warnings);
                    throw new Error(`验证失败: ${messages.errorMessage}`);
                }
            } catch (validationError) {
                console.warn('输入验证出错，使用传统验证:', validationError);
            }
            
            // 传统验证（保持兼容性）
            this.validateRecordData(data);
            
            // 转换为vika格式
            const vikaRecord = this.transformToVikaFormat(data);
            console.log('转换后的vika记录:', vikaRecord);
            
            const url = this.buildURL('records');
            const requestBody = {
                records: [vikaRecord],
                fieldKey: this.fieldKey
            };
            
            console.log('创建记录请求:', { url, requestBody });
            
            // 使用批处理优化的请求
            const response = await apiBatchProcessor.addRequest(
                'create_records',
                () => this.executeRequest(url, {
                    method: 'POST',
                    body: JSON.stringify(requestBody)
                }),
                2 // 创建记录优先级较高
            );
            
            console.log('创建记录响应:', response);
            
            // 检查响应是否有效
            if (!response || !response.data) {
                throw new Error('API响应无效：缺少数据字段');
            }
            
            // 标准化API响应数据结构
            if (response.data && response.data.records) {
                response.data.records = response.data.records.map(record => {
                    if (record.recordId && !record.id) {
                        record.id = record.recordId;
                    }
                    if (!record.fields) {
                        record.fields = {};
                    }
                    return record;
                });
            }
            
            // 验证API响应（只记录警告，不阻止处理）
            try {
                const responseValidation = ValidationUtils.validateApiResponse(response, 'record_create');
                if (!responseValidation.isValid) {
                    console.warn('创建记录响应验证警告:', responseValidation.errors, responseValidation.warnings);
                }
            } catch (validationError) {
                console.warn('响应验证出错，但继续处理:', validationError);
            }
            
            // 返回转换后的记录
            if (response.data.records && response.data.records.length > 0) {
                const transformedRecord = this.transformFromVikaFormat(response.data.records[0]);
                console.log('转换后的记录:', transformedRecord);

                // 清理相关缓存，因为数据已更新
                apiCache.deletePattern('api_records_');
                
                return {
                    success: true,
                    data: transformedRecord
                };
            }
            
            // 如果没有返回记录，但响应成功，创建一个基本的记录对象
            if (response.success) {
                const basicRecord = {
                    id: response.data.recordId || Date.now().toString(),
                    ...data,
                    createdAt: new Date()
                };
                
                console.log('使用基本记录对象:', basicRecord);
                
                return {
                    success: true,
                    data: basicRecord
                };
            }
            
            throw new Error('创建记录失败：服务器未返回有效数据');
        } catch (error) {
            console.error('创建记录失败:', error);
            return {
                success: false,
                error: error.message || '创建记录失败'
            };
        }
    }
    
    // 更新记录
    async updateRecord(recordId, data) {
        return ErrorUtils.wrapAsync(async () => {
            // 使用增强的数据验证
            const inputValidation = ValidationUtils.validateTimeRecordInput(data);
            if (!inputValidation.isValid) {
                const messages = ValidationUtils.formatValidationMessages(inputValidation.errors, inputValidation.warnings);
                throw new Error(`验证失败: ${messages.errorMessage}`);
            }
            
            // 传统验证（保持兼容性）
            this.validateRecordData(data);
            
            // 转换为vika格式
            const vikaRecord = this.transformToVikaFormat(data);
            vikaRecord.recordId = recordId;
            
            const url = this.buildURL('records');
            const requestBody = {
                records: [vikaRecord],
                fieldKey: this.fieldKey
            };
            
            // 使用限流机制执行请求
            const response = await this.makeRequest(() => 
                this.executeRequest(url, {
                    method: 'PATCH',
                    body: JSON.stringify(requestBody)
                })
            );
            
            // 验证API响应
            const responseValidation = ValidationUtils.validateApiResponse(response, 'record_update');
            if (!responseValidation.isValid) {
                console.warn('更新记录响应验证警告:', responseValidation.errors, responseValidation.warnings);
                if (responseValidation.errors.length > 0) {
                    throw new Error(`API响应无效: ${responseValidation.errors[0].message}`);
                }
            }
            
            // 返回转换后的记录
            if (response.data.records && response.data.records.length > 0) {
                const transformedRecord = this.transformFromVikaFormat(response.data.records[0]);
                
                // 验证转换后的记录
                const recordValidation = ValidationUtils.validateApiResponse({ data: transformedRecord }, 'record_structure');
                if (!recordValidation.isValid) {
                    console.warn('更新的记录结构验证警告:', recordValidation.warnings);
                }
                
                return {
                    success: true,
                    data: transformedRecord
                };
            }
            
            throw new Error('更新记录失败：服务器未返回记录数据');
        }, { operation: 'updateRecord', recordId, data })();
    }
    
    // 删除记录
    async deleteRecord(recordId) {
        try {
            if (!recordId) {
                throw new Error('验证失败：记录ID不能为空');
            }
            
            const url = this.buildURL('records', { recordIds: recordId });
            
            // 使用限流机制执行请求
            await this.makeRequest(() => 
                this.executeRequest(url, {
                    method: 'DELETE'
                })
            );
            
            return {
                success: true,
                data: { recordId }
            };
        } catch (error) {
            console.error('删除记录失败:', error);
            if (error.message.includes('验证失败')) {
                throw error; // 重新抛出验证错误
            }
            throw new Error(TIME_RECORD_ERRORS.SAVE_ERROR);
        }
    }
    
    // 数据验证方法
    validateRecordData(data) {
        if (!data) {
            throw new Error('验证失败：记录数据不能为空');
        }
        
        // 验证必填字段
        if (!data.content || data.content.trim() === '') {
            throw new Error('验证失败：备注内容不能为空');
        }
        
        if (!data.startTime || !(data.startTime instanceof Date)) {
            throw new Error('验证失败：开始时间必须是有效的日期');
        }
        
        if (!data.endTime || !(data.endTime instanceof Date)) {
            throw new Error('验证失败：结束时间必须是有效的日期');
        }
        
        // 验证时间逻辑
        if (data.endTime <= data.startTime) {
            throw new Error('验证失败：结束时间必须晚于开始时间');
        }
        
        // 验证持续时间（至少1秒）
        const duration = Math.floor((data.endTime.getTime() - data.startTime.getTime()) / 1000);
        if (duration < TIME_RECORD_CONFIG.VALIDATION.MIN_DURATION) {
            throw new Error(`验证失败：记录时长至少为${TIME_RECORD_CONFIG.VALIDATION.MIN_DURATION}秒`);
        }
        
        // 验证内容长度
        if (data.content.length > TIME_RECORD_CONFIG.VALIDATION.MAX_CONTENT_LENGTH) {
            throw new Error(`验证失败：备注内容不能超过${TIME_RECORD_CONFIG.VALIDATION.MAX_CONTENT_LENGTH}个字符`);
        }
        
        // 验证分类长度
        if (data.category && data.category.length > TIME_RECORD_CONFIG.VALIDATION.MAX_CATEGORY_LENGTH) {
            throw new Error(`验证失败：分类名称不能超过${TIME_RECORD_CONFIG.VALIDATION.MAX_CATEGORY_LENGTH}个字符`);
        }
        
        return true;
    }
    
    // 获取用户友好的错误消息
    getUserFriendlyError(error) {
        return globalErrorHandler.getUserFriendlyError(error);
    }
    
    // 数据格式转换方法
    transformToVikaFormat(data) {
        const fields = {};
        
        // 时间记录分类
        if (data.category) {
            fields[TIME_RECORD_FIELDS.CATEGORY] = data.category;
        }
        
        // 备注信息
        if (data.content) {
            fields[TIME_RECORD_FIELDS.CONTENT] = data.content;
        }
        
        // 开始时间戳(秒)
        if (data.startTime) {
            fields[TIME_RECORD_FIELDS.START_TIMESTAMP] = Math.floor(data.startTime.getTime() / 1000);
        }
        
        // 结束时间戳(秒)
        if (data.endTime) {
            fields[TIME_RECORD_FIELDS.END_TIMESTAMP] = Math.floor(data.endTime.getTime() / 1000);
        }
        
        // 持续时间文本描述
        if (data.durationText) {
            fields[TIME_RECORD_FIELDS.DURATION_TEXT] = data.durationText;
        }
        
        return { fields };
    }
    
    transformFromVikaFormat(record) {
        const fields = record.fields || {};
        
        // 计算持续时间（秒）
        const startTimestamp = fields[TIME_RECORD_FIELDS.START_TIMESTAMP];
        const endTimestamp = fields[TIME_RECORD_FIELDS.END_TIMESTAMP];
        const duration = (startTimestamp && endTimestamp) ? endTimestamp - startTimestamp : 0;
        
        return {
            id: record.recordId,
            category: fields[TIME_RECORD_FIELDS.CATEGORY] || '',
            content: fields[TIME_RECORD_FIELDS.CONTENT] || '',
            startTime: startTimestamp ? new Date(startTimestamp * 1000) : null,
            endTime: endTimestamp ? new Date(endTimestamp * 1000) : null,
            duration: duration,
            durationText: fields[TIME_RECORD_FIELDS.DURATION_TEXT] || this.formatDuration(duration),
            createdAt: fields[TIME_RECORD_FIELDS.CREATED_TIME] ? new Date(fields[TIME_RECORD_FIELDS.CREATED_TIME]) : null,
            user: fields[TIME_RECORD_FIELDS.USER] || null
        };
    }
    
    // 格式化持续时间为可读文本
    formatDuration(seconds) {
        if (!seconds || seconds <= 0) return '0秒';
        
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const remainingSeconds = seconds % 60;
        
        const parts = [];
        if (hours > 0) parts.push(`${hours}小时`);
        if (minutes > 0) parts.push(`${minutes}分钟`);
        if (remainingSeconds > 0 || parts.length === 0) parts.push(`${remainingSeconds}秒`);
        
        return parts.join('');
    }

    /**
     * 批量操作方法 - 性能优化
     * 任务: 8.1 - 优化API请求的批量处理
     */

    // 批量创建记录
    async batchCreateRecords(records) {
        return ErrorUtils.wrapAsync(async () => {
            if (!Array.isArray(records) || records.length === 0) {
                throw new Error('批量创建记录：记录数组不能为空');
            }

            // 验证所有记录
            for (const record of records) {
                const inputValidation = ValidationUtils.validateTimeRecordInput(record);
                if (!inputValidation.isValid) {
                    const messages = ValidationUtils.formatValidationMessages(inputValidation.errors, inputValidation.warnings);
                    throw new Error(`批量创建验证失败: ${messages.errorMessage}`);
                }
                this.validateRecordData(record);
            }

            // 使用批处理器处理
            const results = await apiBatchProcessor.batchCreateRecords(records, this);
            
            // 清理相关缓存
            apiCache.deletePattern('api_records_');
            
            return results;
        }, { operation: 'batchCreateRecords', count: records.length })();
    }

    // 批量更新记录
    async batchUpdateRecords(updates) {
        return ErrorUtils.wrapAsync(async () => {
            if (!Array.isArray(updates) || updates.length === 0) {
                throw new Error('批量更新记录：更新数组不能为空');
            }

            // 验证所有更新数据
            for (const update of updates) {
                if (!update.id) {
                    throw new Error('批量更新记录：记录ID不能为空');
                }
                const inputValidation = ValidationUtils.validateTimeRecordInput(update.data);
                if (!inputValidation.isValid) {
                    const messages = ValidationUtils.formatValidationMessages(inputValidation.errors, inputValidation.warnings);
                    throw new Error(`批量更新验证失败: ${messages.errorMessage}`);
                }
                this.validateRecordData(update.data);
            }

            // 使用批处理器处理
            const results = await apiBatchProcessor.batchUpdateRecords(updates, this);
            
            // 清理相关缓存
            apiCache.deletePattern('api_records_');
            
            return results;
        }, { operation: 'batchUpdateRecords', count: updates.length })();
    }

    // 批量删除记录
    async batchDeleteRecords(recordIds) {
        return ErrorUtils.wrapAsync(async () => {
            if (!Array.isArray(recordIds) || recordIds.length === 0) {
                throw new Error('批量删除记录：记录ID数组不能为空');
            }

            // 验证所有ID
            for (const id of recordIds) {
                if (!id) {
                    throw new Error('批量删除记录：记录ID不能为空');
                }
            }

            // 使用批处理器处理
            const results = await apiBatchProcessor.batchDeleteRecords(recordIds, this);
            
            // 清理相关缓存
            apiCache.deletePattern('api_records_');
            
            return results;
        }, { operation: 'batchDeleteRecords', count: recordIds.length })();
    }

    // 批量获取记录
    async batchGetRecords(recordIds) {
        return ErrorUtils.wrapAsync(async () => {
            if (!Array.isArray(recordIds) || recordIds.length === 0) {
                throw new Error('批量获取记录：记录ID数组不能为空');
            }

            // 检查缓存
            const cachedResults = {};
            const uncachedIds = [];
            
            for (const id of recordIds) {
                const cacheKey = `api_record_${id}`;
                const cached = apiCache.get(cacheKey);
                if (cached) {
                    cachedResults[id] = cached;
                } else {
                    uncachedIds.push(id);
                }
            }

            let results = { ...cachedResults };

            // 批量获取未缓存的记录
            if (uncachedIds.length > 0) {
                const batchResults = await apiBatchProcessor.batchGetRecords(uncachedIds, this);
                
                // 处理结果并缓存
                batchResults.forEach((result, index) => {
                    const id = uncachedIds[index];
                    if (result.status === 'fulfilled') {
                        results[id] = result.value;
                        apiCache.set(`api_record_${id}`, result.value, 180000); // 缓存3分钟
                    } else {
                        results[id] = { error: result.reason };
                    }
                });
            }

            return results;
        }, { operation: 'batchGetRecords', count: recordIds.length })();
    }

    /**
     * 获取批处理统计信息
     */
    getBatchStats() {
        return apiBatchProcessor.getStats();
    }

    /**
     * 清理批处理队列
     */
    cleanupBatchProcessor() {
        apiBatchProcessor.cleanup();
    }
}