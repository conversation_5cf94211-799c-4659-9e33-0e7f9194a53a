/* 极简设计系统 */
:root {
    /* 简化的颜色系统 */
    --primary: #000;
    --secondary: #666;
    --muted: #999;
    --border: #e5e5e5;
    --background: #fff;
    --surface: #fafafa;
    
    /* 简化的间距 */
    --space-xs: 0.5rem;
    --space-sm: 1rem;
    --space-md: 1.5rem;
    --space-lg: 2rem;
    --space-xl: 3rem;
    
    /* 字体 */
    --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.5rem;
    
    /* 简化的设计元素 */
    --radius: 8px;
    --border-width: 1px;
    --transition: 0.2s ease;
}

/* 全局重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.5;
    color: var(--primary);
    background: var(--background);
    -webkit-font-smoothing: antialiased;
    /* 全局文本换行设置 */
    word-wrap: break-word;
    overflow-wrap: break-word;
}

/* 通用 blockquote 样式 - 基础样式 */
blockquote {
    position: relative !important;
    margin: var(--space-sm) 0 !important;
    padding: var(--space-sm) var(--space-sm) var(--space-sm) calc(var(--space-sm) + 6px) !important;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%) !important;
    border-radius: var(--radius) !important;
    color: var(--secondary) !important;
    font-style: italic !important;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05) !important;
    border: 1px solid rgba(0, 0, 0, 0.06) !important;
}

blockquote::before {
    content: '' !important;
    position: absolute !important;
    left: 0 !important;
    top: 0 !important;
    bottom: 0 !important;
    width: 4px !important;
    background: linear-gradient(180deg, #3b82f6 0%, #1d4ed8 100%) !important;
    border-radius: 2px 0 0 2px !important;
    box-shadow: 0 0 6px rgba(59, 130, 246, 0.2) !important;
}

/* 布局 */
.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 var(--space-sm);
}

.app {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.main-content {
    flex: 1;
    padding: var(--space-lg) 0;
}

/* 头部 */
header {
    border-bottom: var(--border-width) solid var(--border);
    position: sticky;
    top: 0;
    background: var(--background);
    z-index: 100;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-md) 0;
}

/* 头部标题链接样式 */
.header-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--primary);
    text-decoration: none;
    margin-right: var(--space-md);
    transition: var(--transition);
}

.header-title:hover {
    opacity: 0.8;
}

.header-title.active {
    color: var(--primary);
}

.header-left {
    display: flex;
    align-items: center;
}

.header-left nav {
    display: flex;
    gap: var(--space-sm);
    align-items: center;
}

.nav-link {
    color: var(--secondary);
    text-decoration: none;
    font-size: var(--font-size-base);
    font-weight: 500;
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--radius);
    transition: var(--transition);
}

.nav-link:hover {
    color: var(--primary);
    background: var(--surface);
}

.nav-link.active {
    color: var(--primary);
    background: var(--surface);
}

.header-right {
    display: flex;
    gap: var(--space-xs);
    align-items: center;
}

/* 按钮 */
.btn {
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--radius);
    font-size: var(--font-size-sm);
    font-weight: 500;
    cursor: pointer;
    border: var(--border-width) solid transparent;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    text-decoration: none;
    background: transparent;
}

.btn-primary {
    background: var(--primary);
    color: var(--background);
}

.btn-primary:hover {
    opacity: 0.8;
}

.btn-secondary {
    border-color: var(--border);
    color: var(--primary);
}

.btn-secondary:hover {
    background: var(--surface);
}

.btn-sm {
    padding: 4px var(--space-xs);
    font-size: var(--font-size-sm);
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 登录横幅 */
.login-banner {
    background: var(--surface);
    border: var(--border-width) solid var(--border);
    border-radius: var(--radius);
    padding: var(--space-md);
    margin-bottom: var(--space-lg);
    text-align: center;
}

.login-banner p {
    margin-bottom: var(--space-sm);
    color: var(--secondary);
}

/* 发布区域 */
.post-section {
    background: var(--background);
    border: var(--border-width) solid var(--border);
    border-radius: var(--radius);
    padding: var(--space-md);
    margin-bottom: var(--space-lg);
}

/* 发布区域的输入框 */
.post-section .memo-content {
    width: 100%;
    min-height: 100px;
    padding: var(--space-sm);
    border: var(--border-width) solid var(--border);
    border-radius: var(--radius);
    font-family: inherit;
    font-size: var(--font-size-base);
    resize: vertical;
    margin-bottom: var(--space-sm);
    transition: var(--transition);
    /* 确保输入框内容正确换行 */
    word-wrap: break-word;
    overflow-wrap: break-word;
}

.post-section .memo-content:focus {
    outline: none;
    border-color: var(--primary);
}

.post-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.post-options {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
}

.status-select {
    padding: var(--space-xs) var(--space-sm);
    border: var(--border-width) solid var(--border);
    border-radius: var(--radius);
    font-size: var(--font-size-sm);
    background: var(--background);
    color: var(--primary);
    cursor: pointer;
    transition: var(--transition);
    min-width: 100px;
}

.status-select:focus {
    outline: none;
    border-color: var(--primary);
}

.status-select:hover {
    border-color: var(--secondary);
}

/* 状态选择器选项样式 */
.status-select option {
    padding: var(--space-xs);
    background: var(--background);
    color: var(--primary);
}

/* 压缩选项样式 */
.compression-options {
    display: flex;
    align-items: center;
    gap: var(--space-xs);
    flex-wrap: wrap;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: var(--font-size-sm);
    color: var(--secondary);
    cursor: pointer;
    user-select: none;
}

.checkbox-label input[type="checkbox"] {
    margin: 0;
    cursor: pointer;
}

.compression-mode {
    padding: 4px var(--space-xs);
    border: var(--border-width) solid var(--border);
    border-radius: var(--radius);
    font-size: 11px;
    background: var(--background);
    color: var(--primary);
    cursor: pointer;
    transition: var(--transition);
    min-width: 80px;
}

.compression-mode:focus {
    outline: none;
    border-color: var(--primary);
}

.compression-mode:hover {
    border-color: var(--secondary);
}

.compression-mode option {
    padding: var(--space-xs);
    background: var(--background);
    color: var(--primary);
    font-size: 11px;
}

/* 压缩进度条样式 */
.compression-progress {
    margin: var(--space-sm) 0;
    padding: var(--space-sm);
    background: var(--surface);
    border-radius: var(--radius);
    border: var(--border-width) solid var(--border);
}

.compression-progress .progress-bar {
    background: linear-gradient(90deg, #f3f4f6, #e5e7eb);
}

.compression-progress .progress-fill {
    background: linear-gradient(90deg, #3b82f6, #1d4ed8);
}

.compression-progress .progress-text {
    color: var(--primary);
    font-weight: 500;
}

.file-upload label {
    cursor: pointer;
    color: var(--secondary);
    font-size: var(--font-size-sm);
}

.file-upload input[type="file"] {
    display: none;
}

/* 图片预览区域 */
.upload-preview {
    margin: var(--space-sm) 0;
    padding: var(--space-sm);
    background: var(--surface);
    border-radius: var(--radius);
    border: var(--border-width) solid var(--border);
}

.preview-title {
    font-size: var(--font-size-sm);
    color: var(--secondary);
    margin-bottom: var(--space-sm);
    font-weight: 500;
}

.preview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: var(--space-sm);
}

.preview-item {
    position: relative;
    background: var(--background);
    border-radius: var(--radius);
    border: var(--border-width) solid var(--border);
    overflow: hidden;
}

.preview-image {
    width: 100%;
    height: 80px;
    object-fit: cover;
    display: block;
}

.preview-video {
    width: 100%;
    height: 80px;
    object-fit: cover;
    display: block;
    border-radius: var(--radius);
}

.preview-placeholder {
    width: 100%;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--surface);
    color: var(--muted);
    font-size: var(--font-size-sm);
    border-radius: var(--radius);
}

.remove-btn {
    position: absolute;
    top: 4px;
    right: 4px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.6);
    color: white;
    border: none;
    cursor: pointer;
    font-size: 14px;
    line-height: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.remove-btn:hover {
    background: rgba(255, 0, 0, 0.8);
    transform: scale(1.1);
}

.file-info {
    padding: var(--space-xs);
    background: var(--background);
}

.file-name {
    font-size: 11px;
    color: var(--primary);
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.file-size {
    font-size: 10px;
    color: var(--muted);
}

.file-type {
    font-size: 10px;
    color: var(--muted);
    font-weight: 500;
}

/* 上传进度条 */
.upload-progress {
    margin: var(--space-sm) 0;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: var(--surface);
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: var(--space-xs);
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary), #4ade80);
    border-radius: 3px;
    transition: width 0.3s ease;
}

.progress-text {
    font-size: var(--font-size-sm);
    color: var(--secondary);
    text-align: center;
}

/* 备忘录列表 */
.memos-list {
    display: flex;
    flex-direction: column;
    gap: var(--space-md);
}

.memo-card {
    background: var(--background);
    border: var(--border-width) solid var(--border);
    border-radius: var(--radius);
    padding: var(--space-md);
    transition: var(--transition);
}

.memo-card:hover {
    border-color: var(--primary);
}

.memo-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-sm);
}

.memo-time {
    color: var(--muted);
    font-size: var(--font-size-sm);
}

.memo-status {
    font-size: var(--font-size-sm);
    padding: 2px var(--space-xs);
    border-radius: var(--radius);
    font-weight: 500;
}

.memo-status.private {
    background: rgba(255, 193, 7, 0.1);
    color: #856404;
    border: 1px solid rgba(255, 193, 7, 0.3);
}

/* 备忘录卡片中的内容 */
.memo-card .memo-content {
    color: var(--primary);
    line-height: 1.6;
    /* 修复长链接和长文本换行问题 */
    word-wrap: break-word;
    word-break: break-word;
    overflow-wrap: break-word;
    /* 确保内容不会超出容器 */
    max-width: 100%;
    overflow: hidden;
}

/* Markdown 渲染内容样式 */
.memo-card .memo-content.markdown-rendered {
    white-space: normal; /* 允许 HTML 正常渲染 */
}

/* Markdown 渲染元素样式 */
.memo-card .memo-content h1,
.memo-card .memo-content h2,
.memo-card .memo-content h3,
.memo-card .memo-content h4,
.memo-card .memo-content h5,
.memo-card .memo-content h6 {
    margin: var(--space-sm) 0 var(--space-xs) 0;
    font-weight: 600;
    line-height: 1.3;
}

.memo-card .memo-content h1 { font-size: 1.4em; }
.memo-card .memo-content h2 { font-size: 1.3em; }
.memo-card .memo-content h3 { font-size: 1.2em; }
.memo-card .memo-content h4 { font-size: 1.1em; }
.memo-card .memo-content h5 { font-size: 1em; }
.memo-card .memo-content h6 { font-size: 0.9em; color: var(--secondary); }

.memo-card .memo-content p {
    margin: var(--space-xs) 0;
}

.memo-card .memo-content strong {
    font-weight: 600;
}

.memo-card .memo-content em {
    font-style: italic;
    color: var(--secondary);
}

.memo-card .memo-content del {
    text-decoration: line-through;
    color: var(--muted);
}

.memo-card .memo-content code {
    background: var(--surface);
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    font-size: 0.9em;
    border: 1px solid var(--border);
}

.memo-card .memo-content pre {
    background: var(--surface);
    border: var(--border-width) solid var(--border);
    border-radius: var(--radius);
    padding: var(--space-sm);
    margin: var(--space-xs) 0;
    overflow-x: auto;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    font-size: 0.85em;
    line-height: 1.4;
}

.memo-card .memo-content pre code {
    background: none;
    border: none;
    padding: 0;
}

.memo-card .memo-content blockquote {
    position: relative;
    margin: var(--space-sm) 0;
    padding: var(--space-sm) var(--space-sm) var(--space-sm) calc(var(--space-sm) + 6px);
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: var(--radius);
    color: var(--secondary);
    font-style: italic;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.06);
}

.memo-card .memo-content blockquote::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(180deg, #3b82f6 0%, #1d4ed8 100%);
    border-radius: 2px 0 0 2px;
    box-shadow: 0 0 6px rgba(59, 130, 246, 0.2);
}

.memo-card .memo-content ul,
.memo-card .memo-content ol {
    margin: var(--space-xs) 0;
    padding-left: var(--space-md);
}

.memo-card .memo-content li {
    margin: 2px 0;
}

.memo-card .memo-content hr {
    border: none;
    border-top: 1px solid var(--border);
    margin: var(--space-sm) 0;
}

/* 备忘录中的表格样式 */
.memo-card .memo-content .markdown-table {
    border-collapse: collapse;
    width: 100%;
    margin: var(--space-sm) 0;
    border-radius: var(--radius);
    overflow: hidden;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
    border: 1px solid var(--border);
    background: var(--background);
    font-size: var(--font-size-sm);
}

.memo-card .memo-content .markdown-table th {
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    color: var(--primary);
    font-weight: 600;
    padding: var(--space-xs) var(--space-sm);
    text-align: left;
    border-bottom: 1px solid var(--border);
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.3px;
}

.memo-card .memo-content .markdown-table td {
    padding: var(--space-xs) var(--space-sm);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    color: var(--primary);
    line-height: 1.4;
}

.memo-card .memo-content .markdown-table tbody tr:hover {
    background: var(--surface);
    transition: background-color 0.2s ease;
}

.memo-card .memo-content .markdown-table tbody tr:nth-child(even) {
    background: rgba(248, 250, 252, 0.3);
}

.memo-card .memo-content .markdown-table tbody tr:nth-child(even):hover {
    background: var(--surface);
}

/* 当备忘录有图片时，内容与图片之间的间距 */
.memo-card .memo-content:not(:last-child) {
    margin-bottom: var(--space-xs);
}

.memo-images {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: var(--space-sm);
    margin-top: var(--space-xs);
}

.memo-images img {
    width: 100%;
    height: 150px;
    object-fit: cover;
    border-radius: var(--radius);
    border: var(--border-width) solid var(--border);
}

/* 媒体文件显示区域 */
.memo-media {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: var(--space-sm);
    margin-top: var(--space-xs);
}

.memo-image {
    width: 100%;
    height: 150px;
    object-fit: cover;
    border-radius: var(--radius);
    border: var(--border-width) solid var(--border);
    cursor: pointer;
    transition: var(--transition);
}

.memo-image:hover {
    transform: scale(1.02);
    border-color: var(--primary);
}

.memo-video {
    width: 100%;
    max-height: 300px;
    border-radius: var(--radius);
    border: var(--border-width) solid var(--border);
    background: var(--surface);
    transition: all 0.3s ease;
}

/* 视频放大功能样式 */
.memo-video:focus {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
}

/* 视频放大时的样式 */
.memo-video.video-enlarged {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2) !important;
    border: 2px solid var(--primary) !important;
    border-radius: 12px !important;
    transform: scale(1.01) !important;
    transition: all 0.3s ease !important;
    z-index: 10 !important;
    /* 确保能够使用设定的宽度 */
    max-width: none !important;
}

/* 搜索框样式 */
.search-container {
    margin: var(--space-md) 0;
    position: relative;
}

.search-box {
    position: relative;
    display: flex;
    align-items: center;
    background: var(--surface);
    border: var(--border-width) solid var(--border);
    border-radius: var(--radius);
    transition: var(--transition);
    overflow: hidden;
}

.search-box:focus-within,
.search-box.expanded {
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
}

.search-input {
    flex: 1;
    padding: var(--space-sm) var(--space-md);
    border: none;
    background: transparent;
    font-size: var(--font-size-base);
    color: var(--foreground);
    outline: none;
}

.search-input::placeholder {
    color: var(--muted);
}

.search-icon {
    padding: var(--space-sm);
    color: var(--muted);
    cursor: pointer;
    transition: var(--transition);
}

.search-icon:hover {
    color: var(--primary);
}

.search-clear {
    padding: var(--space-sm);
    color: var(--muted);
    cursor: pointer;
    transition: var(--transition);
    font-size: var(--font-size-sm);
}

.search-clear:hover {
    color: var(--destructive);
}

/* 搜索建议 */
.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--surface);
    border: var(--border-width) solid var(--border);
    border-top: none;
    border-radius: 0 0 var(--radius) var(--radius);
    box-shadow: var(--shadow);
    z-index: 100;
    max-height: 300px;
    overflow-y: auto;
}

.suggestions-section {
    padding: var(--space-sm);
}

.suggestions-title {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--muted);
    margin-bottom: var(--space-xs);
}

.popular-tags {
    display: flex;
    flex-wrap: wrap;
    gap: var(--space-xs);
}

.tag-suggestion {
    display: flex;
    align-items: center;
    gap: var(--space-xs);
    padding: var(--space-xs) var(--space-sm);
    background: var(--muted-background);
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: var(--transition);
    font-size: var(--font-size-sm);
}

.tag-suggestion:hover {
    background: var(--accent);
    color: var(--accent-foreground);
}

.tag-name {
    color: var(--primary);
    font-weight: 500;
}

.tag-count {
    color: var(--muted);
    font-size: var(--font-size-xs);
}

.search-tip {
    padding: var(--space-sm);
    color: var(--muted);
    font-size: var(--font-size-sm);
}

/* 标签高亮样式 */
.tag-highlight {
    display: inline-block;
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark, var(--primary)) 100%);
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.85em;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    margin: 0 2px;
}

.tag-highlight:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(var(--primary-rgb), 0.3);
    background: var(--primary-dark, var(--primary));
}

/* 搜索结果高亮 */
.search-highlight {
    background: yellow;
    color: black;
    padding: 1px 2px;
    border-radius: 2px;
}

/* 空搜索状态 */
.empty-search-state {
    text-align: center;
    padding: var(--space-xl);
    color: var(--muted);
}

.empty-search-state h3 {
    margin-bottom: var(--space-sm);
    color: var(--foreground);
}

.empty-search-state p {
    margin-bottom: var(--space-md);
}

/* 加载更多 */
.load-more-container {
    text-align: center;
    margin: var(--space-lg) 0;
}

.no-more {
    color: var(--muted);
    font-size: var(--font-size-sm);
    text-align: center;
    padding: var(--space-md);
}

/* 页脚 */
.footer {
    border-top: var(--border-width) solid var(--border);
    padding: var(--space-lg) 0;
    text-align: center;
    margin-top: var(--space-xl);
}

.footer p {
    color: var(--muted);
    font-size: var(--font-size-sm);
    margin-bottom: var(--space-xs);
}

.footer a {
    color: var(--primary);
    text-decoration: none;
}

.footer a:hover {
    text-decoration: underline;
}

/* 模态框 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: var(--space-sm);
}

.modal.show {
    display: flex;
}

.modal-content {
    background: var(--background);
    border-radius: var(--radius);
    width: 100%;
    max-width: 400px;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
    border: var(--border-width) solid var(--border);
}

.close {
    position: absolute;
    top: var(--space-sm);
    right: var(--space-sm);
    font-size: var(--font-size-lg);
    cursor: pointer;
    color: var(--muted);
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: none;
    border: none;
}

.close:hover {
    color: var(--primary);
}

/* 表单 */
.login-form {
    padding: var(--space-lg);
}

.login-form h2 {
    text-align: center;
    margin-bottom: var(--space-md);
    color: var(--primary);
    font-size: var(--font-size-xl);
    font-weight: 600;
}

.form-group {
    margin-bottom: var(--space-sm);
}

.form-group label {
    display: block;
    margin-bottom: var(--space-xs);
    font-weight: 500;
    color: var(--primary);
    font-size: var(--font-size-sm);
}

.form-group input {
    width: 100%;
    padding: var(--space-xs);
    border: var(--border-width) solid var(--border);
    border-radius: var(--radius);
    font-family: inherit;
    font-size: var(--font-size-base);
    transition: var(--transition);
}

.form-group input:focus {
    outline: none;
    border-color: var(--primary);
}

.login-form .btn {
    width: 100%;
    margin-top: var(--space-sm);
}

/* 更新日志 */
.changelog-content {
    padding: var(--space-lg);
    max-height: 70vh;
    overflow-y: auto;
}

.changelog-content h2 {
    text-align: center;
    margin-bottom: var(--space-md);
    color: var(--primary);
    font-size: var(--font-size-xl);
    font-weight: 600;
}

.changelog-item {
    margin-bottom: var(--space-md);
    padding: var(--space-sm);
    border-left: 2px solid var(--border);
}

.changelog-item h3 {
    color: var(--primary);
    margin-bottom: var(--space-xs);
    font-size: var(--font-size-lg);
    font-weight: 600;
}

.changelog-item p {
    color: var(--secondary);
    line-height: 1.5;
}

/* 通知 */
.notification {
    position: fixed;
    top: var(--space-sm);
    right: var(--space-sm);
    padding: var(--space-sm);
    border-radius: var(--radius);
    z-index: 2000;
    min-width: 200px;
    transform: translateX(120%);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: var(--border-width) solid;
    font-size: var(--font-size-sm);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    background: #f0f9f0;
    border-color: #4caf50;
    color: #2e7d32;
}

.notification.error {
    background: #fef2f2;
    border-color: #f44336;
    color: #c62828;
}

.notification.info {
    background: #f0f8ff;
    border-color: #2196f3;
    color: #1565c0;
}

/* 状态 */
.loading-container {
    text-align: center;
    padding: var(--space-xl);
    color: var(--muted);
}

.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid var(--border);
    border-top: 2px solid var(--primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: var(--space-sm);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.empty-state {
    text-align: center;
    padding: var(--space-xl);
    color: var(--muted);
}

.empty-state h3 {
    font-size: var(--font-size-lg);
    font-weight: 500;
    margin-bottom: var(--space-xs);
    color: var(--secondary);
}

.empty-state p {
    font-size: var(--font-size-sm);
    color: var(--muted);
}

/* 图片预览模态框 */
.image-preview-modal {
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
}

.image-preview-content {
    background: transparent;
    border: none;
    box-shadow: none;
    max-width: 95vw;
    max-height: 95vh;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.image-container {
    position: relative;
    max-width: 100%;
    max-height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 200px;
}

.image-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
    gap: var(--space-sm);
}

.image-loading p {
    font-size: var(--font-size-sm);
    opacity: 0.8;
}

.preview-image {
    max-width: 90vw;
    max-height: 85vh;
    width: auto;
    height: auto;
    object-fit: contain;
    border-radius: var(--radius);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    background: white;
    /* 确保图片不会超出视口 */
    display: block;
}

/* 图片预览的关闭按钮 */
.image-preview-content .close {
    position: fixed;
    top: var(--space-sm);
    right: var(--space-sm);
    background: rgba(0, 0, 0, 0.6);
    color: white;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    font-size: var(--font-size-lg);
    cursor: pointer;
    z-index: 1001;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
}

.image-preview-content .close:hover {
    background: rgba(0, 0, 0, 0.8);
    transform: scale(1.1);
}

/* 链接样式 */
a {
    color: var(--primary);
    text-decoration: underline;
    /* 确保链接能够正确换行 */
    word-wrap: break-word;
    word-break: break-all;
    overflow-wrap: break-word;
    /* 防止链接过长时超出容器 */
    max-width: 100%;
    display: inline-block;
}

a:hover {
    opacity: 0.8;
}

/* 在备忘录内容中的链接 */
.memo-card .memo-content a {
    color: var(--primary);
    text-decoration: underline;
    /* 更激进的换行策略，确保长链接不会破坏布局 */
    word-break: break-all;
    overflow-wrap: anywhere;
}

/* 长文本和代码块的处理 */
.memo-card .memo-content code,
.memo-card .memo-content pre {
    word-wrap: break-word;
    word-break: break-all;
    overflow-wrap: break-word;
    white-space: pre-wrap;
    max-width: 100%;
    overflow-x: auto;
}

/* 响应式 */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: var(--space-sm);
    }
    
    .post-actions {
        flex-direction: column;
        gap: var(--space-sm);
        align-items: stretch;
    }
    
    .memo-images {
        grid-template-columns: 1fr;
    }
    
    .modal-content {
        width: 95%;
        margin: var(--space-sm);
    }
    
    /* 移动端更严格的文本换行 */
    .memo-card .memo-content {
        word-break: break-all;
        overflow-wrap: anywhere;
        /* 确保在小屏幕上不会有水平滚动 */
        max-width: calc(100vw - 4rem);
    }
    
    /* 移动端链接处理 */
    .memo-card .memo-content a {
        word-break: break-all;
        overflow-wrap: anywhere;
        /* 在移动端显示为块级元素，便于点击 */
        display: inline-block;
        max-width: 100%;
    }
    
    /* 移动端图片预览优化 */
    .preview-image {
        max-width: 95vw;
        max-height: 80vh;
        /* 在移动端确保图片不会太大 */
        object-fit: contain;
    }
    
    .image-preview-content .close {
        top: var(--space-xs);
        right: var(--space-xs);
        width: 36px;
        height: 36px;
        font-size: var(--font-size-base);
    }
    
    /* 移动端图片上传预览优化 */
    .preview-grid {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
        gap: var(--space-xs);
    }
    
    .preview-image {
        height: 60px;
    }
    
    .preview-video {
        height: 60px;
    }
    
    .file-info {
        padding: 4px;
    }
    
    .file-name {
        font-size: 10px;
    }
    
    .file-size {
        font-size: 9px;
    }
    
    .file-type {
        font-size: 9px;
    }
    
    /* 移动端媒体文件显示优化 */
    .memo-media {
        grid-template-columns: 1fr;
    }
    
    .memo-video {
        max-height: 200px;
    }
    
    /* 移动端发布选项优化 */
    .post-options {
        flex-direction: column;
        align-items: stretch;
        gap: var(--space-xs);
    }
    
    .status-select {
        width: 100%;
    }
    
    /* 移动端压缩选项优化 */
    .compression-options {
        flex-direction: column;
        align-items: stretch;
        gap: var(--space-xs);
    }
    
    .compression-mode {
        width: 100%;
        min-width: auto;
    }
    
    /* 移动端备忘录头部优化 */
    .memo-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--space-xs);
    }
    
    .memo-status {
        font-size: 11px;
        padding: 1px 6px;
    }
}