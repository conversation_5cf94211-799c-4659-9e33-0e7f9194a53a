// 使用相对路径导入配置
import { API_CONFIG } from '../config.js';
import { performanceMonitor } from '../utils/performance.js';

/**
 * API服务类，用于处理所有与后端的通信
 */
export class ApiService {
    constructor() {
        this.baseURL = API_CONFIG.baseURL;
        this.token = API_CONFIG.token;
        // 优化请求队列和限流控制
        this.apiQueue = [];
        this.isProcessingQueue = false;
        this.requestDelay = 200; // 减少请求间隔到200ms
        this.maxRetries = 3; // 减少重试次数到3次
        this.maxConcurrentRequests = 3; // 允许并发请求
        this.activeRequests = 0;
        
        // 添加请求缓存
        this.requestCache = new Map();
        this.cacheTimeout = 2 * 60 * 1000; // 2分钟缓存
    }
    
    /**
     * 延迟函数
     * @param {number} ms - 延迟毫秒数
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    /**
     * 处理 API 请求队列（支持并发）
     */
    async processApiQueue() {
        if (this.isProcessingQueue || this.apiQueue.length === 0) return;
        
        this.isProcessingQueue = true;
        
        // 并发处理多个请求
        const promises = [];
        
        while (this.apiQueue.length > 0 && this.activeRequests < this.maxConcurrentRequests) {
            const request = this.apiQueue.shift();
            this.activeRequests++;
            
            promises.push(this.processRequest(request));
        }
        
        if (promises.length > 0) {
            await Promise.allSettled(promises);
        }
        
        this.isProcessingQueue = false;
        
        // 如果还有请求在队列中，继续处理
        if (this.apiQueue.length > 0) {
            setTimeout(() => this.processApiQueue(), this.requestDelay);
        }
    }
    
    /**
     * 处理单个请求
     */
    async processRequest(request) {
        const { resolve, reject, url, config, cacheKey } = request;
        const requestName = `${config.method || 'GET'} ${url.split('/').pop()}`;
        
        try {
            // 开始性能监控
            performanceMonitor.start(requestName);
            
            // 检查缓存
            if (cacheKey && this.requestCache.has(cacheKey)) {
                const cached = this.requestCache.get(cacheKey);
                if (Date.now() - cached.timestamp < this.cacheTimeout) {
                    performanceMonitor.end(requestName);
                    console.log('🎯 使用缓存:', url);
                    resolve(cached.data);
                    return;
                }
                this.requestCache.delete(cacheKey);
            }
            
            // 添加重试计数
            if (!request.retryCount) request.retryCount = 0;
            
            const response = await fetch(url, config);
            
            if (response.status === 429) {
                // 遇到限流，指数退避重试
                if (request.retryCount < this.maxRetries) {
                    request.retryCount++;
                    const backoffDelay = Math.min(500 * Math.pow(2, request.retryCount), 5000); // 最大5秒
                    
                    console.warn(`API限流，${backoffDelay}ms后重试 (${request.retryCount}/${this.maxRetries}):`, url);
                    
                    // 延迟后重新加入队列
                    setTimeout(() => {
                        this.apiQueue.unshift(request);
                        this.processApiQueue();
                    }, backoffDelay);
                    
                    performanceMonitor.end(requestName);
                    return;
                } else {
                    throw new Error(`API请求失败，已达到最大重试次数: ${response.status}`);
                }
            }
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            
            // 缓存GET请求的结果
            if (cacheKey && (!config.method || config.method === 'GET')) {
                this.requestCache.set(cacheKey, {
                    data: data,
                    timestamp: Date.now()
                });
            }
            
            performanceMonitor.end(requestName);
            resolve(data);
            
        } catch (error) {
            performanceMonitor.end(requestName);
            console.error('API请求失败:', error);
            reject(error);
        } finally {
            this.activeRequests--;
        }
    }
    
    /**
     * 发送API请求（带限流、重试和缓存）
     * @param {string} endpoint - API端点
     * @param {Object} options - 请求选项
     * @returns {Promise} API响应
     */
    async fetchAPI(endpoint, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        
        const config = {
            headers: {
                'Authorization': `Bearer ${this.token}`,
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        };

        // 生成缓存键（仅对GET请求）
        const cacheKey = (!config.method || config.method === 'GET') ? 
            `${url}_${JSON.stringify(config.headers)}` : null;

        return new Promise((resolve, reject) => {
            // 添加到请求队列
            this.apiQueue.push({ resolve, reject, url, config, cacheKey });
            
            // 开始处理队列
            this.processApiQueue();
        });
    }

    /**
     * 获取默认请求头
     */
    getDefaultHeaders() {
        const headers = {
            'Authorization': `Bearer ${this.token}`,
            'Content-Type': 'application/json'
        };
        
        if (API_CONFIG.fieldKey) {
            headers['field-key'] = API_CONFIG.fieldKey;
        }
        
        return headers;
    }
    
    /**
     * 发送GET请求
     */
    async get(endpoint, params = {}) {
        try {
            // 构建查询参数
            const url = new URL(`${this.baseURL}${endpoint}`);
            Object.keys(params).forEach(key => {
                if (params[key] !== undefined && params[key] !== null) {
                    // 特殊处理排序参数
                    if (key === 'sort' && Array.isArray(params[key])) {
                        url.searchParams.append(key, JSON.stringify(params[key]));
                    } else if (typeof params[key] === 'object') {
                        // 其他对象类型参数也进行JSON序列化
                        url.searchParams.append(key, JSON.stringify(params[key]));
                    } else {
                        url.searchParams.append(key, params[key]);
                    }
                }
            });
            
            // 使用带限流的fetchAPI替代原生fetch
            const response = await this.fetchAPI(`${endpoint}?${url.searchParams}`);
            return response;
        } catch (error) {
            console.error('GET请求失败:', error);
            // 提供更具体的错误信息
            if (error.name === 'TypeError' && error.message.includes('fetch')) {
                throw new Error('网络请求失败，请检查网络连接或稍后重试。');
            }
            throw error;
        }
    }
    
    /**
     * 发送POST请求
     */
    async post(endpoint, data = {}) {
        try {
            // 使用带限流的fetchAPI替代原生fetch
            const response = await this.fetchAPI(endpoint, {
                method: 'POST',
                body: JSON.stringify(data)
            });
            
            return response;
        } catch (error) {
            console.error('POST请求失败:', error);
            // 提供更具体的错误信息
            if (error.name === 'TypeError' && error.message.includes('fetch')) {
                throw new Error('网络请求失败，请检查网络连接或稍后重试。');
            }
            throw error;
        }
    }
    
    /**
     * 发送PATCH请求
     */
    async patch(endpoint, data = {}) {
        try {
            // 使用带限流的fetchAPI替代原生fetch
            const response = await this.fetchAPI(endpoint, {
                method: 'PATCH',
                body: JSON.stringify(data)
            });
            
            return response;
        } catch (error) {
            console.error('PATCH请求失败:', error);
            // 提供更具体的错误信息
            if (error.name === 'TypeError' && error.message.includes('fetch')) {
                throw new Error('网络请求失败，请检查网络连接或稍后重试。');
            }
            throw error;
        }
    }
    
    /**
     * 发送DELETE请求
     */
    async delete(endpoint) {
        try {
            // 使用带限流的fetchAPI替代原生fetch
            const response = await this.fetchAPI(endpoint, {
                method: 'DELETE'
            });
            
            return response;
        } catch (error) {
            console.error('DELETE请求失败:', error);
            // 提供更具体的错误信息
            if (error.name === 'TypeError' && error.message.includes('fetch')) {
                throw new Error('网络请求失败，请检查网络连接或稍后重试。');
            }
            throw error;
        }
    }
    
    /**
     * 获取Memos列表
     */
    async getMemos(params = {}) {
        const defaultParams = {
            viewId: API_CONFIG.viewId,
            fieldKey: API_CONFIG.fieldKey,
            cellFormat: 'json',
            pageSize: params.pageSize || 10,
            ...params
        };
        
        // 如果有pageNum参数，则使用pageNum而不是offset
        if (params.pageNum) {
            defaultParams.pageNum = params.pageNum;
        }
        
        // 移除offset参数，使用原始代码的分页方式
        delete defaultParams.offset;
        
        // 移除sort参数，使用API默认排序
        delete defaultParams.sort;
        
        return await this.get(`/datasheets/${API_CONFIG.datasheetId}/records`, defaultParams);
    }
    
    /**
     * 获取公开Memos列表（未登录时使用）
     */
    async getPublicMemos(params = {}) {
        const defaultParams = {
            viewId: API_CONFIG.viewId,
            fieldKey: API_CONFIG.fieldKey,
            cellFormat: 'json',
            pageSize: params.pageSize || 10,
            filterByFormula: '{status}="公开"',
            ...params
        };
        
        // 如果有pageNum参数，则使用pageNum而不是offset
        if (params.pageNum) {
            defaultParams.pageNum = params.pageNum;
        }
        
        // 移除offset参数，使用原始代码的分页方式
        delete defaultParams.offset;
        
        // 移除sort参数，使用API默认排序
        delete defaultParams.sort;
        
        return await this.get(`/datasheets/${API_CONFIG.datasheetId}/records`, defaultParams);
    }
    
    /**
     * 创建新的Memo
     */
    async createMemo(memoData) {
        const now = new Date().toISOString();
        
        // 处理文件URL - 将所有文件URL用逗号分隔存储在 picurls 字段
        let picurls = '';
        if (memoData.files && memoData.files.length > 0) {
            picurls = memoData.files.map(file => file.url).join(',');
        } else if (memoData.images && memoData.images.length > 0) {
            // 向后兼容：如果传入的是 images 数组
            picurls = memoData.images.join(',');
        }
        
        const data = {
            records: [{
                fields: {
                    content: memoData.content,
                    picurls: picurls, // 使用 picurls 字段存储所有媒体文件URL
                    tags: memoData.tags || [],
                    user_id: memoData.userId ? [memoData.userId] : null,
                    status: memoData.status || '公开',
                    posttime: memoData.posttime || now,
                    createdAt: now
                }
            }]
        };
        
        return await this.post(`/datasheets/${API_CONFIG.datasheetId}/records`, data);
    }
    
    /**
     * 更新Memo
     */
    async updateMemo(recordId, memoData) {
        // 处理图片URL - 如果有多个图片，用逗号分隔
        const picurls = memoData.images && memoData.images.length > 0 
            ? memoData.images.join(',') 
            : '';
            
        const data = {
            records: [{
                fields: {
                    content: memoData.content,
                    picurls: picurls,
                    tags: memoData.tags || [],
                    updatedAt: new Date().toISOString()
                }
            }]
        };
        
        return await this.patch(`/datasheets/${API_CONFIG.datasheetId}/records/${recordId}`, data);
    }
    
    /**
     * 删除Memo
     */
    async deleteMemo(recordId) {
        return await this.delete(`/datasheets/${API_CONFIG.datasheetId}/records/${recordId}`);
    }
    
    /**
     * 用户登录验证
     */
    async authenticateUser(username, password) {
        const params = {
            viewId: API_CONFIG.userViewId,
            fieldKey: API_CONFIG.fieldKey,
            filterByFormula: `AND({username} = '${username}', {password} = '${password}')`
        };
        
        return await this.get(`/datasheets/${API_CONFIG.userDatasheetId}/records`, params);
    }
    
    /**
     * 获取用户信息
     */
    async getUserInfo(userId) {
        const params = {
            viewId: API_CONFIG.userViewId,
            fieldKey: API_CONFIG.fieldKey,
            filterByFormula: `{userId} = '${userId}'`
        };
        
        return await this.get(`/datasheets/${API_CONFIG.userDatasheetId}/records`, params);
    }
    
    /**
     * 根据用户名查找用户
     */
    async getUserByUsername(username) {
        const params = {
            viewId: API_CONFIG.userViewId,
            fieldKey: API_CONFIG.fieldKey,
            filterByFormula: `{用户名}="${username}"`
        };
        
        return await this.get(`/datasheets/${API_CONFIG.userDatasheetId}/records`, params);
    }
    
    /**
     * 根据用户ID获取用户信息
     */
    async getUserById(userId) {
        return await this.get(`/datasheets/${API_CONFIG.userDatasheetId}/records/${userId}`, {
            fieldKey: API_CONFIG.fieldKey
        });
    }
    
    /**
     * 获取用户列表
     */
    async getUsers(pageSize = 100) {
        const params = {
            viewId: API_CONFIG.userViewId,
            fieldKey: API_CONFIG.fieldKey,
            pageSize: pageSize
        };
        
        return await this.get(`/datasheets/${API_CONFIG.userDatasheetId}/records`, params);
    }
}