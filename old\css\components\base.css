/**

 * 基础样式

 * 重置和全局样式

 */



/* 现代CSS重置 */

*,

*::before,

*::after {

  box-sizing: border-box;

  margin: 0;

  padding: 0;

}



html {

  line-height: 1.5;

  -webkit-text-size-adjust: 100%;

  -moz-tab-size: 4;

  tab-size: 4;

  font-family: var(--font-sans);

  font-feature-settings: normal;

  font-variation-settings: normal;

}



body {

  line-height: inherit;

  background-color: var(--color-bg, var(--color-gray-100));

  color: var(--color-text, var(--color-gray-900));

  transition: background-color var(--duration-normal) var(--ease-in-out),

              color var(--duration-normal) var(--ease-in-out);

}



/* 滚动条样式 */

::-webkit-scrollbar {

  width: 8px;

  height: 8px;

}



::-webkit-scrollbar-track {

  background: var(--color-bg-secondary, var(--color-gray-100));

}



::-webkit-scrollbar-thumb {

  background: var(--color-gray-400);

  border-radius: var(--radius-full);

}



::-webkit-scrollbar-thumb:hover {

  background: var(--color-gray-500);

}



/* 选择文本样式 */

::selection {

  background-color: var(--color-primary);

  color: white;

}



/* 焦点样式 */

:focus-visible {

  outline: 2px solid var(--color-primary);

  outline-offset: 2px;

}



/* 图片和媒体 */

img,

video {

  max-width: 100%;

  height: auto;

}



/* 按钮重置 */

button {

  background: none;

  border: none;

  font: inherit;

  cursor: pointer;

}



/* 输入框重置 */

input,

textarea,

select {

  font: inherit;

  color: inherit;

}



/* 链接样式 */

a {

  color: var(--color-primary);

  text-decoration: none;

  transition: color var(--duration-fast) var(--ease-out);

}



a:hover {

  color: var(--color-primary-hover);

}



/* 列表重置 */

ul,

ol {

  list-style: none;

}



/* 表格重置 */

table {

  border-collapse: collapse;

  border-spacing: 0;

}



/* 隐藏类 */

.hidden {

  display: none !important;

}



.sr-only {

  position: absolute;

  width: 1px;

  height: 1px;

  padding: 0;

  margin: -1px;

  overflow: hidden;

  clip: rect(0, 0, 0, 0);

  white-space: nowrap;

  border: 0;

}



/* 响应式容器 */

.container {

  width: 100%;

  max-width: 1200px;

  margin: 0 auto;

  padding: 0 var(--space-4);

}



@media (min-width: 640px) {

  .container {

    padding: 0 var(--space-6);

  }

}



@media (min-width: 1024px) {

  .container {

    padding: 0 var(--space-8);

  }

}



/* 工具类 */

.text-center {

  text-align: center;

}



.text-left {

  text-align: left;

}



.text-right {

  text-align: right;

}



.flex {

  display: flex;

}



.flex-col {

  flex-direction: column;

}



.items-center {

  align-items: center;

}



.justify-center {

  justify-content: center;

}



.justify-between {

  justify-content: space-between;

}



.space-x-2 > * + * {

  margin-left: var(--space-2);

}



.space-x-3 > * + * {

  margin-left: var(--space-3);

}



.space-x-4 > * + * {

  margin-left: var(--space-4);

}



.space-y-2 > * + * {

  margin-top: var(--space-2);

}



.space-y-3 > * + * {

  margin-top: var(--space-3);

}



.space-y-4 > * + * {

  margin-top: var(--space-4);

}



/* 动画类 */

.animate-spin {

  animation: spin 1s linear infinite;

}



.animate-pulse {

  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;

}



.animate-bounce {

  animation: bounce 1s infinite;

}



@keyframes spin {

  to {

    transform: rotate(360deg);

  }

}



@keyframes pulse {

  0%, 100% {

    opacity: 1;

  }

  50% {

    opacity: .5;

  }

}



@keyframes bounce {

  0%, 100% {

    transform: translateY(-25%);

    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);

  }

  50% {

    transform: none;

    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);

  }

}