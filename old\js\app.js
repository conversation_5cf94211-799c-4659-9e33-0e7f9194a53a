// 导入所有模块
import { APP_CONFIG } from './config.js';
import {
    getElement,
    showNotification,
    scrollToElement,
    formatDate
} from './modules/utils.js';
import { ThemeManager } from './modules/theme.js';
import { AuthManager } from './modules/auth.js';
import { MemoManager } from './modules/memo.js';
import { APIService } from './modules/api.js';
import { globalLazyLoader, createLazyImage } from './modules/lazyload.js';
import { PageStateManager } from './modules/pageStateManager.js';

// DOM 元素引用
const dom = {
    // 登录相关
    loginModal: getElement('#loginModal'),
    loginForm: getElement('#loginForm'),
    loginUsername: getElement('#loginUsername'),
    loginPassword: getElement('#loginPassword'),

    // 发布区域
    postSection: getElement('#postSection'),
    memoContent: getElement('#memoContent'),
    memoFile: getElement('#memoFile'),
    uploadPreview: getElement('#uploadPreview'),
    imagePreview: getElement('#imagePreview'),
    videoPreview: getElement('#videoPreview'),
    fileSelectText: getElement('#fileSelectText'),
    memoStatus: getElement('#memoStatus'),
    postButton: getElement('#postButton'),
    buttonText: getElement('#buttonText'),
    postLoading: getElement('#postLoading'),

    // 内容区域
    memosContainer: getElement('#memosContainer'),
    memosContent: getElement('#memosContent'),
    loadMoreButton: getElement('#loadMoreButton'),
    loadMoreText: getElement('#loadMoreText'),
    loadMoreSpinner: getElement('#loadMoreSpinner'),
    loadingIndicator: getElement('#loadingIndicator'),
    emptyState: getElement('#emptyState'),

    // 主题切换
    lightThemeBtn: getElement('#lightThemeBtn'),
    darkThemeBtn: getElement('#darkThemeBtn'),
    eyeProtectBtn: getElement('#eyeProtectBtn'),

    // 更新日志
    changelogModal: getElement('#changelogModal'),
    changelogContent: getElement('#changelogContent'),
    changelogBtn: getElement('#changelogBtn')
};

// 初始化图片放大功能
function initImageZoom() {
    document.addEventListener('click', (e) => {
        if (e.target.tagName === 'IMG' && e.target.closest('.memo-card')) {
            // 获取图片URL，优先使用data-src（真实URL），如果没有则使用src
            const imageUrl = e.target.dataset.src || e.target.src;
            
            // 如果图片还没加载完成，先加载
            if (e.target.classList.contains('lazy-loading') || !imageUrl || imageUrl.startsWith('data:image/svg+xml')) {
                showNotification('图片正在加载中...', 'info', 2000);
                return;
            }
            
            const overlay = document.createElement('div');
            overlay.style.cssText = 'position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0, 0, 0, 0.8); display: flex; justify-content: center; align-items: center; z-index: 1000; cursor: zoom-out; backdrop-filter: blur(4px);';
            overlay.onclick = () => document.body.removeChild(overlay);

            const imgElement = document.createElement('img');
            imgElement.src = imageUrl;
            imgElement.style.cssText = 'max-width: 90%; max-height: 90%; object-fit: contain; border-radius: 8px; box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5); transition: transform 0.3s ease;';
            
            // 添加加载动画
            imgElement.onload = () => {
                imgElement.style.transform = 'scale(1)';
            };
            imgElement.style.transform = 'scale(0.8)';

            overlay.appendChild(imgElement);
            document.body.appendChild(overlay);
            
            // 阻止事件冒泡
            e.stopPropagation();
        }
    });
}

// 主应用类
class App {
    constructor() {
        this.themeManager = new ThemeManager();
        this.authManager = new AuthManager();
        this.memoManager = new MemoManager();
        this.pageStateManager = new PageStateManager();
        this.isInitialized = false;
    }

    async init() {
        if (this.isInitialized) return;

        try {
            console.log('开始初始化应用...');
            
            // 重新获取DOM元素（因为组件是异步加载的）
            this.refreshDOMReferences();

            // 显示加载状态
            if (dom.loadingIndicator) {
                dom.loadingIndicator.classList.remove('hidden');
            }

            // 初始化主题管理器
            this.themeManager.init();

            // 初始化认证管理器
            this.authManager.init();

            // 初始化事件监听
            this.initEventListeners();

            // 初始化图片放大功能
            initImageZoom();

            // 加载初始数据
            await this.loadInitialData();

            // 隐藏加载状态
            if (dom.loadingIndicator) {
                dom.loadingIndicator.classList.add('hidden');
            }

            this.isInitialized = true;
            console.log('应用初始化完成');

            // 显示欢迎消息
            showNotification('欢迎使用当记！', 'success', 2000);

        } catch (error) {
            console.error('应用初始化失败:', error);
            if (dom.loadingIndicator) {
                dom.loadingIndicator.innerHTML = `
                    <div class="loading-container">
                        <p class="text-red-500 mb-4">应用初始化失败</p>
                        <button onclick="location.reload()" class="btn btn-primary">
                            重新加载
                        </button>
                    </div>
                `;
            }
        }
    }

    /**
     * 重新获取DOM元素引用（用于组件异步加载后）
     */
    refreshDOMReferences() {
        console.log('刷新DOM元素引用...');
        // 登录相关
        dom.loginModal = getElement('#loginModal');
        dom.loginForm = getElement('#loginForm');
        dom.loginUsername = getElement('#loginUsername');
        dom.loginPassword = getElement('#loginPassword');

        // 发布区域
        dom.postSection = getElement('#postSection');
        dom.memoContent = getElement('#memoContent');
        dom.memoFile = getElement('#memoFile');
        dom.uploadPreview = getElement('#uploadPreview');
        dom.imagePreview = getElement('#imagePreview');
        dom.videoPreview = getElement('#videoPreview');
        dom.fileSelectText = getElement('#fileSelectText');
        dom.memoStatus = getElement('#memoStatus');
        dom.postButton = getElement('#postButton');
        dom.buttonText = getElement('#buttonText');
        dom.postLoading = getElement('#postLoading');

        // 内容区域
        dom.memosContainer = getElement('#memosContainer');
        dom.memosContent = getElement('#memosContent');
        dom.loadMoreButton = getElement('#loadMoreButton');
        dom.loadMoreText = getElement('#loadMoreText');
        dom.loadMoreSpinner = getElement('#loadMoreSpinner');
        dom.loadingIndicator = getElement('#loadingIndicator');
        dom.emptyState = getElement('#emptyState');

        // 主题切换
        dom.lightThemeBtn = getElement('#lightThemeBtn');
        dom.darkThemeBtn = getElement('#darkThemeBtn');
        dom.eyeProtectBtn = getElement('#eyeProtectBtn');

        // 更新日志
        dom.changelogModal = getElement('#changelogModal');
        dom.changelogContent = getElement('#changelogContent');
        dom.changelogBtn = getElement('#changelogBtn');

        // 登录横幅
        dom.loginBanner = getElement('#loginBanner');

        console.log('DOM元素引用已刷新');
    }

    /**
     * 加载初始数据
     */
    async loadInitialData() {
        try {
            console.log('开始加载初始数据...');
            
            // 渲染备忘录列表
            await this.renderMemos();

            // 更新UI状态
            this.updateUI();
            
            console.log('初始数据加载完成');

        } catch (error) {
            console.error('加载初始数据失败:', error);
            showNotification('数据加载失败', 'error', 3000);
        }
    }

    initEventListeners() {
        // 登录相关事件
        dom.loginForm?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.authManager.handleLogin(e);
        });

        // 主题切换事件
        dom.lightThemeBtn?.addEventListener('click', () => {
            this.themeManager.setTheme('light');
        });

        dom.darkThemeBtn?.addEventListener('click', () => {
            this.themeManager.setTheme('dark');
        });

        dom.eyeProtectBtn?.addEventListener('click', () => {
            this.themeManager.setTheme('eyeProtect');
        });

        // 发布备忘录
        dom.postButton?.addEventListener('click', () => {
            this.handleCreateMemo();
        });

        // 加载更多
        dom.loadMoreButton?.addEventListener('click', () => {
            this.loadMoreMemos();
        });

        // 文件上传
        dom.memoFile?.addEventListener('change', (e) => {
            this.handleFileUpload(e);
        });

        // 认证状态监听
        this.authManager.addListener((event, data) => {
            // 延迟处理认证状态变化，避免频繁API调用
            setTimeout(() => {
                this.onAuthStateChange(event, data);
            }, 500);
        });

        // 备忘录变化监听
        this.memoManager.addListener((action, data) => {
            this.onMemoChange(action, data);
        });

        // 主题变化监听
        this.themeManager.addListener((theme, oldTheme) => {
            this.onThemeChange(theme, oldTheme);
        });

        // 添加滚动监听器来优化懒加载
        this.initScrollListener();

        // 更新按钮状态
        this.updateUI();
    }

    /**
     * 处理文件上传
     */
    handleFileUpload(event) {
        const files = event.target.files;
        if (!files || files.length === 0) return;

        const file = files[0];
        const maxSize = 10 * 1024 * 1024; // 10MB

        // 检查文件大小
        if (file.size > maxSize) {
            showNotification('文件大小不能超过10MB', 'error', 3000);
            return;
        }

        // 显示预览
        if (file.type.startsWith('image/')) {
            this.showImagePreview(file);
        } else if (file.type.startsWith('video/')) {
            this.showVideoPreview(file);
        }

        // 更新文件选择文本
        if (dom.fileSelectText) {
            dom.fileSelectText.textContent = file.name;
        }
    }

    /**
     * 显示图片预览
     */
    showImagePreview(file) {
        const reader = new FileReader();
        reader.onload = (e) => {
            if (dom.imagePreview && dom.uploadPreview) {
                dom.imagePreview.src = e.target.result;
                dom.imagePreview.classList.remove('hidden');
                dom.videoPreview?.classList.add('hidden');
                dom.uploadPreview.classList.remove('hidden');
            }
        };
        reader.readAsDataURL(file);
    }

    /**
     * 显示视频预览
     */
    showVideoPreview(file) {
        const reader = new FileReader();
        reader.onload = (e) => {
            if (dom.videoPreview && dom.uploadPreview) {
                dom.videoPreview.src = e.target.result;
                dom.videoPreview.classList.remove('hidden');
                dom.imagePreview?.classList.add('hidden');
                dom.uploadPreview.classList.remove('hidden');
            }
        };
        reader.readAsDataURL(file);
    }

    /**
     * 处理创建备忘录
     */
    async handleCreateMemo() {
        const content = dom.memoContent?.value.trim();
        const status = dom.memoStatus?.value || '公开';
        const file = dom.memoFile?.files[0];

        if (!content) {
            showNotification('请输入内容', 'warning', 2000);
            dom.memoContent?.focus();
            return;
        }

        // 显示加载状态
        this.setPostButtonLoading(true);

        try {
            const currentUser = this.authManager.getCurrentUser();
            const now = new Date().toISOString();
            
            const memoData = {
                content,
                status,
                author: currentUser,
                tags: [], // 可以后续扩展标签功能
                posttime: now,
                createdAt: now
            };

            // 如果有文件，先上传到服务器
            if (file) {
                try {
                    // 更新按钮文本显示上传进度
                    if (dom.buttonText) {
                        dom.buttonText.textContent = '上传中...';
                    }
                    
                    // 上传图片到服务器
                    const uploadResult = await APIService.uploadImage(file, { format: 'url_only' });
                    console.log('图片上传成功:', uploadResult);
                    
                    // 添加服务器返回的URL到附件
                    memoData.attachments = [{
                        id: Date.now().toString(),
                        name: file.name,
                        type: file.type,
                        url: uploadResult, // 使用服务器返回的URL
                        uploadedAt: new Date()
                    }];
                    
                    // 更新按钮文本
                    if (dom.buttonText) {
                        dom.buttonText.textContent = '发布中...';
                    }
                } catch (uploadError) {
                    console.error('图片上传失败:', uploadError);
                    showNotification('图片上传失败，请重试', 'error', 3000);
                    return;
                }
            }

            console.log('准备创建备忘录:', memoData);

            // 调用API创建备忘录
            await this.memoManager.createMemo(memoData);

            // 清空表单
            this.clearPostForm();

            // 重新渲染列表
            await this.renderMemos();

        } catch (error) {
            console.error('创建备忘录失败:', error);
            // 错误提示已经在MemoManager中处理了
        } finally {
            this.setPostButtonLoading(false);
        }
    }

    /**
     * 设置发布按钮加载状态
     */
    setPostButtonLoading(loading) {
        if (!dom.postButton || !dom.buttonText || !dom.postLoading) return;

        if (loading) {
            dom.postButton.disabled = true;
            dom.buttonText.textContent = '发布中...';
            dom.postLoading.classList.remove('hidden');
        } else {
            dom.postButton.disabled = false;
            dom.buttonText.textContent = '发布';
            dom.postLoading.classList.add('hidden');
        }
    }

    /**
     * 清空发布表单
     */
    clearPostForm() {
        if (dom.memoContent) dom.memoContent.value = '';
        if (dom.memoFile) dom.memoFile.value = '';
        if (dom.uploadPreview) dom.uploadPreview.classList.add('hidden');
        if (dom.fileSelectText) dom.fileSelectText.textContent = '点击上传图片或视频';
    }

    /**
     * 渲染备忘录列表
     */
    async renderMemos(forceReload = false) {
        // 重新获取DOM元素，确保组件已加载
        this.refreshDOMReferences();
        
        if (!dom.memosContent) {
            console.error('memosContent 元素未找到');
            return;
        }
        
        console.log('开始渲染备忘录列表...');

        // 检查是否应该恢复状态
        if (!forceReload) {
            const savedState = this.pageStateManager.getMemosState();
            if (savedState && this.pageStateManager.shouldRestoreState()) {
                console.log('恢复保存的备忘录状态');
                try {
                    this.restoreMemosFromState(savedState);
                    return;
                } catch (error) {
                    console.warn('恢复状态失败，将重新加载:', error);
                    // 清除无效状态
                    this.pageStateManager.clearState();
                }
            }
        }

        // 显示加载状态
        if (dom.loadingIndicator) {
            dom.loadingIndicator.classList.remove('hidden');
        }

        try {
            // 从Vika API加载数据
            const paginatedData = await this.memoManager.loadMemos(1, APP_CONFIG.recordsPerPage);
            let memos = paginatedData.memos;

            // 权限过滤：未登录用户只能看到公开内容
            const authStatus = this.authManager.getAuthStatus();
            if (!authStatus.hasValidSession) {
                memos = memos.filter(memo => memo.status === '公开');
                console.log(`权限过滤：未登录用户，只显示 ${memos.length} 条公开内容`);
            } else {
                console.log(`已登录用户：显示 ${memos.length} 条内容（包含私密）`);
            }

            if (memos.length === 0) {
                this.showEmptyState();
                return;
            }

            // 隐藏空状态
            if (dom.emptyState) {
                dom.emptyState.classList.add('hidden');
            }

            // 渲染备忘录
            dom.memosContent.innerHTML = memos.map(memo => this.renderMemoCard(memo)).join('');

            // 初始化懒加载图片
            this.initLazyImages();

            // 更新当前页码
            this.memoManager.currentPage = paginatedData.currentPage;

            // 更新加载更多按钮
            this.updateLoadMoreButton(paginatedData);

            // 保存状态
            this.pageStateManager.saveMemosState(memos, paginatedData.currentPage, paginatedData.hasMore);

        } catch (error) {
            console.error('渲染备忘录失败:', error);
            showNotification('加载数据失败', 'error', 3000);
        } finally {
            // 隐藏加载状态
            if (dom.loadingIndicator) {
                dom.loadingIndicator.classList.add('hidden');
            }
        }
    }

    /**
     * 从保存的状态恢复备忘录列表
     */
    restoreMemosFromState(savedState) {
        console.log('从状态恢复备忘录列表:', savedState);
        
        // 权限过滤：未登录用户只能看到公开内容
        const authStatus = this.authManager.getAuthStatus();
        let memos = savedState.memos;
        
        if (!authStatus.hasValidSession) {
            memos = memos.filter(memo => memo.status === '公开');
            console.log(`权限过滤：未登录用户，显示 ${memos.length} 条公开内容`);
        }

        if (memos.length === 0) {
            this.showEmptyState();
            return;
        }

        // 隐藏空状态
        if (dom.emptyState) {
            dom.emptyState.classList.add('hidden');
        }

        // 渲染备忘录（需要重新创建Memo对象）
        const memoObjects = memos.map(memoData => {
            // 创建一个简单的备忘录对象，包含必要的方法
            const memo = {
                ...memoData,
                getFormattedDate: function() {
                    return formatDate(this.createdAt);
                }
            };
            return memo;
        });

        dom.memosContent.innerHTML = memoObjects.map(memo => this.renderMemoCard(memo)).join('');

        // 初始化懒加载图片
        this.initLazyImages();

        // 更新当前页码
        this.memoManager.currentPage = savedState.currentPage;

        // 更新加载更多按钮
        this.updateLoadMoreButton({ hasMore: savedState.hasMore });

        // 恢复滚动位置
        this.pageStateManager.restoreScrollPosition();

        // 隐藏加载状态
        if (dom.loadingIndicator) {
            dom.loadingIndicator.classList.add('hidden');
        }
    }

    /**
     * 获取当前显示的所有备忘录
     */
    getAllDisplayedMemos() {
        // 从DOM中提取当前显示的备忘录ID，然后从memoManager中获取完整数据
        const memoCards = dom.memosContent?.querySelectorAll('.memo-card[data-memo-id]') || [];
        const displayedMemos = [];
        
        memoCards.forEach(card => {
            const memoId = card.getAttribute('data-memo-id');
            const memo = this.memoManager.getMemo(memoId);
            if (memo) {
                displayedMemos.push(memo);
            }
        });
        
        return displayedMemos;
    }

    /**
     * 渲染单个备忘录卡片
     */
    renderMemoCard(memo) {
        const author = memo.author || { username: '匿名用户' };
        const avatar = author.avatar || `https://ui-avatars.com/api/?name=${encodeURIComponent(author.username)}&background=3B82F6&color=fff`;
        
        // 检查用户是否登录以及是否是备忘录的所有者
        const authStatus = this.authManager.getAuthStatus();
        const isOwner = authStatus.hasValidSession && 
                       (memo.user_id === authStatus.user.id || 
                        (memo.fields && memo.fields.user_id && memo.fields.user_id[0] === authStatus.user.id));

        return `
            <div class="memo-card" data-memo-id="${memo.id}" style="cursor: pointer;">
                <div class="memo-header">
                    <div class="memo-author">
                        <img src="${avatar}" alt="${author.username}" class="memo-avatar">
                        <div class="memo-meta">
                            <div class="memo-username">${author.username}</div>
                            <div class="memo-time">${memo.getFormattedDate()}</div>
                        </div>
                    </div>
                    <div class="memo-actions" onclick="event.stopPropagation();">
                        <button class="btn-ghost btn-sm" onclick="app.viewMemoDetail('${memo.id}')" title="查看详情">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            </svg>
                        </button>
                        ${isOwner ? `
                        <button class="btn-ghost btn-sm" onclick="app.editMemo('${memo.id}')" title="编辑">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                        </button>
                        <button class="btn-ghost btn-sm" onclick="app.deleteMemo('${memo.id}')" title="删除">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                        </button>
                        ` : ''}
                    </div>
                </div>
                
                <div class="memo-content" onclick="app.viewMemoDetail('${memo.id}')">
                    ${this.formatMemoContent(memo.content)}
                </div>

                ${memo.attachments && memo.attachments.length > 0 ? `
                <div class="memo-media">
                    ${memo.attachments.map(att => {
                        if (att.type.startsWith('image/')) {
                            return `
                                <div class="media-item" style="cursor: zoom-in;" onclick="event.stopPropagation(); app.viewImage('${att.url}')">
                                    ${createLazyImage(att.thumbnail || att.url, '附件图片', 'memo-image lazy-image', {
                                        style: 'cursor: pointer;'
                                    })}
                                </div>
                            `;
                        }
                        return `
                            <div class="media-item">
                                <a href="${att.url}" target="_blank" class="attachment-link">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                    ${att.name}
                                </a>
                            </div>
                        `;
                    }).join('')}
                </div>
                ` : ''}
            </div>
        `;
    }

    /**
     * 格式化备忘录内容
     */
    formatMemoContent(content) {
        try {
            // 如果有完整的markdown渲染器
            if (window.md) {
                return window.md.render(content);
            }
            
            // 如果markdown-it未加载，使用简单渲染
            if (typeof window.markdownit === 'undefined') {
                console.warn('markdownit 未加载，使用简单文本渲染');
                return this.formatSimpleContent(content);
            }
            
            // 创建markdown-it实例
            const md = window.markdownit({
                html: true,
                linkify: true,
                typographer: true,
                breaks: true,
                highlight: function (str, lang) {
                    if (typeof window.hljs !== 'undefined' && window.hljs.getLanguage && window.hljs.getLanguage(lang)) {
                        try {
                            return window.hljs.highlight(str, { language: lang }).value;
                        } catch (__) {}
                    }
                    
                    // 返回转义后的代码而不是空字符串
                    if (typeof window.markdownit !== 'undefined') {
                        try {
                            return `<pre class="hljs"><code class="language-${lang || 'plaintext'}">${window.markdownit().utils.escapeHtml(str)}</code></pre>`;
                        } catch (escapeError) {
                            console.error('HTML转义失败:', escapeError);
                        }
                    }
                    
                    // 最后的后备方案
                    return `<pre><code class="language-${lang || 'plaintext'}">${str.replace(/[&<>"']/g, c => ({
                        '&': '&amp;',
                        '<': '&lt;',
                        '>': '&gt;',
                        '"': '&quot;',
                        "'": '&#39;'
                    }[c]))}</code></pre>`;
                }
            });
            
            return md.render(content);
        } catch (error) {
            console.error('Markdown渲染失败:', error);
            // 最后的降级方案
            return this.formatSimpleContent(content);
        }
    }

    /**
     * 简单文本格式化函数
     */
    formatSimpleContent(content) {
        if (!content) return '';
        
        return content
            .replace(/\n/g, '<br>')
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`(.*?)`/g, '<code>$1</code>')
            .replace(/(https?:\/\/[^\s]+)/g, (url) => {
                // 对长链接进行截断处理
                let displayUrl = url;
                if (url.length > 50) {
                    displayUrl = url.substring(0, 50) + '...';
                }
                return `<a href="${url}" target="_blank" class="memo-link" title="${url}">${displayUrl}</a>`;
            });
    }

    /**
     * 渲染附件
     */
    renderAttachments(attachments) {
        return `
            <div class="memo-media">
                ${attachments.map(att => {
            if (att.type.startsWith('image/')) {
                return createLazyImage(att.url, att.name, 'memo-image', {
                    style: 'cursor: pointer;'
                });
            } else if (att.type.startsWith('video/')) {
                return `<video src="${att.url}" class="memo-video" controls preload="metadata"></video>`;
            }
            return '';
        }).join('')}
            </div>
        `;
    }

    /**
     * 初始化滚动监听器
     */
    initScrollListener() {
        let scrollTimeout;
        
        const handleScroll = () => {
            // 清除之前的定时器
            clearTimeout(scrollTimeout);
            
            // 延迟执行，避免频繁触发
            scrollTimeout = setTimeout(() => {
                // 检查视口中的图片
                globalLazyLoader.checkViewport();
            }, 100);
        };
        
        // 添加滚动监听
        window.addEventListener('scroll', handleScroll, { passive: true });
        
        // 添加窗口大小变化监听
        window.addEventListener('resize', handleScroll, { passive: true });
        
        console.log('滚动监听器已初始化');
    }

    /**
     * 初始化懒加载图片
     */
    initLazyImages() {
        // 获取新添加的懒加载图片
        const lazyImages = dom.memosContent?.querySelectorAll('img[data-src]') || [];
        
        if (lazyImages.length > 0) {
            console.log(`初始化 ${lazyImages.length} 个懒加载图片`);
            globalLazyLoader.observeAll(lazyImages);
            
            // 手动检查一次视口中的图片
            setTimeout(() => {
                globalLazyLoader.checkViewport();
            }, 100);
        }
    }

    /**
     * 显示/隐藏登录横幅
     */
    updateLoginBanner() {
        const loginBanner = getElement('#loginBanner');
        const authStatus = this.authManager.getAuthStatus();
        
        if (!loginBanner) return;
        
        if (!authStatus.hasValidSession) {
            // 检查用户是否手动关闭过横幅
            const bannerDismissed = localStorage.getItem('loginBannerDismissed');
            if (!bannerDismissed) {
                loginBanner.classList.remove('hidden');
                console.log('显示登录横幅');
            }
        } else {
            loginBanner.classList.add('hidden');
            // 登录后清除关闭状态
            localStorage.removeItem('loginBannerDismissed');
            console.log('隐藏登录横幅');
        }
    }

    /**
     * 关闭登录横幅
     */
    hideBanner() {
        const loginBanner = getElement('#loginBanner');
        if (loginBanner) {
            loginBanner.classList.add('fade-out');
            setTimeout(() => {
                loginBanner.classList.add('hidden');
                loginBanner.classList.remove('fade-out');
            }, 300);
            
            // 记住用户关闭了横幅
            localStorage.setItem('loginBannerDismissed', 'true');
            console.log('用户关闭了登录横幅');
        }
    }

    /**
     * 显示空状态
     */
    showEmptyState() {
        if (dom.emptyState) {
            dom.emptyState.classList.remove('hidden');
        }
        if (dom.memosContent) {
            dom.memosContent.innerHTML = '';
        }
    }

    /**
     * 更新加载更多按钮
     */
    updateLoadMoreButton(paginatedData) {
        if (!dom.loadMoreButton) return;

        if (paginatedData.hasMore) {
            dom.loadMoreButton.classList.remove('hidden');
        } else {
            dom.loadMoreButton.classList.add('hidden');
        }
    }

    /**
     * 加载更多备忘录
     */
    async loadMoreMemos() {
        if (!dom.loadMoreButton) return;

        // 显示加载状态
        this.setLoadMoreButtonLoading(true);

        try {
            const nextPage = this.memoManager.currentPage + 1;
            
            // 从Vika API加载下一页数据
            const paginatedData = await this.memoManager.loadMoreMemos(nextPage, APP_CONFIG.recordsPerPage);

            if (paginatedData && paginatedData.memos.length > 0) {
                // 权限过滤：未登录用户只能看到公开内容
                const authStatus = this.authManager.getAuthStatus();
                let filteredMemos = paginatedData.memos;
                
                if (!authStatus.hasValidSession) {
                    filteredMemos = paginatedData.memos.filter(memo => memo.status === '公开');
                    console.log(`权限过滤：未登录用户，新加载 ${filteredMemos.length} 条公开内容`);
                }
                
                if (filteredMemos.length > 0) {
                    // 渲染新的备忘录并追加到现有列表
                    const newMemosHtml = filteredMemos.map(memo => this.renderMemoCard(memo)).join('');
                    if (dom.memosContent) {
                        dom.memosContent.insertAdjacentHTML('beforeend', newMemosHtml);
                        
                        // 初始化新添加的懒加载图片
                        this.initLazyImages();
                    }
                    
                    showNotification(`加载了 ${filteredMemos.length} 条新备忘录`, 'success', 2000);
                } else {
                    showNotification('没有更多公开内容了，请登录查看更多', 'info', 3000);
                }
                
                // 更新当前页码
                this.memoManager.currentPage = nextPage;
                
                // 更新加载更多按钮状态
                this.updateLoadMoreButton(paginatedData);
                
                // 更新保存的状态
                const allMemos = this.getAllDisplayedMemos();
                this.pageStateManager.saveMemosState(allMemos, nextPage, paginatedData.hasMore);
                
                showNotification(`加载了 ${paginatedData.memos.length} 条新备忘录`, 'success', 2000);
            } else {
                // 没有更多数据
                this.updateLoadMoreButton({ hasMore: false });
                showNotification('没有更多内容了', 'info', 2000);
            }
        } catch (error) {
            console.error('加载更多备忘录失败:', error);
            showNotification('加载失败，请重试', 'error', 3000);
        } finally {
            this.setLoadMoreButtonLoading(false);
        }
    }

    /**
     * 设置加载更多按钮的加载状态
     */
    setLoadMoreButtonLoading(loading) {
        if (!dom.loadMoreButton || !dom.loadMoreText || !dom.loadMoreSpinner) return;

        if (loading) {
            dom.loadMoreButton.disabled = true;
            dom.loadMoreText.textContent = '加载中...';
            dom.loadMoreSpinner.classList.remove('hidden');
        } else {
            dom.loadMoreButton.disabled = false;
            dom.loadMoreText.textContent = '加载更多';
            dom.loadMoreSpinner.classList.add('hidden');
        }
    }

    /**
     * 认证状态变化处理
     */
    onAuthStateChange(event, data) {
        console.log('认证状态变化:', event, data);
        this.updateUI();

        if (event === 'login') {
            // 延迟重新加载数据，避免与登录请求冲突
            setTimeout(() => {
                this.renderMemos();
            }, 1000);
        }
    }

    /**
     * 备忘录变化处理
     */
    onMemoChange(action, data) {
        console.log('备忘录变化:', action, data);

        switch (action) {
            case 'create':
            case 'update':
            case 'delete':
                this.renderMemos();
                break;
        }
    }

    /**
     * 主题变化处理
     */
    onThemeChange(theme, oldTheme) {
        console.log('主题变化:', theme, oldTheme);
        // 可以在这里添加主题切换的特殊处理逻辑
    }

    /**
     * 更新UI状态
     */
    updateUI() {
        const authStatus = this.authManager.getAuthStatus();

        // 更新发布区域显示
        if (dom.postSection) {
            if (authStatus.hasValidSession) {
                dom.postSection.classList.remove('hidden');
                dom.postSection.style.display = 'block';
            } else {
                dom.postSection.classList.add('hidden');
                dom.postSection.style.display = 'none';
            }
        }

        // 更新页面标题提示
        this.updatePageTitle(authStatus);

        // 更新登录横幅显示
        this.updateLoginBanner();
    }

    /**
     * 更新页面标题
     */
    updatePageTitle(authStatus) {
        const originalTitle = '当记';
        if (authStatus.hasValidSession) {
            document.title = `${originalTitle} - ${authStatus.user.nickname || authStatus.user.username}`;
        } else {
            document.title = `${originalTitle} - 记录美好生活`;
        }
    }

    /**
     * 查看备忘录详情
     */
    viewMemoDetail(memoId) {
        // 保存当前滚动位置
        this.pageStateManager.saveScrollPosition();
        
        // 导航到内容详情页
        window.location.href = `content.html?id=${memoId}`;
    }

    /**
     * 编辑备忘录
     */
    editMemo(memoId) {
        const memo = this.memoManager.getMemo(memoId);
        if (!memo) return;

        // 简单实现：将内容填入编辑框
        if (dom.memoContent) {
            dom.memoContent.value = memo.content;
            dom.memoContent.focus();
            scrollToElement(dom.postSection);
        }

        if (dom.memoStatus) {
            dom.memoStatus.value = memo.status;
        }

        showNotification('内容已加载到编辑框', 'info', 2000);
    }

    /**
     * 删除备忘录
     */
    async deleteMemo(memoId) {
        if (confirm('确定要删除这条备忘录吗？')) {
            try {
                await this.memoManager.deleteMemo(memoId);
                // 重新渲染列表
                await this.renderMemos();
            } catch (error) {
                console.error('删除备忘录失败:', error);
                // 错误提示已经在MemoManager中处理了
            }
        }
    }

    /**
     * 查看图片（放大显示）
     */
    viewImage(imageUrl) {
        // 创建模态框
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-[9999]';
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.75);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        `;

        // 创建图片容器
        const imgContainer = document.createElement('div');
        imgContainer.style.cssText = `
            position: relative;
            max-width: 90vw;
            max-height: 90vh;
        `;

        // 创建图片
        const img = document.createElement('img');
        img.src = imageUrl;
        img.style.cssText = `
            max-width: 90vw;
            max-height: 90vh;
            object-fit: contain;
            border-radius: 8px;
        `;

        // 创建关闭按钮
        const closeBtn = document.createElement('button');
        closeBtn.innerHTML = '&times;';
        closeBtn.style.cssText = `
            position: absolute;
            top: -40px;
            right: 0;
            background: transparent;
            color: white;
            border: none;
            font-size: 40px;
            cursor: pointer;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        `;

        // 组装元素
        imgContainer.appendChild(img);
        imgContainer.appendChild(closeBtn);
        modal.appendChild(imgContainer);
        document.body.appendChild(modal);

        // 添加事件监听器
        const close = () => {
            document.body.removeChild(modal);
        };

        closeBtn.addEventListener('click', close);
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                close();
            }
        });

        // ESC键关闭
        const escHandler = (e) => {
            if (e.key === 'Escape') {
                close();
                document.removeEventListener('keydown', escHandler);
            }
        };
        document.addEventListener('keydown', escHandler);
    }

    /**
     * 显示登录模态框
     */
    showLogin() {
        if (dom.loginModal) {
            dom.loginModal.classList.add('show');
        }
    }

    /**
     * 隐藏登录模态框
     */
    hideLogin() {
        if (dom.loginModal) {
            dom.loginModal.classList.remove('show');
        }
    }

    /**
     * 显示更新日志
     */
    showChangelog() {
        if (dom.changelogModal && dom.changelogContent) {
            dom.changelogContent.innerHTML = `
                <div class="space-y-6">
                    <div class="changelog-section">
                        <h4 class="text-lg font-bold mb-3 text-primary">🚀 v2.0.0 - 现代化重构版本</h4>
                        <ul class="list-disc list-inside space-y-2 text-sm">
                            <li>✨ 全新的模块化架构设计</li>
                            <li>🎨 现代化的UI设计和交互体验</li>
                            <li>🌈 完整的主题系统（浅色/深色/护眼模式）</li>
                            <li>📱 完全响应式设计，完美适配移动端</li>
                            <li>⚡ 性能优化，加载速度提升50%</li>
                            <li>🔒 增强的用户认证和安全机制</li>
                            <li>📎 支持图片和视频附件上传</li>
                            <li>🏷️ 智能标签系统和搜索功能</li>
                            <li>💾 数据导入导出功能</li>
                            <li>🔔 优雅的通知提示系统</li>
                        </ul>
                    </div>
                    
                    <div class="changelog-section">
                        <h4 class="text-lg font-bold mb-3 text-secondary">🛠️ v1.2.0 - 模块化重构</h4>
                        <ul class="list-disc list-inside space-y-2 text-sm">
                            <li>使用ES模块重构代码结构</li>
                            <li>提高代码可维护性</li>
                            <li>优化性能表现</li>
                        </ul>
                    </div>
                    
                    <div class="changelog-section">
                        <h4 class="text-lg font-bold mb-3 text-accent">🎯 v1.1.0 - 功能增强</h4>
                        <ul class="list-disc list-inside space-y-2 text-sm">
                            <li>添加图片上传功能</li>
                            <li>支持Markdown格式</li>
                            <li>主题切换优化</li>
                        </ul>
                    </div>
                </div>
            `;
            dom.changelogModal.classList.add('show');
        }
    }

    /**
     * 隐藏更新日志
     */
    hideChangelog() {
        if (dom.changelogModal) {
            dom.changelogModal.classList.remove('show');
        }
    }


}

// 创建应用实例
const app = new App();

// 暴露必要的全局方法（用于HTML内联事件）
window.app = app;
window.showLogin = () => app.showLogin();
window.hideLogin = () => app.hideLogin();
window.showChangelog = () => app.showChangelog();
window.hideChangelog = () => app.hideChangelog();

// 不再使用 DOMContentLoaded，因为组件是异步加载的
// 初始化将由 index.html 中的组件加载器触发
window.showLogin = () => app.showLogin();
window.hideLogin = () => app.hideLogin();
window.createMemo = () => app.createMemo();
window.loadMoreMemos = () => app.loadMoreMemos();
window.showChangelog = () => app.showChangelog();
window.hideChangelog = () => app.hideChangelog();

// 添加手势操作支持
function initGestureSupport() {
    const memosList = document.getElementById('memosList');
    if (!memosList) return;

    let touchStartX = 0;
    let touchStartY = 0;
    let touchEndX = 0;
    let touchEndY = 0;

    // 触摸开始
    memosList.addEventListener('touchstart', function(event) {
        touchStartX = event.changedTouches[0].screenX;
        touchStartY = event.changedTouches[0].screenY;
    }, false);

    // 触摸结束
    memosList.addEventListener('touchend', function(event) {
        touchEndX = event.changedTouches[0].screenX;
        touchEndY = event.changedTouches[0].screenY;
        handleGesture();
    }, false);

    // 处理手势
    function handleGesture() {
        const deltaX = touchEndX - touchStartX;
        const deltaY = touchEndY - touchStartY;
        const absDeltaX = Math.abs(deltaX);
        const absDeltaY = Math.abs(deltaY);

        // 检查是否为水平滑动且滑动距离足够
        if (absDeltaX > absDeltaY && absDeltaX > 50) {
            if (deltaX > 0) {
                // 向右滑动 - 上一页
                console.log('向右滑动 - 上一页');
                // 这里可以触发上一页操作
                // pageStateManager.prevPage();
            } else {
                // 向左滑动 - 下一页
                console.log('向左滑动 - 下一页');
                // 这里可以触发下一页操作
                // pageStateManager.nextPage();
            }
        }
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', async function () {
    try {
        // 初始化组件（需要先导入componentLoader）
        const { componentLoader } = await import('./modules/componentLoader.js');
        await componentLoader.loadComponents([
            { containerId: 'headerComponent', componentPath: 'components/header.html' },
            { containerId: 'loginBannerComponent', componentPath: 'components/login-banner.html' },
            { containerId: 'loginModalComponent', componentPath: 'components/login-modal.html' },
            { containerId: 'postSectionComponent', componentPath: 'components/post-section.html' },
            { containerId: 'memosListComponent', componentPath: 'components/memos-list.html' },
            { containerId: 'loadMoreButtonComponent', componentPath: 'components/load-more-button.html' },
            { containerId: 'footerComponent', componentPath: 'components/footer.html' },
            { containerId: 'updateLogModalComponent', componentPath: 'components/update-log-modal.html' }
        ]);
        
        // 初始化手势支持
        initGestureSupport();
        
        // 加载第一页数据
        await app.renderMemos();
        
        // 初始化主题
        themeManager.init();
        
    } catch (error) {
        console.error('应用初始化失败:', error);
    }
});
