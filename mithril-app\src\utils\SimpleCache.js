/**
 * 简单缓存类
 * 提供基本的缓存功能，支持设置过期时间
 */
export class SimpleCache {
    constructor() {
        // 使用Map存储缓存数据
        this.cache = new Map();
    }

    /**
     * 获取缓存值
     * @param {string} key - 缓存键
     * @returns {*} 缓存值，如果不存在或已过期则返回null
     */
    get(key) {
        if (!this.cache.has(key)) {
            return null;
        }

        const item = this.cache.get(key);
        const now = Date.now();

        // 检查是否已过期
        if (item.expireAt && now > item.expireAt) {
            // 删除过期项
            this.cache.delete(key);
            return null;
        }

        return item.value;
    }

    /**
     * 设置缓存值
     * @param {string} key - 缓存键
     * @param {*} value - 缓存值
     * @param {number} ttl - 过期时间（毫秒），可选
     */
    set(key, value, ttl = 0) {
        const item = {
            value: value,
            expireAt: ttl > 0 ? Date.now() + ttl : null
        };
        this.cache.set(key, item);
    }

    /**
     * 删除缓存项
     * @param {string} key - 缓存键
     */
    delete(key) {
        this.cache.delete(key);
    }

    /**
     * 根据键的模式删除缓存项
     * @param {string} pattern - 键的模式（前缀匹配）
     */
    deletePattern(pattern) {
        for (const key of this.cache.keys()) {
            if (key.startsWith(pattern)) {
                this.cache.delete(key);
            }
        }
    }

    /**
     * 清空所有缓存
     */
    clear() {
        this.cache.clear();
    }

    /**
     * 获取缓存大小
     * @returns {number} 缓存项数量
     */
    size() {
        return this.cache.size;
    }
}

// 创建全局实例
export const apiCache = new SimpleCache();