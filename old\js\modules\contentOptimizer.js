/**

 * 内容页优化器

 * 专门优化内容页的脚本加载、渲染性能

 */



export class ContentOptimizer {

    constructor() {

        this.scriptCache = new Map();

        this.loadingPromises = new Map();

        this.defaultTimeout = 3000; // 3秒超时

        this.retryCount = 2;

        

        // CDN源列表，用于markdown-it加载失败时的备用方案

        this.cdnSources = [

            'https://cdn.jsdelivr.net/npm/markdown-it@13.0.1/dist/markdown-it.min.js',

            'https://cdnjs.cloudflare.com/ajax/libs/markdown-it/13.0.1/markdown-it.min.js'

        ];

        

        this.hljsSources = [

            'https://cdn.jsdelivr.net/npm/highlight.js@11.8.0/lib/highlight.min.js',

            'https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/highlight.min.js'

        ];

        

        // 预加载关键脚本

        this.preloadCriticalScripts();

    }



    /**

     * 预加载关键脚本

     */

    preloadCriticalScripts() {

        const criticalScripts = [

            ...this.cdnSources,

            ...this.hljsSources

        ];



        criticalScripts.forEach(url => {

            this.preloadScript(url);

        });

    }



    /**

     * 预加载脚本（不阻塞）

     */

    preloadScript(url) {

        if (this.scriptCache.has(url)) {

            return;

        }



        const link = document.createElement('link');

        link.rel = 'preload';

        link.as = 'script';

        link.href = url;

        document.head.appendChild(link);



        // 标记为预加载中

        this.scriptCache.set(url, { status: 'preloading', timestamp: Date.now() });

    }



    /**

     * 优化的脚本加载器

     */

    async loadScript(url, timeout = this.defaultTimeout) {

        // 检查缓存

        const cached = this.scriptCache.get(url);

        if (cached && cached.status === 'loaded') {

            console.log('脚本已缓存:', url);

            return cached.exports;

        }



        // 防重复加载

        if (this.loadingPromises.has(url)) {

            console.log('等待脚本加载完成:', url);

            return await this.loadingPromises.get(url);

        }



        // 创建加载Promise

        const loadPromise = this.loadScriptWithRetry(url, timeout);

        this.loadingPromises.set(url, loadPromise);



        try {

            const result = await loadPromise;

            this.loadingPromises.delete(url);

            return result;

        } catch (error) {

            this.loadingPromises.delete(url);

            throw error;

        }

    }



    /**

     * 带重试和CDN切换的脚本加载

     */

    async loadScriptWithRetry(url, timeout, retryCount = this.retryCount) {

        // 确定CDN源列表

        let sources = [url];

        if (url.includes('markdown-it')) {

            sources = this.cdnSources;

        } else if (url.includes('highlight')) {

            sources = this.hljsSources;

        }



        // 尝试每个源

        for (let s = 0; s < sources.length; s++) {

            const sourceUrl = sources[s];

            

            // 对每个源进行重试

            for (let i = 0; i <= retryCount; i++) {

                try {

                    console.log(`尝试加载脚本 (${s + 1}-${i + 1}/${sources.length}-${retryCount + 1}):`, sourceUrl);

                    const result = await this.loadScriptCore(sourceUrl, timeout);

                    

                    // 缓存成功结果

                    this.scriptCache.set(sourceUrl, {

                        status: 'loaded',

                        exports: result,

                        timestamp: Date.now()

                    });

                    

                    // 如果使用了备用源，也缓存到主URL

                    if (sourceUrl !== url) {

                        this.scriptCache.set(url, {

                            status: 'loaded',

                            exports: result,

                            timestamp: Date.now()

                        });

                    }

                    

                    return result;

                } catch (error) {

                    console.warn(`脚本加载失败 (${s + 1}-${i + 1}/${sources.length}-${retryCount + 1}):`, sourceUrl, error);

                    

                    // 如果是最后一次尝试

                    if (s === sources.length - 1 && i === retryCount) {

                        // 标记为失败

                        this.scriptCache.set(sourceUrl, {

                            status: 'failed',

                            error: error,

                            timestamp: Date.now()

                        });

                        

                        // 如果使用了备用源，也标记主URL失败

                        if (sourceUrl !== url) {

                            this.scriptCache.set(url, {

                                status: 'failed',

                                error: error,

                                timestamp: Date.now()

                            });

                        }

                        

                        throw error;

                    }

                }

                

                // 重试前等待

                await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));

            }

        }

    }



    /**

     * 核心脚本加载逻辑

     */

    loadScriptCore(url, timeout) {

        return new Promise((resolve, reject) => {

            // 检查是否已经存在

            const existingScript = document.querySelector(`script[src="${url}"]`);

            if (existingScript) {

                // 检查全局对象是否已加载

                const globalName = this.getGlobalName(url);

                if (globalName && window[globalName]) {

                    resolve(window[globalName]);

                    return;

                }

            }



            const script = document.createElement('script');

            script.src = url;

            script.async = true;



            // 设置超时

            const timeoutId = setTimeout(() => {

                script.remove();

                reject(new Error(`脚本加载超时: ${url}`));

            }, timeout);



            script.onload = () => {

                clearTimeout(timeoutId);

                

                // 获取导出的对象

                const globalName = this.getGlobalName(url);

                const exports = globalName ? window[globalName] : true;

                

                console.log('脚本加载成功:', url);

                resolve(exports);

            };



            script.onerror = () => {

                clearTimeout(timeoutId);

                script.remove();

                reject(new Error(`脚本加载失败: ${url}`));

            };



            document.head.appendChild(script);

        });

    }



    /**

     * 根据URL获取全局变量名

     */

    getGlobalName(url) {

        const globalMap = {

            'markdown-it': 'markdownit',

            'highlight.js': 'hljs',

            'prism': 'Prism'

        };



        for (const [key, value] of Object.entries(globalMap)) {

            if (url.includes(key)) {

                return value;

            }

        }

        return null;

    }



    /**

     * 优化的Markdown渲染器

     */

    async createOptimizedMarkdownRenderer() {

        try {

            // 尝试加载markdown-it

            let markdownit;

            try {

                markdownit = await this.loadScript(

                    'https://cdn.jsdelivr.net/npm/markdown-it@13.0.1/dist/markdown-it.min.js',

                    2000 // 2秒超时

                );

            } catch (error) {

                console.warn('从CDN加载markdown-it失败，尝试备用源:', error);

                // 尝试备用CDN源

                try {

                    markdownit = await this.loadScript(

                        'https://cdnjs.cloudflare.com/ajax/libs/markdown-it/13.0.1/markdown-it.min.js',

                        2000 // 2秒超时

                    );

                } catch (fallbackError) {

                    console.warn('备用源加载失败:', fallbackError);

                    throw fallbackError;

                }

            }



            if (markdownit) {

                const md = markdownit({

                    html: true,

                    linkify: true,

                    typographer: true,

                    breaks: true

                });



                // 尝试加载代码高亮

                try {

                    const hljs = await this.loadScript(

                        'https://cdn.jsdelivr.net/npm/highlight.js@11.8.0/lib/highlight.min.js',

                        1500 // 1.5秒超时

                    );



                    if (hljs) {

                        md.set({

                            highlight: function (str, lang) {

                                if (lang && hljs.getLanguage) {

                                    try {

                                        // 验证语言是否被支持

                                        const isLanguageSupported = hljs.getLanguage(lang);

                                        if (isLanguageSupported) {

                                            return hljs.highlight(str, { language: lang }).value;

                                        }

                                    } catch (langCheckError) {

                                        console.warn(`语言支持检查失败 (${lang}):`, langCheckError);

                                    }

                                }

                                // 返回转义后的代码而不是空字符串

                                try {

                                    // 作为后备方案，使用 markdown-it 自带的 escapeHtml

                                    return `<pre class="hljs"><code class="language-${lang || 'plaintext'}">${md.utils.escapeHtml(str)}</code></pre>`;

                                } catch (escapeError) {

                                    console.error('HTML转义失败:', escapeError);

                                    return `<pre><code class="language-${lang || 'plaintext'}">${str.replace(/[&<>"']/g, c => ({

                                        '&': '&amp;',

                                        '<': '&lt;',

                                        '>': '&gt;',

                                        '"': '&quot;',

                                        "'": '&#39;'

                                    }[c]))}</code></pre>`;

                                }

                            }

                        });

                    }

                } catch (error) {

                    console.warn('代码高亮加载失败，使用基础渲染:', error);

                }



                return {

                    render: (content) => {

                        try {

                            return md.render(content);

                        } catch (renderError) {

                            console.error('Markdown渲染失败:', renderError);

                            // 使用简单格式化作为最后的降级方案

                            return this.formatSimpleContent(content);

                        }

                    },

                    type: 'markdown'

                };

            }

        } catch (error) {

            console.warn('Markdown渲染器加载失败:', error);

        }



        // 回退到简单渲染器

        return this.createFallbackRenderer();

    }



    /**

     * 创建回退渲染器

     */

    createFallbackRenderer() {

        return {

            render: (content) => this.formatSimpleContent(content),

            type: 'fallback'

        };

    }



    /**

     * 简单文本格式化函数

     */

    formatSimpleContent(content) {

        if (!content) return '';

        

        return content

            .replace(/\n/g, '<br>')

            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')

            .replace(/\*(.*?)\*/g, '<em>$1</em>')

            .replace(/`(.*?)`/g, '<code>$1</code>')

            .replace(/(https?:\/\/[^\s]+)/g, '<a href="$1" target="_blank">$1</a>');

    }



    /**

     * 优化的内容渲染

     */

    async renderContent(content, container) {

        if (!content || !container) {

            return;

        }



        // 显示加载状态

        container.innerHTML = `

            <div class="content-loading">

                <div class="loading-spinner"></div>

                <span>正在渲染内容...</span>

            </div>

        `;



        try {

            // 获取渲染器

            const renderer = await this.createOptimizedMarkdownRenderer();

            

            // 渲染内容

            const renderedContent = renderer.render(content);

            

            // 使用requestAnimationFrame优化DOM更新

            requestAnimationFrame(() => {

                container.innerHTML = renderedContent;

                

                // 添加渲染完成标记

                container.setAttribute('data-rendered', renderer.type);

                

                console.log(`内容渲染完成 (${renderer.type}):`, content.length, '字符');

            });



        } catch (error) {

            console.error('内容渲染失败:', error);

            

            // 最后的降级方案 - 纯文本显示

            requestAnimationFrame(() => {

                container.innerHTML = `<pre style="white-space: pre-wrap;">${this.escapeHtml(content)}</pre>`;

                container.setAttribute('data-rendered', 'plaintext');

            });

        }

    }



    /**

     * HTML转义函数

     */

    escapeHtml(text) {

        const map = {

            '&': '&amp;',

            '<': '&lt;',

            '>': '&gt;',

            '"': '&quot;',

            "'": '&#039;'

        };

        

        return text.replace(/[&<>"']/g, function(m) { return map[m]; });

    }



    /**

     * 获取加载统计信息

     */

    getLoadingStats() {

        let loaded = 0;

        let failed = 0;

        let preloading = 0;

        

        for (const [, item] of this.scriptCache) {

            switch (item.status) {

                case 'loaded':

                    loaded++;

                    break;

                case 'failed':

                    failed++;

                    break;

                case 'preloading':

                    preloading++;

                    break;

            }

        }

        

        return {

            loaded,

            failed,

            preloading,

            total: this.scriptCache.size

        };

    }



    /**

     * 清除缓存

     */

    clearCache() {

        this.scriptCache.clear();

        this.loadingPromises.clear();

    }



    /**

     * 预加载内容资源

     */

    preloadContentResources() {

        // 预加载常用的CSS和字体资源

        const resources = [

            'css/optimized-content.css'

        ];

        

        resources.forEach(href => {

            const link = document.createElement('link');

            link.rel = 'preload';

            link.as = 'style';

            link.href = href;

            document.head.appendChild(link);

        });

    }

}