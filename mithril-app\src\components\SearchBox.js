// 使用全局的 Mithril 实例
const m = window.m;

// 导入搜索工具函数
import {
    getPopularTags,
    getAllUniqueTags,
    filterTagSuggestions,
    detectTagInput,
    insertTagAtPosition
} from '../utils/searchUtils.js';

/**
 * 搜索框组件
 */
export const SearchBox = {
    oninit: function(vnode) {
        this.searchQuery = '';
        this.isExpanded = false;
        this.showSuggestions = false;
        this.popularTags = [];

        // 标签自动完成相关状态
        this.showTagSuggestions = false;
        this.tagSuggestions = [];
        this.selectedSuggestionIndex = -1;
        this.tagInputInfo = null;
        this.allTags = [];

        // 获取热门标签和所有标签
        this.updatePopularTags();
        this.updateAllTags();

        // 添加全局键盘事件监听
        this.keyHandler = (e) => {
            if (e.key === 'Escape' && this.searchQuery) {
                this.clearSearch(vnode);
            }
        };
        document.addEventListener('keydown', this.keyHandler);
    },

    onremove: function() {
        // 清理事件监听器
        if (this.keyHandler) {
            document.removeEventListener('keydown', this.keyHandler);
        }
    },
    
    updatePopularTags: function() {
        if (window.memos && window.memos.list) {
            this.popularTags = getPopularTags(window.memos.list, 8);
        }
    },

    updateAllTags: function() {
        if (window.memos && window.memos.list) {
            this.allTags = getAllUniqueTags(window.memos.list);
        }
    },
    
    handleSearch: function(vnode) {
        const query = this.searchQuery.trim();
        if (vnode.attrs.onSearch) {
            vnode.attrs.onSearch(query);
        }
        this.showSuggestions = false;
    },
    
    handleInput: function(vnode, e) {
        this.searchQuery = e.target.value;
        const cursorPosition = e.target.selectionStart;

        // 检测标签输入状态
        this.tagInputInfo = detectTagInput(this.searchQuery, cursorPosition);

        if (this.tagInputInfo && this.tagInputInfo.isAtEnd) {
            // 正在输入标签，显示标签建议
            this.updateAllTags(); // 确保标签列表是最新的
            this.tagSuggestions = filterTagSuggestions(this.allTags, this.tagInputInfo.tag);
            this.showTagSuggestions = this.tagSuggestions.length > 0;
            this.showSuggestions = false; // 隐藏普通建议
            this.selectedSuggestionIndex = -1;
        } else {
            // 不在标签输入状态，显示普通建议
            this.showTagSuggestions = false;
            this.showSuggestions = this.searchQuery.length > 0;
            this.selectedSuggestionIndex = -1;
        }

        // 实时搜索
        if (vnode.attrs.onSearch) {
            vnode.attrs.onSearch(this.searchQuery);
        }

        m.redraw();
    },
    
    handleTagClick: function(vnode, tag) {
        this.searchQuery = `#${tag}`;
        this.handleSearch(vnode);
        this.showSuggestions = false;

        // 更新搜索框的值
        const searchInput = document.querySelector('.search-input');
        if (searchInput) {
            searchInput.value = this.searchQuery;
        }

        m.redraw();
    },

    // 外部调用的方法，用于从其他组件设置搜索查询
    setSearchQuery: function(query) {
        this.searchQuery = query;
        const searchInput = document.querySelector('.search-input');
        if (searchInput) {
            searchInput.value = query;
        }
        m.redraw();
    },
    
    clearSearch: function(vnode) {
        this.searchQuery = '';
        this.showSuggestions = false;
        this.showTagSuggestions = false;
        this.selectedSuggestionIndex = -1;
        this.tagInputInfo = null;
        if (vnode.attrs.onSearch) {
            vnode.attrs.onSearch('');
        }
    },

    handleKeyDown: function(vnode, e) {
        if (this.showTagSuggestions && this.tagSuggestions.length > 0) {
            switch (e.key) {
                case 'ArrowDown':
                    e.preventDefault();
                    this.selectedSuggestionIndex = Math.min(
                        this.selectedSuggestionIndex + 1,
                        this.tagSuggestions.length - 1
                    );
                    m.redraw();
                    break;

                case 'ArrowUp':
                    e.preventDefault();
                    this.selectedSuggestionIndex = Math.max(
                        this.selectedSuggestionIndex - 1,
                        -1
                    );
                    m.redraw();
                    break;

                case 'Enter':
                    e.preventDefault();
                    if (this.selectedSuggestionIndex >= 0) {
                        this.selectTagSuggestion(vnode, this.tagSuggestions[this.selectedSuggestionIndex]);
                    }
                    break;

                case 'Escape':
                    this.showTagSuggestions = false;
                    this.selectedSuggestionIndex = -1;
                    m.redraw();
                    break;
            }
        } else {
            // 普通搜索的键盘处理
            switch (e.key) {
                case 'Enter':
                    this.handleSearch(vnode);
                    break;
                case 'Escape':
                    this.clearSearch(vnode);
                    break;
            }
        }
    },

    selectTagSuggestion: function(vnode, tag) {
        if (!this.tagInputInfo) return;

        const input = document.querySelector('.search-input');
        if (!input) return;

        const result = insertTagAtPosition(
            this.searchQuery,
            this.tagInputInfo.start,
            this.tagInputInfo.end,
            tag
        );

        this.searchQuery = result.text;
        input.value = result.text;

        // 设置光标位置
        setTimeout(() => {
            input.setSelectionRange(result.cursorPosition, result.cursorPosition);
            input.focus();
        }, 0);

        // 隐藏建议
        this.showTagSuggestions = false;
        this.selectedSuggestionIndex = -1;
        this.tagInputInfo = null;

        // 触发搜索
        if (vnode.attrs.onSearch) {
            vnode.attrs.onSearch(this.searchQuery);
        }

        m.redraw();
    },
    
    view: function(vnode) {
        return m('.search-container', [
            // 搜索状态指示器
            this.searchQuery ? m('.search-status', [
                m('.status-text', [
                    this.searchQuery.startsWith('#') ?
                        ['正在显示标签: ', m('strong.tag-name', this.searchQuery)] :
                        ['搜索结果: ', m('strong.search-term', `"${this.searchQuery}"`)]
                ]),
                m('button.status-clear', {
                    onclick: () => this.clearSearch(vnode),
                    title: '显示所有备忘录'
                }, [
                    m('span', '✕'),
                    m('span', '显示全部')
                ])
            ]) : null,

            // 搜索输入框
            m('.search-box', {
                class: this.isExpanded ? 'expanded' : ''
            }, [
                m('input.search-input', {
                    type: 'text',
                    placeholder: '搜索备忘录或标签...',
                    value: this.searchQuery,
                    oninput: (e) => this.handleInput(vnode, e),
                    onfocus: () => {
                        this.isExpanded = true;
                        this.showSuggestions = this.searchQuery.length > 0 || this.popularTags.length > 0;
                    },
                    onblur: () => {
                        // 延迟隐藏建议，以便点击建议项
                        setTimeout(() => {
                            this.isExpanded = false;
                            this.showSuggestions = false;
                            this.showTagSuggestions = false;
                            m.redraw();
                        }, 200);
                    },
                    onkeydown: (e) => this.handleKeyDown(vnode, e)
                }),
                
                // 搜索图标
                m('.search-icon', {
                    onclick: () => this.handleSearch(vnode)
                }, '🔍'),
                
                // 清除按钮 - 更明显的样式
                this.searchQuery ? m('.search-clear', {
                    onclick: () => this.clearSearch(vnode),
                    title: '清除搜索 (ESC)'
                }, [
                    m('span.clear-icon', '✕'),
                    m('span.clear-text', '清除')
                ]) : null
            ]),
            
            // 标签自动完成建议
            this.showTagSuggestions ? m('.search-suggestions.tag-suggestions', [
                m('.suggestions-section', [
                    m('.suggestions-title', [
                        '标签建议',
                        this.tagInputInfo ? m('span.tag-input-hint', ` (输入: #${this.tagInputInfo.tag})`) : null
                    ]),
                    m('.tag-autocomplete-list', this.tagSuggestions.map((tag, index) =>
                        m('.tag-autocomplete-item', {
                            class: index === this.selectedSuggestionIndex ? 'selected' : '',
                            onclick: () => this.selectTagSuggestion(vnode, tag),
                            onmouseenter: () => {
                                this.selectedSuggestionIndex = index;
                                m.redraw();
                            }
                        }, [
                            m('span.tag-icon', '#'),
                            m('span.tag-text', tag),
                            m('span.tag-action', '选择')
                        ])
                    ))
                ])
            ]) :

            // 普通搜索建议和热门标签
            this.showSuggestions ? m('.search-suggestions', [
                // 当前搜索为空时显示热门标签
                !this.searchQuery && this.popularTags.length > 0 ? [
                    m('.suggestions-section', [
                        m('.suggestions-title', '热门标签'),
                        m('.popular-tags', this.popularTags.map(({ tag, count }) =>
                            m('.tag-suggestion', {
                                onclick: () => this.handleTagClick(vnode, tag)
                            }, [
                                m('span.tag-name', `#${tag}`),
                                m('span.tag-count', count)
                            ])
                        ))
                    ])
                ] : null,

                // 搜索提示
                this.searchQuery ? [
                    m('.suggestions-section', [
                        m('.search-tip', [
                            '按 Enter 搜索 "',
                            m('strong', this.searchQuery),
                            '"'
                        ])
                    ])
                ] : null
            ]) : null
        ]);
    }
};
