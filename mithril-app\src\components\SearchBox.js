// 使用全局的 Mithril 实例
const m = window.m;

// 导入搜索工具函数
import { getPopularTags } from '../utils/searchUtils.js';

/**
 * 搜索框组件
 */
export const SearchBox = {
    oninit: function(vnode) {
        this.searchQuery = '';
        this.isExpanded = false;
        this.showSuggestions = false;
        this.popularTags = [];
        
        // 获取热门标签
        this.updatePopularTags();
    },
    
    updatePopularTags: function() {
        if (window.memos && window.memos.list) {
            this.popularTags = getPopularTags(window.memos.list, 8);
        }
    },
    
    handleSearch: function(vnode) {
        const query = this.searchQuery.trim();
        if (vnode.attrs.onSearch) {
            vnode.attrs.onSearch(query);
        }
        this.showSuggestions = false;
    },
    
    handleInput: function(vnode, e) {
        this.searchQuery = e.target.value;
        
        // 实时搜索
        if (vnode.attrs.onSearch) {
            vnode.attrs.onSearch(this.searchQuery);
        }
        
        // 显示建议
        this.showSuggestions = this.searchQuery.length > 0;
        m.redraw();
    },
    
    handleTagClick: function(vnode, tag) {
        this.searchQuery = `#${tag}`;
        this.handleSearch(vnode);
        this.showSuggestions = false;
        m.redraw();
    },
    
    clearSearch: function(vnode) {
        this.searchQuery = '';
        this.showSuggestions = false;
        if (vnode.attrs.onSearch) {
            vnode.attrs.onSearch('');
        }
    },
    
    view: function(vnode) {
        return m('.search-container', [
            // 搜索输入框
            m('.search-box', {
                class: this.isExpanded ? 'expanded' : ''
            }, [
                m('input.search-input', {
                    type: 'text',
                    placeholder: '搜索备忘录或标签...',
                    value: this.searchQuery,
                    oninput: (e) => this.handleInput(vnode, e),
                    onfocus: () => {
                        this.isExpanded = true;
                        this.showSuggestions = this.searchQuery.length > 0 || this.popularTags.length > 0;
                    },
                    onblur: () => {
                        // 延迟隐藏建议，以便点击建议项
                        setTimeout(() => {
                            this.isExpanded = false;
                            this.showSuggestions = false;
                            m.redraw();
                        }, 200);
                    },
                    onkeydown: (e) => {
                        if (e.key === 'Enter') {
                            this.handleSearch(vnode);
                        } else if (e.key === 'Escape') {
                            this.clearSearch(vnode);
                        }
                    }
                }),
                
                // 搜索图标
                m('.search-icon', {
                    onclick: () => this.handleSearch(vnode)
                }, '🔍'),
                
                // 清除按钮
                this.searchQuery ? m('.search-clear', {
                    onclick: () => this.clearSearch(vnode)
                }, '✕') : null
            ]),
            
            // 搜索建议和热门标签
            this.showSuggestions ? m('.search-suggestions', [
                // 当前搜索为空时显示热门标签
                !this.searchQuery && this.popularTags.length > 0 ? [
                    m('.suggestions-section', [
                        m('.suggestions-title', '热门标签'),
                        m('.popular-tags', this.popularTags.map(({ tag, count }) =>
                            m('.tag-suggestion', {
                                onclick: () => this.handleTagClick(vnode, tag)
                            }, [
                                m('span.tag-name', `#${tag}`),
                                m('span.tag-count', count)
                            ])
                        ))
                    ])
                ] : null,
                
                // 搜索提示
                this.searchQuery ? [
                    m('.suggestions-section', [
                        m('.search-tip', [
                            '按 Enter 搜索 "',
                            m('strong', this.searchQuery),
                            '"'
                        ])
                    ])
                ] : null
            ]) : null
        ]);
    }
};
