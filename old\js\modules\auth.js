/**
 * 认证模块 - 现代化重构版本
 * 处理用户登录、登出和认证状态管理
 */

import { STORAGE_KEYS } from '../config.js';
import { getElement, showNotification, generateId } from './utils.js';
import { APIService } from './api.js';

/**
 * 认证状态管理类
 */
class AuthManager {
    constructor() {
        this.isLoggedIn = false;
        this.currentUser = null;
        this.loginAttempts = 0;
        this.maxLoginAttempts = 5;
        this.lockoutTime = 15 * 60 * 1000; // 15分钟
        this.sessionTimeout = 24 * 60 * 60 * 1000; // 24小时
        this.listeners = new Set();
        this.init();
    }

    /**
     * 初始化认证模块
     */
    init() {
        this.checkLoginStatus();
        this.bindEvents();
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        const loginBtn = getElement('#loginBtn');
        const logoutBtn = getElement('#logoutBtn');
        const loginForm = getElement('#loginForm');
        const closeLogin = getElement('#closeLogin');

        if (loginBtn) {
            loginBtn.addEventListener('click', () => this.showLogin());
        }

        if (logoutBtn) {
            logoutBtn.addEventListener('click', () => this.logout());
        }

        if (loginForm) {
            loginForm.addEventListener('submit', (e) => this.handleLogin(e));
        }

        if (closeLogin) {
            closeLogin.addEventListener('click', () => this.hideLogin());
        }
    }

    /**
     * 显示登录模态框
     */
    showLogin() {
        const loginModal = getElement('#loginModal');
        if (loginModal) {
            loginModal.style.display = 'flex';
        }
    }

    /**
     * 隐藏登录模态框
     */
    hideLogin() {
        const loginModal = getElement('#loginModal');
        if (loginModal) {
            loginModal.style.display = 'none';
        }
    }

    /**
     * 检查是否被锁定
     */
    isLockedOut() {
        const lockoutData = localStorage.getItem('auth_lockout');
        if (!lockoutData) return false;
        
        const { timestamp, attempts } = JSON.parse(lockoutData);
        const now = Date.now();
        
        if (attempts >= this.maxLoginAttempts && (now - timestamp) < this.lockoutTime) {
            return true;
        }
        
        // 锁定时间已过，清除锁定状态
        if ((now - timestamp) >= this.lockoutTime) {
            localStorage.removeItem('auth_lockout');
            this.loginAttempts = 0;
        }
        
        return false;
    }

    /**
     * 记录登录失败
     */
    recordFailedLogin() {
        this.loginAttempts++;
        const lockoutData = {
            timestamp: Date.now(),
            attempts: this.loginAttempts
        };
        localStorage.setItem('auth_lockout', JSON.stringify(lockoutData));
    }

    /**
     * 清除登录失败记录
     */
    clearFailedLogins() {
        this.loginAttempts = 0;
        localStorage.removeItem('auth_lockout');
    }

    /**
     * 处理登录
     * @param {Event} e - 表单提交事件
     */
    async handleLogin(e) {
        e.preventDefault();
        
        // 检查是否被锁定
        if (this.isLockedOut()) {
            const remainingTime = Math.ceil((this.lockoutTime - (Date.now() - JSON.parse(localStorage.getItem('auth_lockout')).timestamp)) / 60000);
            this.showError(`登录失败次数过多，请等待 ${remainingTime} 分钟后重试`);
            return;
        }

        const usernameInput = getElement('#loginUsername');
        const passwordInput = getElement('#loginPassword');
        const submitBtn = getElement('#loginForm button[type="submit"]');
        
        const username = usernameInput?.value.trim();
        const password = passwordInput?.value.trim();

        if (!username || !password) {
            this.showError('请输入用户名和密码');
            return;
        }

        // 显示加载状态
        if (submitBtn) {
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<span class="animate-spin">⏳</span> 登录中...';
        }

        try {
            // 从Vika用户表验证用户
            const userResult = await APIService.getUserByUsername(username);
            
            if (userResult && userResult.data && userResult.data.records && userResult.data.records.length > 0) {
                const userRecord = userResult.data.records[0];
                const userData = userRecord.fields;
                
                // 验证密码（注意：实际应用中应该使用加密密码）
                if (userData.密码 === password) {
                    const user = { 
                        id: userRecord.recordId,
                        username: userData.用户名,
                        nickname: userData.昵称 || userData.用户名,
                        phone: userData.手机号 || '',
                        avatar: userData.头像 || `https://ui-avatars.com/api/?name=${encodeURIComponent(userData.昵称 || userData.用户名)}&background=3B82F6&color=fff`,
                        token: 'vika-token-' + Date.now(),
                        loginTime: new Date().toISOString(),
                        vikaId: userRecord.recordId
                    };
                    
                    this.setCurrentUser(user);
                    this.clearFailedLogins();
                    this.hideLogin();
                    
                    showNotification(`欢迎回来，${userData.昵称 || userData.用户名}！`, 'success', 3000);
                    
                    // 触发自定义事件
                    this.notifyListeners('login', user);
                    document.dispatchEvent(new CustomEvent('auth:login', { detail: user }));
                    
                    // 清空表单
                    if (usernameInput) usernameInput.value = '';
                    if (passwordInput) passwordInput.value = '';
                    
                } else {
                    // 密码错误
                    this.recordFailedLogin();
                    const remainingAttempts = this.maxLoginAttempts - this.loginAttempts;
                    
                    if (remainingAttempts > 0) {
                        this.showError(`密码错误，还有 ${remainingAttempts} 次尝试机会`);
                    } else {
                        this.showError('登录失败次数过多，账户已被锁定15分钟');
                    }
                }
            } else {
                // 用户不存在
                this.recordFailedLogin();
                const remainingAttempts = this.maxLoginAttempts - this.loginAttempts;
                
                if (remainingAttempts > 0) {
                    this.showError(`用户不存在，还有 ${remainingAttempts} 次尝试机会`);
                } else {
                    this.showError('登录失败次数过多，账户已被锁定15分钟');
                }
            }
        } catch (error) {
            console.error('登录失败:', error);
            
            // 根据错误类型显示不同提示
            if (error.message.includes('401')) {
                this.showError('API认证失败，请联系管理员');
            } else if (error.message.includes('403')) {
                this.showError('API权限不足，请联系管理员');
            } else {
                this.showError('网络连接失败，请检查网络后重试');
            }
        } finally {
            // 恢复按钮状态
            if (submitBtn) {
                submitBtn.disabled = false;
                submitBtn.innerHTML = '登录';
            }
        }
    }

    /**
     * 设置当前用户
     * @param {Object} user - 用户信息
     */
    setCurrentUser(user) {
        this.currentUser = user;
        this.isLoggedIn = true;
        localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(user));
        this.updateUI();
    }

    /**
     * 获取当前用户
     * @returns {Object|null} 当前用户信息
     */
    getCurrentUser() {
        return this.currentUser;
    }

    /**
     * 检查登录状态
     */
    checkLoginStatus() {
        const userStr = localStorage.getItem(STORAGE_KEYS.USER);
        if (userStr) {
            try {
                this.currentUser = JSON.parse(userStr);
                this.isLoggedIn = true;
            } catch (error) {
                console.error('解析用户信息失败:', error);
                this.logout();
            }
        }
        this.updateUI();
    }

    /**
     * 登出
     */
    logout() {
        const user = this.currentUser;
        this.currentUser = null;
        this.isLoggedIn = false;
        localStorage.removeItem(STORAGE_KEYS.USER);
        this.clearFailedLogins();
        this.updateUI();
        
        showNotification('已安全退出登录', 'info', 2000);
        
        // 触发自定义事件
        this.notifyListeners('logout', user);
        document.dispatchEvent(new CustomEvent('auth:logout', { detail: user }));
    }

    /**
     * 添加认证状态监听器
     */
    addListener(callback) {
        this.listeners.add(callback);
    }

    /**
     * 移除认证状态监听器
     */
    removeListener(callback) {
        this.listeners.delete(callback);
    }

    /**
     * 通知监听器
     */
    notifyListeners(event, data) {
        this.listeners.forEach(callback => {
            try {
                callback(event, data);
            } catch (error) {
                console.error('认证监听器错误:', error);
            }
        });
    }

    /**
     * 检查会话是否过期
     */
    isSessionExpired() {
        if (!this.currentUser || !this.currentUser.loginTime) {
            return true;
        }
        
        const loginTime = new Date(this.currentUser.loginTime).getTime();
        const now = Date.now();
        
        return (now - loginTime) > this.sessionTimeout;
    }

    /**
     * 刷新会话
     */
    refreshSession() {
        if (this.currentUser) {
            this.currentUser.loginTime = new Date().toISOString();
            localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(this.currentUser));
        }
    }

    /**
     * 获取认证状态信息
     */
    getAuthStatus() {
        return {
            isLoggedIn: this.isLoggedIn,
            user: this.currentUser,
            loginAttempts: this.loginAttempts,
            isLockedOut: this.isLockedOut(),
            sessionExpired: this.isSessionExpired(),
            hasValidSession: this.isLoggedIn && !this.isSessionExpired()
        };
    }

    /**
     * 更新UI显示
     */
    updateUI() {
        const loginBtn = getElement('#loginBtn');
        const logoutBtn = getElement('#logoutBtn');
        const userInfo = getElement('#userInfo');
        const memoForm = getElement('#memoForm');

        if (this.isLoggedIn) {
            if (loginBtn) loginBtn.style.display = 'none';
            if (logoutBtn) logoutBtn.style.display = 'block';
            if (userInfo) {
                userInfo.textContent = `欢迎, ${this.currentUser.username}`;
                userInfo.style.display = 'block';
            }
            if (memoForm) memoForm.style.display = 'block';
        } else {
            if (loginBtn) loginBtn.style.display = 'block';
            if (logoutBtn) logoutBtn.style.display = 'none';
            if (userInfo) userInfo.style.display = 'none';
            if (memoForm) memoForm.style.display = 'none';
        }
    }

    /**
     * 显示错误信息
     * @param {string} message - 错误消息
     */
    showError(message) {
        const errorMsg = getElement('#loginError');
        if (errorMsg) {
            errorMsg.textContent = message;
            errorMsg.style.display = 'block';
            setTimeout(() => {
                errorMsg.style.display = 'none';
            }, 3000);
        }
    }

    /**
     * 显示成功信息
     * @param {string} message - 成功消息
     */
    showSuccess(message) {
        // 可以添加全局提示组件
        console.log(message);
    }
}

// 创建单例实例
const auth = new AuthManager();

export { AuthManager, auth };
export default auth;