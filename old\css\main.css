/**

 * 主样式文件

 * 导入所有组件样式和基础样式

 */



/* 全局样式 */

@import url('./components/variables.css');

@import url('./components/base.css');

@import url('./components/cards.css');

@import url('./components/buttons.css');

@import url('./components/forms.css');

@import url('./components/modals.css');

@import url('./components/lazyload.css');

@import url('./components/login-banner.css');



/* 链接样式 */

.memo-link {

    word-break: break-all;

    max-width: 100%;

    display: inline-block;

    color: #007bff;

    text-decoration: none;

    overflow-wrap: break-word;

}



.memo-link:hover {

    text-decoration: underline;

}



/* 响应式设计 */

@media (max-width: 768px) {

    .memo-link {

        font-size: 0.875rem;

    }

}



/* 自定义样式 */

.main-container {

  min-height: 100vh;

  background: linear-gradient(135deg, var(--color-bg-secondary, var(--color-gray-50)) 0%, var(--color-bg, white) 100%);

}



.content-wrapper {

  max-width: 42rem;

  margin: 0 auto;

  padding: var(--space-4);

}



/* 主题切换器样式 */

.theme-switcher {

  position: fixed;

  top: var(--space-4);

  right: var(--space-4);

  display: flex;

  gap: var(--space-2);

  z-index: var(--z-fixed);

}



.theme-btn {

  width: 2.5rem;

  height: 2.5rem;

  border-radius: var(--radius-full);

  border: none;

  cursor: pointer;

  transition: all var(--duration-fast) var(--ease-out);

  box-shadow: var(--shadow-md);

  display: flex;

  align-items: center;

  justify-content: center;

}



.theme-btn:hover {

  transform: translateY(-1px);

  box-shadow: var(--shadow-lg);

}



.theme-btn.active {

  transform: scale(1.1);

  box-shadow: var(--shadow-xl);

}



.theme-btn svg {

  width: 1.25rem;

  height: 1.25rem;

}



/* 发布区域样式 */

.post-section {

  margin-bottom: var(--space-6);

}



.post-card {

  background: var(--color-bg, white);

  border-radius: var(--radius-xl);

  padding: var(--space-6);

  box-shadow: var(--shadow-md);

  border: 1px solid var(--color-border, var(--color-gray-200));

  transition: all var(--duration-normal) var(--ease-out);

}



.post-card:hover {

  box-shadow: var(--shadow-lg);

}



.post-textarea {

  width: 100%;

  min-height: 6rem;

  padding: var(--space-4);

  border: 1px solid var(--color-border, var(--color-gray-300));

  border-radius: var(--radius-lg);

  font-size: var(--text-base);

  line-height: 1.6;

  resize: vertical;

  transition: all var(--duration-fast) var(--ease-out);

  background-color: var(--color-bg, white);

  color: var(--color-text, var(--color-gray-900));

}



.post-textarea:focus {

  outline: none;

  border-color: var(--color-primary);

  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);

}



.post-textarea::placeholder {

  color: var(--color-text-tertiary, var(--color-gray-400));

}



.post-actions {

  display: flex;

  align-items: center;

  justify-content: space-between;

  margin-top: var(--space-4);

}



.post-options {

  display: flex;

  align-items: center;

  gap: var(--space-3);

}



/* 加载状态 */

.loading-container {

  display: flex;

  flex-direction: column;

  align-items: center;

  justify-content: center;

  padding: var(--space-8);

  color: var(--color-text-secondary, var(--color-gray-600));

}



.loading-spinner {

  width: 2.5rem;

  height: 2.5rem;

  border: 3px solid var(--color-border-light, var(--color-gray-200));

  border-top: 3px solid var(--color-primary);

  border-radius: var(--radius-full);

  animation: spin 1s linear infinite;

  margin-bottom: var(--space-3);

}



.loading-text {

  font-size: var(--text-sm);

  font-weight: 500;

}



/* 空状态 */

.empty-state {

  text-align: center;

  padding: var(--space-12) var(--space-6);

  background: var(--color-bg, white);

  border-radius: var(--radius-xl);

  box-shadow: var(--shadow-sm);

  border: 1px solid var(--color-border, var(--color-gray-200));

}



.empty-icon {

  width: 4rem;

  height: 4rem;

  margin: 0 auto var(--space-4);

  color: var(--color-text-tertiary, var(--color-gray-400));

}



.empty-title {

  font-size: var(--text-xl);

  font-weight: 600;

  color: var(--color-text, var(--color-gray-900));

  margin-bottom: var(--space-2);

}



.empty-description {

  color: var(--color-text-secondary, var(--color-gray-600));

  margin-bottom: var(--space-6);

}



/* 底部区域 */

.footer {

  margin-top: var(--space-12);

  padding: var(--space-6) 0;

  border-top: 1px solid var(--color-border-light, var(--color-gray-100));

  background: var(--color-bg, white);

}



.footer-content {

  display: flex;

  align-items: center;

  justify-content: space-between;

  max-width: 42rem;

  margin: 0 auto;

  padding: 0 var(--space-4);

}



.footer-text {

  font-size: var(--text-sm);

  color: var(--color-text-secondary, var(--color-gray-600));

}



.footer-actions {

  display: flex;

  gap: var(--space-2);

}



/* 图片放大效果 */

.image-zoom-overlay {

  position: fixed;

  top: 0;

  left: 0;

  right: 0;

  bottom: 0;

  background: rgba(0, 0, 0, 0.9);

  display: flex;

  align-items: center;

  justify-content: center;

  z-index: var(--z-modal);

  cursor: zoom-out;

  opacity: 0;

  visibility: hidden;

  transition: all var(--duration-normal) var(--ease-out);

}



.image-zoom-overlay.show {

  opacity: 1;

  visibility: visible;

}



.image-zoom-overlay img {

  max-width: 90vw;

  max-height: 90vh;

  object-fit: contain;

  border-radius: var(--radius-lg);

  box-shadow: var(--shadow-2xl);

}



/* 响应式设计 */

@media (max-width: 768px) {

  .content-wrapper {

    padding: var(--space-2);

  }

  

  .theme-switcher {

    position: static;

    justify-content: center;

    margin-bottom: var(--space-4);

  }

  

  .post-card {

    padding: var(--space-4);

    border-radius: var(--radius-lg);

  }

  

  .post-actions {

    flex-direction: column;

    gap: var(--space-3);

    align-items: stretch;

  }

  

  .post-options {

    justify-content: center;

  }

  

  .footer-content {

    flex-direction: column;

    gap: var(--space-3);

    text-align: center;

  }

}



@media (max-width: 480px) {

  .content-wrapper {

    padding: var(--space-1);

  }

  

  .post-card,

  .memo-card,

  .empty-state {

    margin: 0 calc(-1 * var(--space-1));

    border-radius: var(--radius-lg);

  }

}