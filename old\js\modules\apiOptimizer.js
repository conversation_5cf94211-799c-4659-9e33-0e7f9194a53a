/**

 * API优化器

 * 提供缓存、批量请求、预加载等优化功能

 */



export class APIOptimizer {

    constructor(config = {}) {

        this.cache = new Map();

        this.userCache = new Map();

        this.requestQueue = new Map();

        

        // 默认缓存时间配置

        this.cacheExpiry = config.cacheExpiry || 5 * 60 * 1000; // 5分钟缓存

        this.userCacheExpiry = config.userCacheExpiry || 30 * 60 * 1000; // 30分钟用户缓存

        this.searchCacheExpiry = config.searchCacheExpiry || 2 * 60 * 1000; // 2分钟搜索缓存

        

        // 请求优先级配置

        this.requestPriorities = {

            'critical': 1,    // 关键请求（如当前页面内容）

            'high': 2,        // 高优先级（如用户信息）

            'normal': 3,      // 普通请求（如列表数据）

            'low': 4          // 低优先级（如预加载）

        };

        

        // 网络状态

        this.networkStatus = this.getNetworkStatus();

        this.setupNetworkListener();

    }



    /**

     * 获取网络状态

     */

    getNetworkStatus() {

        if ('connection' in navigator) {

            return {

                effectiveType: navigator.connection.effectiveType,

                downlink: navigator.connection.downlink,

                saveData: navigator.connection.saveData

            };

        }

        return {

            effectiveType: 'unknown',

            downlink: 0,

            saveData: false

        };

    }



    /**

     * 设置网络状态监听器

     */

    setupNetworkListener() {

        if ('connection' in navigator) {

            navigator.connection.addEventListener('change', () => {

                this.networkStatus = this.getNetworkStatus();

                console.log('网络状态变化:', this.networkStatus);

            });

        }

    }



    /**

     * 生成缓存键

     */

    getCacheKey(endpoint, params) {

        const sortedParams = Object.keys(params || {})

            .sort()

            .map(key => `${key}=${params[key]}`)

            .join('&');

        return `${endpoint}?${sortedParams}`;

    }



    /**

     * 检查缓存是否有效

     */

    isCacheValid(cacheItem, expiry = this.cacheExpiry) {

        return cacheItem && (Date.now() - cacheItem.timestamp < expiry);

    }



    /**

     * 设置缓存（支持不同类型的数据和过期时间）

     */

    setCache(key, data, type = 'default', expiry) {

        // 根据数据类型设置不同的默认过期时间

        if (!expiry) {

            switch (type) {

                case 'user':

                    expiry = this.userCacheExpiry;

                    break;

                case 'search':

                    expiry = this.searchCacheExpiry;

                    break;

                case 'memo':

                default:

                    expiry = this.cacheExpiry;

                    break;

            }

        }

        

        this.cache.set(key, {

            data,

            timestamp: Date.now(),

            expiry,

            type

        });

    }



    /**

     * 获取缓存

     */

    getCache(key, expiry) {

        const cacheItem = this.cache.get(key);

        if (this.isCacheValid(cacheItem, expiry)) {

            console.log('使用缓存数据:', key);

            return cacheItem.data;

        }

        return null;

    }



    /**

     * 清除过期缓存

     */

    clearExpiredCache() {

        const now = Date.now();

        for (const [key, item] of this.cache.entries()) {

            if (now - item.timestamp > item.expiry) {

                this.cache.delete(key);

            }

        }

    }



    /**

     * 防重复请求

     */

    async deduplicateRequest(key, requestFn, priority = 'normal') {

        // 如果已有相同请求在进行中，等待结果

        if (this.requestQueue.has(key)) {

            console.log('等待重复请求完成:', key);

            return await this.requestQueue.get(key);

        }



        // 根据网络状态和优先级调整请求策略

        const shouldThrottle = this.networkStatus.effectiveType === 'slow-2g' || 

                              this.networkStatus.effectiveType === '2g' ||

                              (this.networkStatus.saveData && priority === 'low');

        

        if (shouldThrottle && priority === 'low') {

            throw new Error('网络状况不佳，跳过低优先级请求');

        }



        // 创建新请求

        const promise = requestFn();

        this.requestQueue.set(key, promise);



        try {

            const result = await promise;

            this.requestQueue.delete(key);

            return result;

        } catch (error) {

            this.requestQueue.delete(key);

            throw error;

        }

    }



    /**

     * 优化的备忘录获取

     */

    async getOptimizedMemos(page = 1, pageSize = 10, priority = 'normal') {

        const cacheKey = this.getCacheKey('memos', { page, pageSize });



        // 检查缓存

        const cachedData = this.getCache(cacheKey, this.cacheExpiry);

        if (cachedData) {

            return cachedData;

        }



        // 防重复请求

        return await this.deduplicateRequest(cacheKey, async () => {

            const params = new URLSearchParams({

                viewId: 'viwYoodJk54Xt',

                fieldKey: 'name',

                pageSize: pageSize.toString(),

                cellFormat: 'json'

            });



            if (page > 1) {

                params.append('pageNum', page.toString());

            }



            const response = await fetch(`https://api.vika.cn/fusion/v1/datasheets/dstCo3t04QBY3RweuR/records?${params}`, {

                headers: {

                    'Authorization': 'Bearer uskcZUvxWXvLIPXN0hUC6DK',

                    'Content-Type': 'application/json'

                }

            });



            if (!response.ok) {

                throw new Error(`API请求失败: ${response.status}`);

            }



            const data = await response.json();



            // 缓存结果

            this.setCache(cacheKey, data, 'memo');



            // 更新性能统计

            if (this.performanceStats) {

                this.performanceStats.apiCalls++;

            }



            return data;

        }, priority);

    }



    /**

     * 批量获取用户信息（带缓存）

     */

    async getBatchUsers(userIds) {

        const uncachedUserIds = [];

        const cachedUsers = {};



        // 检查哪些用户已缓存

        userIds.forEach(userId => {

            const cachedUser = this.userCache.get(userId);

            if (this.isCacheValid(cachedUser, this.userCacheExpiry)) {

                cachedUsers[userId] = cachedUser.data;

            } else {

                uncachedUserIds.push(userId);

            }

        });



        // 如果所有用户都已缓存，直接返回

        if (uncachedUserIds.length === 0) {

            console.log('所有用户信息已缓存');

            return cachedUsers;

        }



        // 批量获取未缓存的用户

        const cacheKey = this.getCacheKey('users', { ids: uncachedUserIds.sort().join(',') });



        const newUsers = await this.deduplicateRequest(cacheKey, async () => {

            console.log('批量获取用户信息:', uncachedUserIds);



            const response = await fetch(`https://api.vika.cn/fusion/v1/datasheets/dst13vlcxlMi1EanCl/records?pageSize=100&fieldKey=name`, {

                headers: {

                    'Authorization': 'Bearer uskcZUvxWXvLIPXN0hUC6DK',

                    'Content-Type': 'application/json'

                }

            });



            if (!response.ok) {

                throw new Error(`用户API请求失败: ${response.status}`);

            }



            const data = await response.json();

            const users = {};



            if (data && data.data && data.data.records) {

                data.data.records.forEach(userRecord => {

                    if (uncachedUserIds.includes(userRecord.recordId)) {

                        const userData = userRecord.fields;

                        const userInfo = {

                            username: userData.用户名 || '用户',

                            nickname: userData.昵称 || userData.用户名 || '用户',

                            id: userRecord.recordId,

                            avatar: userData.头像 || `https://ui-avatars.com/api/?name=${encodeURIComponent(userData.昵称 || userData.用户名 || '用户')}&background=3B82F6&color=fff`

                        };



                        users[userRecord.recordId] = userInfo;



                        // 缓存用户信息

                        this.userCache.set(userRecord.recordId, {

                            data: userInfo,

                            timestamp: Date.now()

                        });

                    }

                });

            }



            return users;

        });



        // 合并缓存和新获取的用户信息

        return { ...cachedUsers, ...newUsers };

    }



    /**

     * 预加载下一页数据

     */

    async preloadNextPage(currentPage, pageSize) {

        const nextPage = currentPage + 1;

        const cacheKey = this.getCacheKey('memos', { page: nextPage, pageSize });



        // 如果下一页已缓存，跳过

        if (this.getCache(cacheKey)) {

            return;

        }



        // 异步预加载，不阻塞当前操作

        setTimeout(async () => {

            try {

                console.log('预加载下一页数据:', nextPage);

                await this.getOptimizedMemos(nextPage, pageSize);

            } catch (error) {

                console.warn('预加载失败:', error);

            }

        }, 1000);

    }



    /**

     * 获取缓存统计

     */

    getCacheStats() {

        let memoCacheSize = 0;

        let userCacheSize = 0;

        let searchCacheSize = 0;

        let otherCacheSize = 0;

        

        for (const [, item] of this.cache) {

            switch (item.type) {

                case 'memo':

                    memoCacheSize++;

                    break;

                case 'user':

                    userCacheSize++;

                    break;

                case 'search':

                    searchCacheSize++;

                    break;

                default:

                    otherCacheSize++;

                    break;

            }

        }

        

        return {

            memoCacheSize,

            userCacheSize,

            searchCacheSize,

            otherCacheSize,

            total: this.cache.size

        };

    }



    /**

     * 清除所有缓存

     */

    clearAllCache() {

        this.cache.clear();

        this.userCache.clear();

        console.log('所有缓存已清除');

    }



    /**

     * 智能缓存失效 - 根据操作类型清除相关缓存

     */

    invalidateRelatedCache(operation, params = {}) {

        console.log('缓存失效:', operation, params);

        

        switch (operation) {

            case 'memo_created':

            case 'memo_updated':

            case 'memo_deleted':

                // 清除所有备忘录相关缓存

                for (const key of this.cache.keys()) {

                    if (key.startsWith('memos') || key.startsWith('search')) {

                        this.cache.delete(key);

                    }

                }

                break;

                

            case 'user_updated':

                // 清除特定用户缓存

                if (params.userId) {

                    for (const key of this.userCache.keys()) {

                        if (key.includes(params.userId)) {

                            this.userCache.delete(key);

                        }

                    }

                }

                break;

                

            default:

                console.warn('未知的缓存失效操作:', operation);

        }

    }



    /**

     * 多页预加载

     */

    async preloadMultiplePages(currentPage, pageSize, count) {

        const preloadPromises = [];

        

        for (let i = 1; i <= count; i++) {

            const nextPage = currentPage + i;

            const cacheKey = this.getCacheKey('memos', { page: nextPage, pageSize });

            

            // 检查是否已缓存

            if (!this.getCache(cacheKey)) {

                // 添加到预加载队列（低优先级）

                preloadPromises.push(

                    this.getOptimizedMemos(nextPage, pageSize, 'low')

                        .catch(err => console.warn(`预加载第${nextPage}页失败:`, err))

                );

            }

        }

        

        // 并行执行预加载

        if (preloadPromises.length > 0) {

            console.log(`开始预加载${preloadPromises.length}页数据...`);

            await Promise.all(preloadPromises);

            console.log('预加载完成');

        }

    }



    /**

     * 缓存预热 - 根据用户行为预测可能需要的数据

     */

    async warmupCache(predictedPages = [], predictedUserIds = []) {

        const warmupPromises = [];

        

        // 预热页面数据

        for (const page of predictedPages) {

            warmupPromises.push(

                this.getOptimizedMemos(page, 10, 'low')

                    .catch(err => console.warn(`缓存预热页面${page}失败:`, err))

            );

        }

        

        // 预热用户数据

        if (predictedUserIds.length > 0) {

            warmupPromises.push(

                this.getBatchUsers(predictedUserIds)

                    .catch(err => console.warn('缓存预热用户数据失败:', err))

            );

        }

        

        if (warmupPromises.length > 0) {

            console.log(`开始缓存预热，共${warmupPromises.length}个任务...`);

            await Promise.all(warmupPromises);

            console.log('缓存预热完成');

        }

    }



    /**

     * 获取优化的备忘录数据（包含用户信息）

     */

    async getOptimizedMemosWithUsers(page = 1, pageSize = 10) {

        // 获取备忘录数据

        const memoData = await this.getOptimizedMemos(page, pageSize);



        if (!memoData || !memoData.data || !memoData.data.records) {

            return memoData;

        }



        // 提取所有用户ID

        const userIds = [...new Set(

            memoData.data.records

                .map(record => record.fields.用户)

                .filter(userId => userId)

        )];



        // 批量获取用户信息

        const users = await this.getBatchUsers(userIds);



        // 合并数据

        const enrichedRecords = memoData.data.records.map(record => ({

            ...record,

            userInfo: users[record.fields.用户] || {

                username: '未知用户',

                nickname: '未知用户',

                avatar: 'https://ui-avatars.com/api/?name=Unknown&background=gray&color=fff'

            }

        }));



        // 预加载下一页

        this.preloadNextPage(page, pageSize);



        return {

            ...memoData,

            data: {

                ...memoData.data,

                records: enrichedRecords

            }

        };

    }



    /**

     * 搜索优化 - 带缓存的搜索功能

     */

    async searchMemos(keyword, page = 1, pageSize = 10) {

        const cacheKey = this.getCacheKey('search', { keyword, page, pageSize });



        // 检查搜索缓存

        const cachedResult = this.getCache(cacheKey, 2 * 60 * 1000); // 搜索缓存2分钟

        if (cachedResult) {

            return cachedResult;

        }



        return await this.deduplicateRequest(cacheKey, async () => {

            const params = new URLSearchParams({

                viewId: 'viwYoodJk54Xt',

                fieldKey: 'name',

                pageSize: pageSize.toString(),

                cellFormat: 'json',

                filterByFormula: `SEARCH("${keyword}", {内容}) > 0`

            });



            if (page > 1) {

                params.append('pageNum', page.toString());

            }



            const response = await fetch(`https://api.vika.cn/fusion/v1/datasheets/dstCo3t04QBY3RweuR/records?${params}`, {

                headers: {

                    'Authorization': 'Bearer uskcZUvxWXvLIPXN0hUC6DK',

                    'Content-Type': 'application/json'

                }

            });



            if (!response.ok) {

                throw new Error(`搜索API请求失败: ${response.status}`);

            }



            const data = await response.json();



            // 缓存搜索结果

            this.setCache(cacheKey, data, 2 * 60 * 1000);



            return data;

        });

    }



    /**

     * 性能监控

     */

    startPerformanceMonitoring() {

        // 定期清理过期缓存

        setInterval(() => {

            this.clearExpiredCache();

        }, 5 * 60 * 1000); // 每5分钟清理一次



        // 性能统计

        this.performanceStats = {

            cacheHits: 0,

            cacheMisses: 0,

            apiCalls: 0,

            startTime: Date.now()

        };



        console.log('API优化器性能监控已启动');

    }



    /**

     * 获取性能报告

     */

    getPerformanceReport() {

        if (!this.performanceStats) {

            return null;

        }



        const runtime = Date.now() - this.performanceStats.startTime;

        const cacheHitRate = this.performanceStats.cacheHits /

            (this.performanceStats.cacheHits + this.performanceStats.cacheMisses) * 100;



        return {

            runtime: Math.round(runtime / 1000), // 秒

            cacheHitRate: Math.round(cacheHitRate * 100) / 100, // 百分比

            totalRequests: this.performanceStats.cacheHits + this.performanceStats.cacheMisses,

            apiCalls: this.performanceStats.apiCalls,

            cacheStats: this.getCacheStats()

        };

    }



    /**

     * 用户行为追踪

     */

    trackUserBehavior(action, data = {}) {

        // 记录用户行为数据

        const behaviorData = {

            action,

            data,

            timestamp: Date.now(),

            url: window.location.href

        };

        

        // 获取现有的行为数据

        let behaviorHistory = JSON.parse(localStorage.getItem('userBehaviorHistory') || '[]');

        

        // 添加新的行为记录

        behaviorHistory.push(behaviorData);

        

        // 只保留最近100条记录

        if (behaviorHistory.length > 100) {

            behaviorHistory = behaviorHistory.slice(-100);

        }

        

        // 保存到本地存储

        localStorage.setItem('userBehaviorHistory', JSON.stringify(behaviorHistory));

        

        console.log('用户行为已记录:', behaviorData);

    }



    /**

     * 获取用户行为历史

     */

    getUserBehaviorHistory() {

        return JSON.parse(localStorage.getItem('userBehaviorHistory') || '[]');

    }



    /**

     * 基于用户行为的个性化推荐

     */

    async getPersonalizedRecommendations() {

        try {

            // 获取用户行为历史

            const behaviorHistory = this.getUserBehaviorHistory();

            

            // 分析用户偏好

            const userPreferences = this.analyzeUserPreferences(behaviorHistory);

            

            // 根据偏好获取推荐内容

            const recommendations = await this.fetchRecommendations(userPreferences);

            

            return recommendations;

        } catch (error) {

            console.warn('获取个性化推荐失败:', error);

            return [];

        }

    }



    /**

     * 分析用户偏好

     */

    analyzeUserPreferences(behaviorHistory) {

        const preferences = {

            favoriteTags: {},

            viewedPages: {},

            searchKeywords: {},

            readingTime: []

        };

        

        behaviorHistory.forEach(record => {

            switch (record.action) {

                case 'view_page':

                    // 统计查看页面的次数

                    const pageId = record.data.pageId;

                    if (pageId) {

                        preferences.viewedPages[pageId] = (preferences.viewedPages[pageId] || 0) + 1;

                    }

                    break;

                    

                case 'search':

                    // 统计搜索关键词

                    const keyword = record.data.keyword;

                    if (keyword) {

                        preferences.searchKeywords[keyword] = (preferences.searchKeywords[keyword] || 0) + 1;

                    }

                    break;

                    

                case 'read_time':

                    // 记录阅读时间

                    const readTime = record.data.readTime;

                    if (readTime && readTime > 5000) { // 只记录超过5秒的阅读

                        preferences.readingTime.push(readTime);

                    }

                    break;

            }

        });

        

        return preferences;

    }



    /**

     * 根据用户偏好获取推荐内容

     */

    async fetchRecommendations(preferences) {

        // 简化实现：基于查看历史推荐相似内容

        // 在实际应用中，这里会调用推荐算法API

        

        // 获取最受欢迎的页面

        const popularPages = Object.entries(preferences.viewedPages)

            .sort((a, b) => b[1] - a[1])

            .slice(0, 5)

            .map(([pageId, count]) => pageId);

        

        // 获取最常搜索的关键词

        const popularKeywords = Object.entries(preferences.searchKeywords)

            .sort((a, b) => b[1] - a[1])

            .slice(0, 5)

            .map(([keyword, count]) => keyword);

        

        // 构建推荐结果

        const recommendations = [];

        

        // 基于热门页面获取相关内容

        for (const pageId of popularPages) {

            try {

                // 这里应该调用实际的推荐API

                // 暂时使用模拟数据

                recommendations.push({

                    id: `rec_${pageId}`,

                    type: 'related_content',

                    title: `与"${pageId}"相关的内容`,

                    reason: `基于您的浏览历史推荐`

                });

            } catch (error) {

                console.warn('获取相关推荐失败:', error);

            }

        }

        

        // 基于热门关键词获取相关内容

        for (const keyword of popularKeywords) {

            try {

                // 这里应该调用实际的推荐API

                // 暂时使用模拟数据

                recommendations.push({

                    id: `rec_kw_${keyword}`,

                    type: 'keyword_related',

                    title: `关于"${keyword}"的内容`,

                    reason: `基于您的搜索历史推荐`

                });

            } catch (error) {

                console.warn('获取关键词推荐失败:', error);

            }

        }

        

        return recommendations;

    }



    /**

     * 记录页面浏览

     */

    recordPageView(pageId) {

        this.trackUserBehavior('view_page', { pageId });

    }



    /**

     * 记录搜索行为

     */

    recordSearch(keyword) {

        this.trackUserBehavior('search', { keyword });

    }



    /**

     * 记录阅读时间

     */

    recordReadingTime(readTime) {

        this.trackUserBehavior('read_time', { readTime });

    }



}

