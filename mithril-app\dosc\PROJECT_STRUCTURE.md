# 项目结构说明

## 📁 目录结构

```
mithril-app/
├── index.html                    # 主HTML文件
├── package.json                  # 项目配置
├── FILE_REORGANIZATION.md        # 文件重组说明
├── PROJECT_STRUCTURE.md          # 本文档
├── dosc/                         # 文档目录
│   ├── DEPLOYMENT_CHECKLIST.md
│   ├── ERROR_FIXES.md
│   ├── VIDEO_DISPLAY_FIX.md
│   ├── VIDEO_COMPRESSION_OPTIMIZATION.md
│   ├── README_MARKDOWN.md
│   └── test-*.html              # 各种测试页面
└── src/                         # 源代码目录
    ├── app.js                   # 主应用入口
    ├── config.js                # 配置文件
    ├── components/              # 组件目录
    │   ├── Layout.js
    │   ├── PostSection.js
    │   ├── OptimizedMemosList.js
    │   ├── MemosList.js
    │   ├── MarkdownEditor.js
    │   └── ...
    ├── models/                  # 数据模型
    │   ├── Memos.js
    │   ├── Auth.js
    │   └── AppState.js
    ├── services/                # 服务层
    │   ├── ApiService.js
    │   ├── MemosService.js
    │   └── AuthService.js
    ├── styles/                  # 样式文件
    │   ├── main.css            # 主样式文件
    │   ├── markdown.css        # Markdown样式
    │   ├── optimized.css       # 性能优化样式
    │   └── video-compression.css
    ├── utils/                   # 工具函数
    │   ├── startup.js          # 启动工具
    │   ├── verification.js     # 验证工具
    │   ├── markdown.js         # Markdown处理
    │   ├── performance.js      # 性能监控
    │   ├── renderOptimizer.js  # 渲染优化
    │   ├── videoCompressor.js  # 视频压缩
    │   └── format.js
    └── views/                   # 视图组件
        └── ...
```

## 📋 文件分类说明

### 🏠 根目录文件
- **index.html**: 主HTML文件，应用入口
- **package.json**: 项目配置和依赖管理
- **FILE_REORGANIZATION.md**: 文件重组过程说明
- **PROJECT_STRUCTURE.md**: 项目结构文档（本文件）

### 📚 文档目录 (dosc/)
存放所有项目文档和测试页面：
- 部署检查清单
- 错误修复说明
- 功能优化文档
- 测试页面

### 💻 源代码目录 (src/)

#### 🎨 样式文件 (src/styles/)
- **main.css**: 主样式文件，包含设计系统、全局样式、基础组件
- **markdown.css**: Markdown渲染专用样式
- **optimized.css**: 性能优化相关样式
- **video-compression.css**: 视频压缩功能样式

#### 🔧 工具函数 (src/utils/)
- **startup.js**: 应用启动和初始化
- **verification.js**: 功能验证和测试
- **markdown.js**: Markdown解析和渲染
- **performance.js**: 性能监控工具
- **renderOptimizer.js**: 渲染性能优化
- **videoCompressor.js**: 视频压缩功能
- **format.js**: 格式化工具

#### 🧩 组件目录 (src/components/)
- **Layout.js**: 主布局组件
- **PostSection.js**: 发布区域组件
- **OptimizedMemosList.js**: 优化版备忘录列表
- **MemosList.js**: 标准备忘录列表
- **MarkdownEditor.js**: Markdown编辑器

#### 📊 数据模型 (src/models/)
- **Memos.js**: 备忘录数据管理
- **Auth.js**: 用户认证管理
- **AppState.js**: 应用状态管理

#### 🌐 服务层 (src/services/)
- **ApiService.js**: 基础API服务
- **MemosService.js**: 备忘录相关API
- **AuthService.js**: 认证相关API

## 🎯 设计原则

### 1. 模块化设计
- 每个功能模块独立
- 清晰的依赖关系
- 便于维护和扩展

### 2. 分层架构
```
视图层 (Components) 
    ↓
模型层 (Models)
    ↓
服务层 (Services)
    ↓
工具层 (Utils)
```

### 3. 样式组织
- 主样式文件包含设计系统
- 功能样式按模块分离
- 响应式设计统一管理

### 4. 文档管理
- 所有文档集中在dosc目录
- 测试页面与文档一起管理
- 便于查找和维护

## 🚀 开发指南

### 添加新功能
1. **组件**: 在 `src/components/` 创建新组件
2. **样式**: 在 `src/styles/` 添加对应样式
3. **工具**: 在 `src/utils/` 添加工具函数
4. **测试**: 在 `dosc/` 创建测试页面
5. **文档**: 更新相关文档

### 样式开发
1. **全局样式**: 修改 `src/styles/main.css`
2. **功能样式**: 创建独立的CSS文件
3. **变量使用**: 使用CSS变量保持一致性
4. **响应式**: 在main.css中统一管理

### 工具开发
1. **通用工具**: 放在 `src/utils/`
2. **模块导出**: 使用ES6模块语法
3. **文档注释**: 添加详细的JSDoc注释
4. **测试验证**: 在verification.js中添加测试

## 📈 优势总结

### 1. 结构清晰
- 文件分类明确
- 功能模块独立
- 便于团队协作

### 2. 易于维护
- 相关文件集中管理
- 依赖关系清晰
- 修改影响范围可控

### 3. 扩展性强
- 模块化设计
- 标准化的目录结构
- 便于添加新功能

### 4. 开发效率
- 文件查找快速
- 代码复用性高
- 调试和测试方便

---

**文档版本**: v1.0  
**最后更新**: 2025年1月9日  
**维护者**: 开发团队