<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时间记录模块测试</title>
    <link rel="stylesheet" href="src/modules/timeRecord/styles/timeRecord.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #f5f7fa;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-header h1 {
            color: #1f2937;
            margin-bottom: 10px;
        }
        
        .test-header p {
            color: #6b7280;
            margin: 0;
        }
    </style>
</head>
<body>
    <div class="test-header">
        <h1>时间记录模块测试</h1>
        <p>测试修复后的时间记录API功能</p>
    </div>
    
    <div id="app"></div>

    <script src="https://unpkg.com/mithril@2.2.2/mithril.min.js"></script>
    <script type="module">
        // 导入时间记录组件和API测试工具
        import { SimpleTimeRecord } from './src/modules/timeRecord/TimeRecord.js';
        import { apiTester } from './src/utils/ApiTester.js';
        
        // 创建测试应用
        const TestApp = {
            oninit() {
                this.testResults = null;
                this.isRunningTest = false;
            },
            
            async runApiTest() {
                this.isRunningTest = true;
                m.redraw();
                
                try {
                    this.testResults = await apiTester.runFullTest();
                } catch (error) {
                    console.error('测试运行失败:', error);
                    this.testResults = { error: error.message };
                } finally {
                    this.isRunningTest = false;
                    m.redraw();
                }
            },
            
            view() {
                return m('div', [
                    // API测试区域
                    m('div', {
                        style: {
                            background: 'white',
                            padding: '20px',
                            borderRadius: '12px',
                            marginBottom: '20px',
                            boxShadow: '0 4px 16px rgba(0, 0, 0, 0.1)'
                        }
                    }, [
                        m('h3', { style: { margin: '0 0 16px 0' } }, 'API测试'),
                        m('button', {
                            onclick: () => this.runApiTest(),
                            disabled: this.isRunningTest,
                            style: {
                                background: this.isRunningTest ? '#d1d5db' : '#667eea',
                                color: 'white',
                                border: 'none',
                                padding: '10px 20px',
                                borderRadius: '6px',
                                cursor: this.isRunningTest ? 'not-allowed' : 'pointer',
                                marginRight: '10px'
                            }
                        }, this.isRunningTest ? '测试中...' : '运行API测试'),
                        
                        m('button', {
                            onclick: () => {
                                console.clear();
                                console.log('控制台已清空');
                            },
                            style: {
                                background: '#6b7280',
                                color: 'white',
                                border: 'none',
                                padding: '10px 20px',
                                borderRadius: '6px',
                                cursor: 'pointer'
                            }
                        }, '清空控制台'),
                        
                        this.testResults && m('div', {
                            style: {
                                marginTop: '16px',
                                padding: '12px',
                                background: '#f8fafc',
                                borderRadius: '6px',
                                fontSize: '14px'
                            }
                        }, [
                            m('p', { style: { margin: '0 0 8px 0', fontWeight: 'bold' } }, '测试结果:'),
                            this.testResults.error ? 
                                m('p', { style: { color: '#dc2626', margin: 0 } }, `错误: ${this.testResults.error}`) :
                                Object.entries(this.testResults).map(([key, result]) => 
                                    m('p', { 
                                        style: { 
                                            margin: '4px 0',
                                            color: result.success ? '#059669' : '#dc2626'
                                        } 
                                    }, `${key}: ${result.success ? '✅ 通过' : '❌ 失败'}`)
                                )
                        ])
                    ]),
                    
                    // 时间记录组件
                    m(SimpleTimeRecord)
                ]);
            }
        };
        
        // 挂载应用
        m.mount(document.getElementById('app'), TestApp);
        
        console.log('时间记录模块测试页面已加载');
        console.log('💡 提示: 点击"运行API测试"按钮来测试API连接');
    </script>
</body>
</html>