/**
 * 时间记录页面组件
 * 主要的UI组件，处理用户交互
 * 需求: 4.1, 4.2 - 实现TimeRecordPage组件的基本框架和状态管理
 */

// 使用全局的 Mithril 实例
const m = (typeof window !== 'undefined' && window.m) || global.m;

// 导入依赖模块
import { TimeRecord } from '../models/TimeRecord.js';
import { formatDuration } from '../../../utils/format.js';
import { TimeRecordService } from '../services/TimeRecordService.js';
import { notify } from '../utils/NotificationSystem.js';
import { 
    ValidationFeedback, 
    InlineValidationFeedback, 
    CharacterCounter, 
    ValidationStatus,
    LiveValidationTip 
} from './ValidationFeedback.js';

export const TimeRecordPage = {
    /**
     * 组件初始化生命周期方法 - 带性能监控
     * 需求: 4.1 - 设置组件生命周期方法（oninit, view, onremove）
     * 需求: 4.2 - 配置组件状态管理和数据绑定
     */
    oninit(vnode) {
        console.log('TimeRecordPage: 初始化组件');
        console.log('window.appState:', window.appState);
        console.log('window.appState.startTimer:', window.appState && window.appState.startTimer);
        
        // 测试全局状态
        if (window.appState) {
            console.log('全局状态已初始化');
            console.log('timerRunning初始值:', window.appState.timerRunning);
        } else {
            console.error('全局状态未初始化！');
        }
        
        // 初始化服务和模型实例
        const service = new TimeRecordService();
        const timeRecord = new TimeRecord(service);
        
        // 设置组件状态
        vnode.state.timeRecord = timeRecord;
        vnode.state.loading = false;
        vnode.state.error = null;
        vnode.state.successMessage = null;
        
        // 用户反馈状态管理
        vnode.state.feedback = {
            loadingNotificationId: null,    // 加载通知ID
            lastOperationId: null,          // 最后操作的通知ID
            operationInProgress: false,     // 是否有操作正在进行
            networkStatus: 'online'         // 网络状态
        };
        
        // 组件状态管理
        vnode.state.ui = {
            showSaveButton: false,      // 是否显示保存按钮
            inputContent: '',           // 用户输入的备注内容
            isSubmitting: false,        // 是否正在提交数据
            showLoadMore: false,        // 是否显示加载更多按钮
            characterCount: 0,          // 字符计数
            nearLimit: false            // 是否接近字符限制
        };
        
        // 验证状态管理
        vnode.state.validation = {
            inputValidation: null,      // 当前输入验证结果
            showValidationTips: true,   // 是否显示验证提示
            lastValidationTime: 0       // 最后验证时间
        };
        
        // 初始化时加载历史记录
        this.loadInitialRecords(vnode);
        
        // 初始化用户反馈系统
        this.initUserFeedback(vnode);
    },
    
    /**
     * 初始化用户反馈系统
     * 需求: 6.4, 6.5 - 添加操作成功的提示消息，实现数据保存状态的视觉反馈，添加加载状态指示器
     */
    initUserFeedback(vnode) {
        // 简化的用户反馈初始化
        // 监听浏览器的网络状态变化
        if (typeof window !== 'undefined') {
            window.addEventListener('online', () => {
                vnode.state.feedback.networkStatus = 'online';
                notify.success('网络连接已恢复', { duration: 3000 });
                if (m && m.redraw) m.redraw();
            });
            
            window.addEventListener('offline', () => {
                vnode.state.feedback.networkStatus = 'offline';
                notify.warning('网络连接已断开，部分功能可能受限', { 
                    persistent: true,
                    actions: [{
                        label: '重试连接',
                        onClick: () => {
                            this.checkNetworkAndRetry(vnode);
                        }
                    }]
                });
                if (m && m.redraw) m.redraw();
            });
        }
    },
    
    /**
     * 检查网络并重试操作
     */
    async checkNetworkAndRetry(vnode) {
        const loadingId = this.showLoadingFeedback(vnode, '正在检查网络连接...');
        
        try {
            // 等待一段时间让网络恢复
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            // 尝试重新加载数据
            await this.loadInitialRecords(vnode);
            
            this.hideLoadingFeedback(vnode);
            this.showSuccessFeedback(vnode, '网络连接正常，数据已更新');
        } catch (error) {
            this.hideLoadingFeedback(vnode);
            this.showErrorFeedback(vnode, '网络连接仍有问题，请稍后重试');
        }
    },
    
    /**
     * 显示操作成功反馈
     * 需求: 6.4 - 添加操作成功的提示消息
     */
    showSuccessFeedback(vnode, message, options = {}) {
        // 清除之前的操作通知
        if (vnode.state.feedback.lastOperationId) {
            notify.remove(vnode.state.feedback.lastOperationId);
        }
        
        const notificationId = notify.success(message, {
            duration: options.duration || 4000,
            title: options.title || '操作成功',
            ...options
        });
        
        vnode.state.feedback.lastOperationId = notificationId;
        return notificationId;
    },
    
    /**
     * 显示操作错误反馈
     * 需求: 6.5 - 用户输入无效数据时系统应显示验证错误信息
     */
    showErrorFeedback(vnode, message, options = {}) {
        // 清除之前的操作通知
        if (vnode.state.feedback.lastOperationId) {
            notify.remove(vnode.state.feedback.lastOperationId);
        }
        
        const notificationId = notify.error(message, {
            duration: options.duration || 8000,
            title: options.title || '操作失败',
            actions: options.retryable ? [{
                label: '重试',
                onClick: options.onRetry
            }] : [],
            ...options
        });
        
        vnode.state.feedback.lastOperationId = notificationId;
        return notificationId;
    },
    
    /**
     * 显示加载状态反馈
     * 需求: 6.4 - 添加加载状态指示器
     */
    showLoadingFeedback(vnode, message, options = {}) {
        const notificationId = notify.loading(message, {
            title: options.title || '正在处理',
            ...options
        });
        
        vnode.state.feedback.loadingNotificationId = notificationId;
        vnode.state.feedback.operationInProgress = true;
        
        return notificationId;
    },
    
    /**
     * 隐藏加载状态反馈
     */
    hideLoadingFeedback(vnode) {
        if (vnode.state.feedback.loadingNotificationId) {
            notify.remove(vnode.state.feedback.loadingNotificationId);
            vnode.state.feedback.loadingNotificationId = null;
        }
        vnode.state.feedback.operationInProgress = false;
    },
    
    /**
     * 加载初始历史记录
     * 需求: 3.1 - 用户访问时间记录页面时系统应显示历史记录列表
     */
    async loadInitialRecords(vnode) {
        // 显示加载反馈
        const loadingId = this.showLoadingFeedback(vnode, '正在加载历史记录...', {
            title: '数据加载中'
        });
        
        try {
            vnode.state.loading = true;
            vnode.state.error = null;
            if (m && m.redraw) m.redraw();
            
            await vnode.state.timeRecord.loadRecords(1);
            
            // 更新UI状态
            vnode.state.ui.showLoadMore = vnode.state.timeRecord.hasMore;
            
            // 显示成功反馈
            this.hideLoadingFeedback(vnode);
            
            if (vnode.state.timeRecord.records.length > 0) {
                this.showSuccessFeedback(vnode, `成功加载 ${vnode.state.timeRecord.records.length} 条历史记录`, {
                    duration: 2000
                });
            }
            
        } catch (error) {
            console.error('加载历史记录失败:', error);
            this.hideLoadingFeedback(vnode);
            
            // 显示错误反馈
            this.showErrorFeedback(vnode, '加载历史记录失败，请检查网络连接', {
                retryable: true,
                onRetry: () => this.loadInitialRecords(vnode)
            });
            
            vnode.state.error = '加载历史记录失败: ' + error.message;
        } finally {
            vnode.state.loading = false;
            if (m && m.redraw) m.redraw();
        }
    },
    
    /**
     * 开始计时处理函数
     * 需求: 1.1 - 用户点击"开始计时"按钮时系统应开始计时并显示实时时长
     * 需求: 4.3 - 实现按钮状态管理和禁用逻辑，添加按钮点击的视觉反馈效果
     */
    handleStartTimer(vnode) {
        try {
            console.log('开始计时按钮被点击');
            console.log('当前timerRunning状态:', window.appState.timerRunning);
            console.log('网络状态:', vnode.state.feedback.networkStatus);
            
            // 检查是否已经在运行
            if (window.appState.timerRunning) {
                this.showErrorFeedback(vnode, '计时器已在运行中，无需重复开始', {
                    duration: 3000,
                    title: '操作提示'
                });
                return;
            }

            // 检查网络状态
            if (vnode.state.feedback.networkStatus === 'offline') {
                this.showErrorFeedback(vnode, '网络连接不可用，计时数据可能无法保存', {
                    title: '网络警告',
                    duration: 5000
                });
            }

            // 清除之前的错误和成功消息
            vnode.state.error = null;
            vnode.state.successMessage = null;

            const success = vnode.state.timeRecord.startTimer();
            console.log('startTimer返回结果:', success);
            if (success) {
                // 显示成功反馈
                this.showSuccessFeedback(vnode, '计时已开始，开始记录您的工作时间', {
                    title: '计时开始',
                    duration: 3000,
                    actions: [{
                        label: '查看计时器',
                        onClick: () => {
                            // 滚动到计时器区域
                            const timerSection = document.querySelector('.timer-section');
                            if (timerSection) {
                                timerSection.scrollIntoView({ behavior: 'smooth', block: 'center' });
                            }
                        }
                    }]
                });
                
                vnode.state.ui.showSaveButton = false; // 隐藏保存按钮
                vnode.state.successMessage = '✓ 计时已开始，开始记录您的工作时间';
                
                // 清除成功消息
                setTimeout(() => {
                    vnode.state.successMessage = null;
                    if (m && m.redraw) m.redraw();
                }, 5000);
            } else {
                this.showErrorFeedback(vnode, '开始计时失败，请重试', {
                    retryable: true,
                    onRetry: () => this.handleStartTimer(vnode)
                });
                vnode.state.error = '开始计时失败，请重试';
            }
        } catch (error) {
            console.error('开始计时失败:', error);
            this.showErrorFeedback(vnode, '开始计时时发生错误: ' + error.message, {
                retryable: true,
                onRetry: () => this.handleStartTimer(vnode)
            });
            vnode.state.error = '开始计时失败: ' + error.message;
        }
        if (m && m.redraw) m.redraw();
    },
    
    /**
     * 结束计时处理函数
     * 需求: 1.2 - 用户点击"结束计时"按钮时系统应停止计时并显示最终时长
     * 需求: 1.4 - 计时结束后系统应显示内容输入框供用户添加备注
     * 需求: 4.3 - 实现按钮状态管理和禁用逻辑，添加按钮点击的视觉反馈效果
     */
    handleStopTimer(vnode) {
        try {
            // 检查是否正在运行
            if (!window.appState.timerRunning) {
                this.showErrorFeedback(vnode, '计时器未在运行，无法结束计时', {
                    duration: 3000,
                    title: '操作提示'
                });
                return;
            }

            // 检查计时时长是否足够
            if (window.appState.accumulatedDuration < 1) {
                this.showErrorFeedback(vnode, '计时时长太短，请至少计时1秒钟', {
                    duration: 3000,
                    title: '时长不足'
                });
                return;
            }

            // 清除之前的错误和成功消息
            vnode.state.error = null;
            vnode.state.successMessage = null;

            const success = vnode.state.timeRecord.stopTimer();
            if (success) {
                const durationText = window.appState.accumulatedDuration > 0 ? formatDuration(window.appState.accumulatedDuration) : '0秒';
                
                // 显示成功反馈
                this.showSuccessFeedback(vnode, `计时已结束，共计时 ${durationText}`, {
                    title: '计时完成',
                    duration: 5000,
                    actions: [{
                        label: '立即保存',
                        onClick: () => {
                            // 滚动到输入区域
                            const inputSection = document.querySelector('.input-section');
                            if (inputSection) {
                                inputSection.scrollIntoView({ behavior: 'smooth', block: 'center' });
                            }
                            // 聚焦到输入框
                            setTimeout(() => {
                                const textarea = document.querySelector('.content-input');
                                if (textarea) {
                                    textarea.focus();
                                }
                            }, 500);
                        }
                    }]
                });
                
                vnode.state.successMessage = `✓ 计时已结束，共计时 ${durationText}，请添加工作备注`;
                vnode.state.ui.showSaveButton = true; // 显示保存按钮
                vnode.state.ui.inputContent = ''; // 清空输入内容
                
                // 清除成功消息
                setTimeout(() => {
                    vnode.state.successMessage = null;
                    if (m && m.redraw) m.redraw();
                }, 5000);
            } else {
                this.showErrorFeedback(vnode, '结束计时失败，请重试', {
                    retryable: true,
                    onRetry: () => this.handleStopTimer(vnode)
                });
                vnode.state.error = '结束计时失败，请重试';
            }
        } catch (error) {
            console.error('结束计时失败:', error);
            this.showErrorFeedback(vnode, '结束计时时发生错误: ' + error.message, {
                retryable: true,
                onRetry: () => this.handleStopTimer(vnode)
            });
            vnode.state.error = '结束计时失败: ' + error.message;
        }
        if (m && m.redraw) m.redraw();
    },
    
    /**
     * 重置计时器处理函数
     * 需求: 4.3 - 实现按钮状态管理和禁用逻辑，添加按钮点击的视觉反馈效果
     */
    handleResetTimer(vnode) {
        try {
            // 如果正在运行，显示确认提示
            if (window.appState.timerRunning) {
                const currentDuration = formatDuration(window.appState.accumulatedDuration);
                
                // 显示确认通知
                notify.warning(`确定要重置正在运行的计时器吗？当前已计时 ${currentDuration}`, {
                    title: '确认重置',
                    persistent: true,
                    actions: [{
                        label: '确认重置',
                        primary: true,
                        onClick: () => {
                            this.performReset(vnode);
                        }
                    }, {
                        label: '取消',
                        onClick: () => {
                            // 取消操作，不做任何事
                        }
                    }]
                });
                return;
            }

            // 直接重置
            this.performReset(vnode);
        } catch (error) {
            console.error('重置计时器失败:', error);
            this.showErrorFeedback(vnode, '重置计时器时发生错误: ' + error.message, {
                retryable: true,
                onRetry: () => this.handleResetTimer(vnode)
            });
            vnode.state.error = '重置计时器失败: ' + error.message;
        }
        if (m && m.redraw) m.redraw();
    },
    
    /**
     * 执行计时器重置操作
     */
    performReset(vnode) {
        try {
            // 清除之前的错误和成功消息
            vnode.state.error = null;
            vnode.state.successMessage = null;

            const success = vnode.state.timeRecord.resetTimer();
            if (success) {
                // 显示成功反馈
                this.showSuccessFeedback(vnode, '计时器已重置，可以开始新的时间记录', {
                    title: '重置成功',
                    duration: 3000,
                    actions: [{
                        label: '开始计时',
                        onClick: () => {
                            this.handleStartTimer(vnode);
                        }
                    }]
                });
                
                vnode.state.successMessage = '✓ 计时器已重置，可以开始新的时间记录';
                vnode.state.ui.showSaveButton = false;
                vnode.state.ui.inputContent = '';
                vnode.state.ui.isSubmitting = false;
                
                // 清除成功消息
                setTimeout(() => {
                    vnode.state.successMessage = null;
                    if (m && m.redraw) m.redraw();
                }, 3000);
            } else {
                this.showErrorFeedback(vnode, '重置计时器失败，请重试', {
                    retryable: true,
                    onRetry: () => this.performReset(vnode)
                });
                vnode.state.error = '重置计时器失败，请重试';
            }
        } catch (error) {
            console.error('执行重置失败:', error);
            this.showErrorFeedback(vnode, '执行重置时发生错误: ' + error.message);
            vnode.state.error = '重置计时器失败: ' + error.message;
        }
        if (m && m.redraw) m.redraw();
    },
    
    /**
     * 保存记录处理函数
     * 需求: 2.1 - 用户输入备注并点击保存时系统应将记录保存到vika表
     * 需求: 2.3 - 保存成功后系统应重置计时器并显示成功提示
     */
    async handleSaveRecord(vnode) {
        // 显示保存中的加载反馈
        const loadingId = this.showLoadingFeedback(vnode, '正在保存时间记录...', {
            title: '数据保存中'
        });
        
        try {
            vnode.state.ui.isSubmitting = true;
            vnode.state.error = null;
            m.redraw();
            
            // 检查网络状态
            if (vnode.state.feedback.networkStatus === 'offline') {
                this.hideLoadingFeedback(vnode);
                this.showErrorFeedback(vnode, '网络连接不可用，无法保存记录', {
                    title: '网络错误',
                    retryable: true,
                    onRetry: () => this.handleSaveRecord(vnode)
                });
                return;
            }
            
            await vnode.state.timeRecord.saveRecord(vnode.state.ui.inputContent);
            
            // 隐藏加载反馈
            this.hideLoadingFeedback(vnode);
            
            // 显示保存成功反馈
            const durationText = vnode.state.timeRecord.formatDuration(
                vnode.state.timeRecord.records[0]?.duration || 0, 'TEXT'
            );
            
            this.showSuccessFeedback(vnode, `时间记录保存成功！本次工作时长: ${durationText}`, {
                title: '保存成功',
                duration: 5000,
                actions: [{
                    label: '查看记录',
                    onClick: () => {
                        // 滚动到历史记录区域
                        const recordsSection = document.querySelector('.records-section');
                        if (recordsSection) {
                            recordsSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
                        }
                    }
                }, {
                    label: '开始新计时',
                    onClick: () => {
                        this.handleStartTimer(vnode);
                    }
                }]
            });
            
            // 保存成功
            vnode.state.successMessage = '记录保存成功';
            vnode.state.ui.showSaveButton = false;
            vnode.state.ui.inputContent = '';
            vnode.state.ui.showLoadMore = vnode.state.timeRecord.hasMore;
            
            // 清除成功消息
            setTimeout(() => {
                vnode.state.successMessage = null;
                if (m && m.redraw) m.redraw();
            }, 5000);
            
        } catch (error) {
            console.error('保存记录失败:', error);
            
            // 隐藏加载反馈
            this.hideLoadingFeedback(vnode);
            
            // 显示错误反馈
            let errorMessage = '保存记录失败';
            let retryable = true;
            
            if (error.message.includes('验证失败')) {
                errorMessage = error.message;
                retryable = false;
            } else if (error.message.includes('网络')) {
                errorMessage = '网络连接问题，请检查网络后重试';
            } else if (error.message.includes('权限')) {
                errorMessage = '没有保存权限，请重新登录';
                retryable = false;
            } else {
                errorMessage = '保存失败: ' + error.message;
            }
            
            this.showErrorFeedback(vnode, errorMessage, {
                retryable,
                onRetry: retryable ? () => this.handleSaveRecord(vnode) : undefined,
                actions: retryable ? [] : [{
                    label: '保留数据',
                    onClick: () => {
                        // 数据保留在输入框中，用户可以稍后重试
                        notify.info('您的输入内容已保留，可稍后重试保存');
                    }
                }]
            });
            
            vnode.state.error = '保存记录失败: ' + error.message;
        } finally {
            vnode.state.ui.isSubmitting = false;
            if (m && m.redraw) m.redraw();
        }
    },
    
    /**
     * 加载更多记录处理函数
     * 需求: 3.3 - 记录列表超过一页时系统应提供分页或加载更多功能
     */
    async handleLoadMore(vnode) {
        // 显示加载更多的反馈
        const loadingId = this.showLoadingFeedback(vnode, '正在加载更多记录...', {
            title: '加载中'
        });
        
        try {
            vnode.state.loading = true;
            vnode.state.error = null;
            m.redraw();
            
            const beforeCount = vnode.state.timeRecord.records.length;
            await vnode.state.timeRecord.loadMoreRecords();
            const afterCount = vnode.state.timeRecord.records.length;
            const newRecordsCount = afterCount - beforeCount;
            
            vnode.state.ui.showLoadMore = vnode.state.timeRecord.hasMore;
            
            // 隐藏加载反馈
            this.hideLoadingFeedback(vnode);
            
            // 显示成功反馈
            if (newRecordsCount > 0) {
                this.showSuccessFeedback(vnode, `成功加载 ${newRecordsCount} 条新记录`, {
                    duration: 2000
                });
            } else {
                this.showSuccessFeedback(vnode, '已加载所有记录', {
                    duration: 2000
                });
            }
            
        } catch (error) {
            console.error('加载更多记录失败:', error);
            
            // 隐藏加载反馈
            this.hideLoadingFeedback(vnode);
            
            // 显示错误反馈
            this.showErrorFeedback(vnode, '加载更多记录失败，请检查网络连接', {
                retryable: true,
                onRetry: () => this.handleLoadMore(vnode)
            });
            
            vnode.state.error = '加载更多记录失败: ' + error.message;
        } finally {
            vnode.state.loading = false;
            if (m && m.redraw) m.redraw();
        }
    },
    
    /**
     * 处理输入内容变化
     * 需求: 1.4 - 计时结束后系统应显示内容输入框供用户添加备注
     * 需求: 2.4 - 实现输入验证和字符限制
     * 需求: 4.5 - 创建备注内容输入框，实现输入验证和字符限制
     * 任务: 7.3 - 创建验证错误的用户提示
     */
    handleInputChange(vnode, value) {
        vnode.state.ui.inputContent = value;
        
        // 实时验证输入
        const validation = vnode.state.timeRecord.validateInput(value);
        vnode.state.validation.inputValidation = validation;
        vnode.state.validation.lastValidationTime = Date.now();
        
        // 清除之前的全局错误（如果是验证相关的）
        if (vnode.state.error && vnode.state.error.includes('验证')) {
            vnode.state.error = null;
        }
        
        // 更新字符计数
        vnode.state.ui.characterCount = value.length;
        
        // 检查是否接近字符限制
        const maxLength = 1000; // 从配置中获取
        if (value.length > maxLength * 0.9) {
            vnode.state.ui.nearLimit = true;
            
            // 接近限制时的提示（只显示一次）
            if (value.length > maxLength * 0.95 && !this.nearLimitWarningShown) {
                this.nearLimitWarningShown = true;
                notify.warning(`输入内容接近字符限制 (${value.length}/${maxLength})`, {
                    duration: 3000,
                    title: '字符限制提醒'
                });
            }
        } else {
            vnode.state.ui.nearLimit = false;
            this.nearLimitWarningShown = false;
        }
        
        // 输入内容变化时的积极反馈
        if (value.trim().length > 10 && validation.isValid && !this.inputProgressShown) {
            this.inputProgressShown = true;
            
            // 显示鼓励性的反馈
            if (validation.details && validation.details.hasWarnings) {
                notify.info('输入内容不错，注意一下提醒信息', {
                    duration: 2000,
                    title: '输入提示'
                });
            } else {
                notify.success('输入内容看起来很好！', {
                    duration: 2000,
                    title: '输入提示'
                });
            }
        }
        
        // 重置输入进度标记
        if (value.trim().length <= 10) {
            this.inputProgressShown = false;
        }
        
        // 如果有严重错误，显示即时通知
        if (!validation.isValid && value.length > 0) {
            clearTimeout(this.validationTimeout);
            this.validationTimeout = setTimeout(() => {
                // 只对严重错误显示通知，避免过于频繁
                if (validation.error && !validation.error.includes('至少需要')) {
                    this.showErrorFeedback(vnode, validation.error, {
                        title: '输入验证',
                        duration: 4000
                    });
                }
            }, 1500); // 延迟1.5秒显示，避免用户还在输入时就显示错误
        } else {
            clearTimeout(this.validationTimeout);
        }
    },
    
    /**
     * 清除消息
     */
    clearMessages(vnode) {
        vnode.state.error = null;
        vnode.state.successMessage = null;
        if (m && m.redraw) m.redraw();
    },
    
    /**
     * 按日期分组记录
     * 需求: 3.2 - 显示历史记录时每条记录应包含完整信息
     */
    groupRecordsByDate(records) {
        const groups = {};
        
        records.forEach(record => {
            const date = new Date(record.createdAt || record.startTime);
            const dateKey = date.toLocaleDateString('zh-CN', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                weekday: 'long'
            });
            
            if (!groups[dateKey]) {
                groups[dateKey] = {
                    date: dateKey,
                    records: []
                };
            }
            
            groups[dateKey].records.push(record);
        });
        
        // 按日期排序（最新的在前）
        return Object.values(groups).sort((a, b) => {
            const dateA = new Date(a.records[0].createdAt || a.records[0].startTime);
            const dateB = new Date(b.records[0].createdAt || b.records[0].startTime);
            return dateB - dateA;
        });
    },
    
    /**
     * 复制记录内容到剪贴板
     */
    copyRecordContent(content) {
        if (typeof navigator !== 'undefined' && navigator.clipboard) {
            navigator.clipboard.writeText(content || '').then(() => {
                console.log('内容已复制到剪贴板');
                // 可以显示一个临时提示
            }).catch(err => {
                console.error('复制失败:', err);
            });
        }
    },
    
    /**
     * 显示记录详情（可以扩展为模态框）
     */
    showRecordDetails(record) {
        console.log('显示记录详情:', record);
        // 在实际应用中，这里可以打开一个详情模态框
        // 或者导航到详情页面
    },
    
    /**
     * 组件视图渲染方法 - 带性能优化
     * 需求: 4.1 - 渲染UI界面
     * 需求: 4.2 - 配置组件状态管理和数据绑定
     */
    view(vnode) {
        const { timeRecord, loading, error, successMessage, ui } = vnode.state;
        
        // 直接渲染内容，不使用缓存优化
        return this.renderContent(vnode);
    },

    /**
     * 组件销毁生命周期方法 - 清理资源
     * 需求: 4.1 - 设置组件生命周期方法（oninit, view, onremove）
     */
    onremove(vnode) {
        console.log('TimeRecordPage: 清理组件资源');
        
        // 清理TimeRecord模型资源
        if (vnode.state.timeRecord) {
            vnode.state.timeRecord.destroy();
        }
        
        // 清理验证定时器
        if (this.validationTimeout) {
            clearTimeout(this.validationTimeout);
        }
        
        // 清理相关资源（简化版本）
        
        // 重置标记
        this.nearLimitWarningShown = false;
        this.inputProgressShown = false;
    },

    /**
     * 渲染页面内容
     */
    renderContent(vnode) {
        const { timeRecord, loading, error, successMessage, ui } = vnode.state;
        
        return m('div.time-record-page', [
            
            // 错误和成功消息显示
            error && m('div.message.error', [
                m('span.message-text', error),
                m('button.message-close', {
                    onclick: () => this.clearMessages(vnode)
                }, '×')
            ]),
            
            successMessage && m('div.message.success', [
                m('span.message-text', successMessage),
                m('button.message-close', {
                    onclick: () => this.clearMessages(vnode)
                }, '×')
            ]),
            
            // 计时器显示区域
            // 需求: 1.3 - 计时进行中时系统应每秒更新显示的时长
            // 需求: 4.1 - 用户查看计时器时应显示清晰的时间格式
            // 需求: 4.2 - 计时进行中时界面应有明显的视觉反馈
            m('div.timer-section', {
                class: window.appState.timerRunning ? 'timer-running' : 'timer-stopped'
            }, [
                // 计时器状态指示器
                m('div.timer-status', [
                    m('div.status-indicator', {
                        class: window.appState.timerRunning ? 'running' : 'stopped'
                    }),
                    m('span.status-text', 
                        window.appState.timerRunning ? '计时中...' : '已停止'
                    )
                ]),
                
                // 主要时间显示
                m('div.timer-display', {
                    class: window.appState.timerRunning ? 'active' : 'inactive'
                }, [
                    m('span.time-text', formatDuration(window.appState.accumulatedDuration)),
                    window.appState.timerRunning && 
                    m('span.pulse-dot', '●')
                ]),
                
                // 计时器信息
                window.appState.startTime && 
                m('div.timer-info', [
                    window.appState.startTime && 
                    m('div.timer-start', [
                        m('span.info-label', '开始时间: '),
                        m('span.info-value', new Date(window.appState.startTime).toLocaleTimeString('zh-CN', { hour12: false }))
                    ])
                ]),
                
                // 控制按钮
                m('div.timer-controls', [
                    m('button.btn', {
                        class: window.appState.timerRunning ? 'btn-stop' : 'btn-start',
                        onclick: () => {
                            console.log('按钮被点击！');
                            console.log('当前timerRunning:', window.appState.timerRunning);
                            console.log('vnode.state.timeRecord:', vnode.state.timeRecord);
                            if (window.appState.timerRunning) {
                                this.handleStopTimer(vnode);
                            } else {
                                this.handleStartTimer(vnode);
                            }
                        },
                        title: window.appState.timerRunning ? '结束计时' : '开始计时'
                    }, [
                        m('span.btn-icon', window.appState.timerRunning ? '⏸' : '▶'),
                        m('span.btn-text', window.appState.timerRunning ? '结束计时' : '开始计时')
                    ]),
                    m('button.btn.btn-reset', {
                        onclick: () => this.handleResetTimer(vnode),
                        title: '重置计时器'
                    }, [
                        m('span.btn-icon', '⏹'),
                        m('span.btn-text', '重置')
                    ])
                ])
            ]),
            
            // 备注输入区域
            // 需求: 1.4 - 计时结束后系统应显示内容输入框供用户添加备注
            // 需求: 2.4 - 实现输入验证和字符限制
            // 需求: 4.5 - 创建备注内容输入框，实现输入验证和字符限制
            // 任务: 7.3 - 创建验证错误的用户提示
            ui.showSaveButton && m('div.input-section', {
                class: [
                    vnode.state.validation.inputValidation?.error ? 'has-validation-error' : '',
                    vnode.state.validation.inputValidation?.details?.hasWarnings ? 'has-validation-warning' : '',
                    vnode.state.validation.inputValidation?.isValid && ui.inputContent ? 'has-validation-success' : ''
                ].filter(Boolean).join(' ')
            }, [
                m('div.input-header', [
                    m('h3.input-title', [
                        '添加工作备注',
                        // 验证状态指示器
                        m(ValidationStatus, {
                            validation: vnode.state.validation.inputValidation,
                            showSuccess: ui.inputContent && ui.inputContent.trim().length > 5
                        })
                    ]),
                    m('div.input-subtitle', [
                        m('span.input-hint', '请详细描述您刚才完成的工作内容'),
                        window.appState.accumulatedDuration > 0 && 
                        m('span.duration-badge', `用时: ${formatDuration(window.appState.accumulatedDuration)}`)
                    ])
                ]),
                
                // 验证反馈组件
                vnode.state.validation.inputValidation && 
                m(ValidationFeedback, {
                    validation: vnode.state.validation.inputValidation,
                    field: 'content',
                    showWarnings: true,
                    onDismiss: () => {
                        vnode.state.validation.showValidationTips = false;
                        if (m && m.redraw) m.redraw();
                    }
                }),
                
                // 输入框容器
                m('div.input-container', {
                    style: 'position: relative;'
                }, [
                    m('textarea.content-input', {
                        placeholder: '例如：完成了用户登录功能的前端开发，包括表单验证和错误处理...',
                        value: ui.inputContent,
                        oninput: (e) => this.handleInputChange(vnode, e.target.value),
                        disabled: ui.isSubmitting,
                        maxlength: 1000,
                        rows: 4,
                        class: [
                            vnode.state.validation.inputValidation?.error ? 'error' : '',
                            vnode.state.validation.inputValidation?.details?.hasWarnings ? 'warning' : '',
                            vnode.state.validation.inputValidation?.isValid && ui.inputContent ? 'success' : ''
                        ].filter(Boolean).join(' ')
                    }),
                    
                    // 实时验证提示
                    vnode.state.validation.showValidationTips && 
                    m(LiveValidationTip, {
                        validation: vnode.state.validation.inputValidation,
                        delay: 1500
                    }),
                    
                    // 内联验证反馈
                    m(InlineValidationFeedback, {
                        validation: vnode.state.validation.inputValidation,
                        compact: true
                    }),
                    
                    // 字符计数和验证提示
                    m('div.input-footer', [
                        m('div.input-stats', [
                            // 增强的字符计数器
                            m(CharacterCounter, {
                                current: ui.characterCount || 0,
                                max: 1000,
                                showWarning: true,
                                warningThreshold: 0.9
                            }),
                            ui.inputContent && ui.inputContent.trim().length > 0 && 
                            m('span.word-count', `约 ${Math.ceil(ui.inputContent.trim().length / 5)} 词`)
                        ])
                    ])
                ]),
                
                // 快速输入建议
                !ui.inputContent && m('div.quick-suggestions', [
                    m('span.suggestions-label', '快速输入:'),
                    m('div.suggestion-buttons', [
                        m('button.suggestion-btn', {
                            onclick: () => {
                                this.handleInputChange(vnode, '完成了开发任务');
                                if (m && m.redraw) m.redraw();
                            }
                        }, '开发任务'),
                        m('button.suggestion-btn', {
                            onclick: () => {
                                this.handleInputChange(vnode, '参加了会议讨论');
                                if (m && m.redraw) m.redraw();
                            }
                        }, '会议讨论'),
                        m('button.suggestion-btn', {
                            onclick: () => {
                                this.handleInputChange(vnode, '进行了代码审查');
                                if (m && m.redraw) m.redraw();
                            }
                        }, '代码审查'),
                        m('button.suggestion-btn', {
                            onclick: () => {
                                this.handleInputChange(vnode, '学习了新技术');
                                if (m && m.redraw) m.redraw();
                            }
                        }, '学习研究')
                    ])
                ]),
                
                // 操作按钮
                m('div.input-actions', [
                    m('button.btn.btn-primary', {
                        disabled: ui.isSubmitting || !ui.inputContent || !ui.inputContent.trim() || (error && error.includes('备注')),
                        onclick: () => this.handleSaveRecord(vnode),
                        class: ui.isSubmitting ? 'loading' : ''
                    }, [
                        ui.isSubmitting && m('span.loading-spinner'),
                        m('span.btn-text', ui.isSubmitting ? '保存中...' : '保存记录')
                    ]),
                    m('button.btn.btn-secondary', {
                        disabled: ui.isSubmitting,
                        onclick: () => {
                            ui.showSaveButton = false;
                            ui.inputContent = '';
                            ui.characterCount = 0;
                            ui.nearLimit = false;
                            vnode.state.error = null;
                            if (m && m.redraw) m.redraw();
                        }
                    }, '取消')
                ])
            ]),
            
            // 历史记录列表
            // 需求: 3.1 - 用户访问时间记录页面时系统应显示历史记录列表
            // 需求: 3.2 - 显示历史记录时每条记录应包含完整信息
            // 需求: 3.3 - 记录列表超过一页时系统应提供分页或加载更多功能
            // 需求: 3.4 - 没有记录时系统应显示友好的空状态提示
            m('div.records-section', [
                m('div.records-header', [
                    m('div.records-title-area', [
                        m('h3.records-title', '历史记录'),
                        timeRecord && timeRecord.records.length > 0 && 
                        m('span.records-count', `共 ${timeRecord.records.length} 条记录`)
                    ]),
                    
                    // 统计信息
                    timeRecord && timeRecord.records.length > 0 && 
                    m('div.records-stats', [
                        m('div.stat-item', [
                            m('span.stat-label', '总时长'),
                            m('span.stat-value', (() => {
                                const totalDuration = timeRecord.records.reduce((sum, record) => sum + (record.duration || 0), 0);
                                return timeRecord.formatDuration(totalDuration, 'TEXT');
                            })())
                        ]),
                        m('div.stat-item', [
                            m('span.stat-label', '平均时长'),
                            m('span.stat-value', (() => {
                                const totalDuration = timeRecord.records.reduce((sum, record) => sum + (record.duration || 0), 0);
                                const avgDuration = timeRecord.records.length > 0 ? Math.round(totalDuration / timeRecord.records.length) : 0;
                                return timeRecord.formatDuration(avgDuration, 'TEXT');
                            })())
                        ])
                    ])
                ]),
                
                // 加载状态 - 增强视觉反馈
                loading && m('div.loading-container', [
                    m('div.loading-spinner'),
                    m('span.loading-text', '正在加载历史记录...'),
                    m('div.loading-progress', [
                        m('div.progress-bar', {
                            style: 'animation: progress-fill 2s ease-in-out infinite'
                        })
                    ])
                ]),
                
                // 空状态
                timeRecord && timeRecord.records.length === 0 && !loading && 
                m('div.empty-state', [
                    m('div.empty-illustration', [
                        m('div.empty-icon', '⏱️'),
                        m('div.empty-decoration', [
                            m('span.decoration-dot', '•'),
                            m('span.decoration-dot', '•'),
                            m('span.decoration-dot', '•')
                        ])
                    ]),
                    m('h4.empty-title', '还没有时间记录'),
                    m('p.empty-text', '开始您的第一次时间记录，追踪工作效率'),
                    m('div.empty-actions', [
                        m('button.btn.btn-primary.btn-small', {
                            onclick: () => {
                                if (!window.appState.timerRunning) {
                                    this.handleStartTimer(vnode);
                                }
                            },
                            disabled: window.appState.timerRunning
                        }, window.appState.timerRunning ? '计时进行中...' : '开始计时')
                    ])
                ]),
                
                // 记录列表
                timeRecord && timeRecord.records.length > 0 && 
                m('div.records-list', [
                    // 按日期分组显示
                    ...this.groupRecordsByDate(timeRecord.records).map(group => 
                        m('div.record-group', { key: group.date }, [
                            m('div.group-header', [
                                m('span.group-date', group.date),
                                m('span.group-summary', `${group.records.length} 条记录，共 ${timeRecord.formatDuration(
                                    group.records.reduce((sum, record) => sum + (record.duration || 0), 0), 'TEXT'
                                )}`)
                            ]),
                            m('div.group-records', 
                                group.records.map(record => 
                                    m('div.record-item', { key: record.id }, [
                                        m('div.record-main', [
                                            m('div.record-time-info', [
                                                m('span.time-range', 
                                                    `${timeRecord.formatTimestamp(record.startTime, 'TIME_ONLY')} - ${timeRecord.formatTimestamp(record.endTime, 'TIME_ONLY')}`
                                                ),
                                                m('span.record-duration', record.durationText || timeRecord.formatDuration(record.duration, 'TEXT'))
                                            ]),
                                            m('div.record-content', [
                                                m('p.content-text', record.content || '无备注'),
                                                record.category && m('span.category-tag', record.category)
                                            ])
                                        ]),
                                        
                                        // 记录操作按钮（可选）
                                        m('div.record-actions', [
                                            m('button.action-btn', {
                                                title: '复制内容',
                                                onclick: () => this.copyRecordContent(record.content)
                                            }, '📋'),
                                            m('button.action-btn', {
                                                title: '查看详情',
                                                onclick: () => this.showRecordDetails(record)
                                            }, '👁️')
                                        ])
                                    ])
                                )
                            )
                        ])
                    )
                ]),
                
                // 加载更多按钮 - 增强状态反馈
                ui.showLoadMore && 
                m('div.load-more-section', [
                    m('button.btn.btn-outline.load-more-btn', {
                        onclick: () => this.handleLoadMore(vnode),
                        disabled: loading || vnode.state.feedback.operationInProgress,
                        class: loading ? 'loading' : ''
                    }, [
                        loading ? 
                            m('span.loading-spinner') :
                            m('span.btn-icon', '⬇️'),
                        m('span.btn-text', loading ? '加载中...' : '加载更多记录')
                    ]),
                    
                    // 网络状态指示器
                    vnode.state.feedback.networkStatus === 'offline' && 
                    m('div.network-status-warning', [
                        m('span.warning-icon', '⚠️'),
                        m('span.warning-text', '网络连接不可用')
                    ])
                ]),
                
                // 底部提示
                timeRecord && timeRecord.records.length > 0 && !ui.showLoadMore && 
                m('div.records-footer', [
                    m('span.footer-text', '已显示所有记录'),
                    m('button.footer-action', {
                        onclick: () => {
                            // 滚动到顶部
                            if (typeof window !== 'undefined') {
                                window.scrollTo({ top: 0, behavior: 'smooth' });
                            }
                        }
                    }, '回到顶部 ↑')
                ])
            ])
        ]);
    },
    
    /**
     * 组件销毁生命周期方法
     * 需求: 4.1 - 设置组件生命周期方法（oninit, view, onremove）
     */
    onremove(vnode) {
        console.log('TimeRecordPage: 清理组件资源');
        
        // 清理时间记录模型的资源
        if (vnode.state.timeRecord) {
            vnode.state.timeRecord.destroy();
        }
        
        // 清理用户反馈相关资源
        if (vnode.state.feedback) {
            // 清除所有通知
            if (vnode.state.feedback.loadingNotificationId) {
                notify.remove(vnode.state.feedback.loadingNotificationId);
            }
            if (vnode.state.feedback.lastOperationId) {
                notify.remove(vnode.state.feedback.lastOperationId);
            }
        }
        
        // 清理定时器
        if (this.validationTimeout) {
            clearTimeout(this.validationTimeout);
        }
        
        // 清理组件状态
        vnode.state.timeRecord = null;
        vnode.state.loading = false;
        vnode.state.error = null;
        vnode.state.successMessage = null;
        vnode.state.ui = null;
        vnode.state.feedback = null;
    }
};