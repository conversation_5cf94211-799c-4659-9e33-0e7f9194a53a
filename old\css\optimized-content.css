/**

 * 优化版内容页样式

 * 包含骨架屏、加载状态和性能优化样式

 */



/* 基础样式重置 */

* {

    margin: 0;

    padding: 0;

    box-sizing: border-box;

}



body {

    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Aria<PERSON>, sans-serif;

    line-height: 1.6;

    color: #333;

    background-color: #f8f9fa;

}



/* 内容页容器 */

.content-page {

    max-width: 800px;

    margin: 0 auto;

    background: white;

    min-height: 100vh;

    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);

}



/* 内容头部 */

.content-header {

    padding: 40px 40px 20px;

    border-bottom: 1px solid #e9ecef;

    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

    color: white;

}



.content-title {

    font-size: 2.5rem;

    font-weight: 700;

    margin-bottom: 20px;

    line-height: 1.2;

    word-wrap: break-word;

}



.content-meta {

    display: flex;

    align-items: center;

    justify-content: space-between;

}



/* 作者信息 */

.author-info {

    display: flex;

    align-items: center;

    gap: 12px;

}



.author-info.loading .author-avatar {

    background: rgba(255, 255, 255, 0.3);

    animation: skeleton-pulse 1.5s ease-in-out infinite;

}



.author-avatar {

    width: 48px;

    height: 48px;

    border-radius: 50%;

    object-fit: cover;

    border: 2px solid rgba(255, 255, 255, 0.3);

}



.author-details {

    display: flex;

    flex-direction: column;

}



.author-name {

    font-weight: 600;

    font-size: 1.1rem;

    margin-bottom: 2px;

}



.post-time {

    font-size: 0.9rem;

    opacity: 0.8;

}



/* 骨架屏样式 */

.skeleton {

    background: linear-gradient(90deg, rgba(255,255,255,0.2) 25%, rgba(255,255,255,0.4) 50%, rgba(255,255,255,0.2) 75%);

    background-size: 200% 100%;

    animation: skeleton-shimmer 1.5s infinite;

    border-radius: 4px;

}



.skeleton-text {

    height: 20px;

    width: 120px;

    background: linear-gradient(90deg, rgba(255,255,255,0.2) 25%, rgba(255,255,255,0.4) 50%, rgba(255,255,255,0.2) 75%);

    background-size: 200% 100%;

    animation: skeleton-shimmer 1.5s infinite;

    border-radius: 4px;

}



@keyframes skeleton-pulse {

    0%, 100% { opacity: 1; }

    50% { opacity: 0.7; }

}



@keyframes skeleton-shimmer {

    0% { background-position: -200% 0; }

    100% { background-position: 200% 0; }

}



/* 内容主体 */

.content-body {

    padding: 40px;

}



/* 加载状态 */

.content-loading {

    display: flex;

    align-items: center;

    justify-content: center;

    padding: 60px 20px;

    color: #6c757d;

    flex-direction: column;

    gap: 16px;

}



.loading-spinner {

    width: 32px;

    height: 32px;

    border: 3px solid #e9ecef;

    border-top: 3px solid #007bff;

    border-radius: 50%;

    animation: spin 1s linear infinite;

}



@keyframes spin {

    0% { transform: rotate(0deg); }

    100% { transform: rotate(360deg); }

}



/* 内容文本样式 */

#content-text {

    font-size: 1.1rem;

    line-height: 1.8;

    color: #2c3e50;

}



#content-text h1,

#content-text h2,

#content-text h3,

#content-text h4,

#content-text h5,

#content-text h6 {

    margin: 2rem 0 1rem;

    color: #2c3e50;

    font-weight: 600;

}



#content-text h1 { font-size: 2rem; }

#content-text h2 { font-size: 1.75rem; }

#content-text h3 { font-size: 1.5rem; }

#content-text h4 { font-size: 1.25rem; }



#content-text p {

    margin: 1rem 0;

    text-align: justify;

}



#content-text ul,

#content-text ol {

    margin: 1rem 0;

    padding-left: 2rem;

}



#content-text li {

    margin: 0.5rem 0;

}



#content-text blockquote {

    margin: 1.5rem 0;

    padding: 1rem 1.5rem;

    background: #f8f9fa;

    border-left: 4px solid #007bff;

    font-style: italic;

    color: #6c757d;

}



/* 代码样式 */

#content-text code {

    background: #f8f9fa;

    padding: 2px 6px;

    border-radius: 4px;

    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;

    font-size: 0.9em;

    color: #e83e8c;

}



#content-text pre {

    background: #f8f9fa;

    padding: 1.5rem;

    border-radius: 8px;

    overflow-x: auto;

    margin: 1.5rem 0;

    position: relative;

    border: 1px solid #e9ecef;

}



#content-text pre code {

    background: none;

    padding: 0;

    color: #2c3e50;

    font-size: 0.9rem;

}



/* 代码复制按钮 */

.copy-code-btn {

    position: absolute;

    top: 8px;

    right: 8px;

    background: #007bff;

    color: white;

    border: none;

    padding: 4px 8px;

    border-radius: 4px;

    font-size: 0.8rem;

    cursor: pointer;

    opacity: 0.8;

    transition: opacity 0.2s ease;

}



.copy-code-btn:hover {

    opacity: 1;

}



/* 链接样式 */

#content-text a {

    color: #007bff;

    text-decoration: none;

    border-bottom: 1px solid transparent;

    transition: border-color 0.2s ease;

}



#content-text a:hover {

    border-bottom-color: #007bff;

}



/* 图片样式 */

#content-text img {

    max-width: 100%;

    height: auto;

    border-radius: 8px;

    margin: 1rem 0;

    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

    transition: transform 0.2s ease;

}



#content-text img:hover {

    transform: scale(1.02);

}



/* 表格样式 */

#content-text table {

    width: 100%;

    border-collapse: collapse;

    margin: 1.5rem 0;

    background: white;

    border-radius: 8px;

    overflow: hidden;

    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

}



#content-text th,

#content-text td {

    padding: 12px 16px;

    text-align: left;

    border-bottom: 1px solid #e9ecef;

}



#content-text th {

    background: #f8f9fa;

    font-weight: 600;

    color: #495057;

}



#content-text tr:hover {

    background: #f8f9fa;

}



/* 错误页面 */

.error-page {

    display: flex;

    flex-direction: column;

    align-items: center;

    justify-content: center;

    min-height: 60vh;

    text-align: center;

    padding: 40px;

}



.error-icon {

    font-size: 4rem;

    margin-bottom: 1rem;

}



.error-page h2 {

    color: #dc3545;

    margin-bottom: 1rem;

}



.error-page p {

    color: #6c757d;

    margin-bottom: 2rem;

    max-width: 400px;

}



.retry-btn {

    background: #007bff;

    color: white;

    border: none;

    padding: 12px 24px;

    border-radius: 6px;

    cursor: pointer;

    font-size: 1rem;

    transition: background-color 0.2s ease;

}



.retry-btn:hover {

    background: #0056b3;

}



/* 内容错误样式 */

.content-error {

    background: #f8d7da;

    color: #721c24;

    padding: 1rem;

    border-radius: 6px;

    border: 1px solid #f5c6cb;

    margin: 1rem 0;

}



.content-error p {

    margin-bottom: 0.5rem;

}



.content-error pre {

    background: #fff;

    padding: 1rem;

    border-radius: 4px;

    overflow-x: auto;

    margin-top: 0.5rem;

    color: #2c3e50;

}



/* 响应式设计 */

@media (max-width: 768px) {

    .content-page {

        margin: 0;

        box-shadow: none;

    }

    

    .content-header {

        padding: 20px;

    }

    

    .content-title {

        font-size: 2rem;

    }

    

    .content-body {

        padding: 20px;

    }

    

    #content-text {

        font-size: 1rem;

    }

    

    #content-text pre {

        padding: 1rem;

        font-size: 0.8rem;

    }

    

    .copy-code-btn {

        font-size: 0.7rem;

        padding: 3px 6px;

    }

}



@media (max-width: 480px) {

    .content-header {

        padding: 16px;

    }

    

    .content-title {

        font-size: 1.5rem;

    }

    

    .content-body {

        padding: 16px;

    }

    

    .author-info {

        gap: 8px;

    }

    

    .author-avatar {

        width: 40px;

        height: 40px;

    }

}



/* 打印样式 */

@media print {

    .content-page {

        box-shadow: none;

        background: white;

    }

    

    .content-header {

        background: white !important;

        color: black !important;

        border-bottom: 2px solid #000;

    }

    

    .copy-code-btn {

        display: none;

    }

    

    #content-text a {

        color: black;

        text-decoration: underline;

    }

    

    #content-text a:after {

        content: " (" attr(href) ")";

        font-size: 0.8em;

        color: #666;

    }

}



/* 高对比度模式支持 */

@media (prefers-contrast: high) {

    .content-header {

        background: #000 !important;

        color: #fff !important;

    }

    

    #content-text {

        color: #000;

    }

    

    #content-text code {

        background: #f0f0f0;

        color: #000;

    }

    

    #content-text pre {

        background: #f0f0f0;

        border: 2px solid #000;

    }

}



/* 减少动画模式支持 */

@media (prefers-reduced-motion: reduce) {

    .loading-spinner,

    .skeleton,

    .skeleton-text {

        animation: none;

    }

    

    #content-text img {

        transition: none;

    }

    

    .copy-code-btn {

        transition: none;

    }

}