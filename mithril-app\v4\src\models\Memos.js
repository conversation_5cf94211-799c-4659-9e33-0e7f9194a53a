// 使用全局的 Mithril 实例而不是导入
const m = window.m;

// 使用相对路径导入服务和配置
import { MemosService } from '../services/MemosService.js';
import { APP_CONFIG } from '../config.js';

export class Memos {
    constructor() {
        this.memosService = new MemosService();
        this.list = [];
        this.currentPage = 1;
        this.hasMore = true;
        this.loading = false;
        this.preloading = false; // 预加载状态
        // 添加用户缓存
        this.userCache = new Map();
        this.userCacheExpiry = 10 * 60 * 1000; // 10分钟缓存
        
        // 预加载下一页的延迟时间
        this.preloadDelay = 1000;
        this.preloadTimer = null;
    }
    
    // 加载 memo 列表
    async loadMemos(page = 1, limit = APP_CONFIG.recordsPerPage || 10) {
        // 如果是第一页，显示加载状态
        if (page === 1) {
            this.loading = true;
            m.redraw();
        }
        
        try {
            let response;
            
            // 根据登录状态决定加载公开内容还是所有内容
            if (window.auth && window.auth.isAuthenticated) {
                // 登录用户加载所有内容
                response = await this.memosService.getMemos(page, limit);
            } else {
                // 未登录用户只加载公开内容
                response = await this.memosService.getPublicMemos(page, limit);
            }
            
            // 检查响应是否成功
            if (response && response.success) {
                // 收集所有唯一的用户ID
                const userIds = [...new Set(
                    response.data.records
                        .filter(record => record.fields.user_id && record.fields.user_id.length > 0)
                        .map(record => record.fields.user_id[0])
                )];
                
                // 批量获取用户信息
                const userCache = {};
                if (userIds.length > 0) {
                    try {
                        // 获取所有用户信息
                        const usersResult = await this.memosService.getUsers(userIds);
                        if (usersResult && usersResult.success && usersResult.data) {
                            usersResult.data.forEach(userRecord => {
                                if (userIds.includes(userRecord.recordId)) {
                                    const userData = userRecord.fields;
                                    userCache[userRecord.recordId] = {
                                        username: userData.用户名 || '用户',
                                        nickname: userData.昵称 || userData.用户名 || '用户',
                                        id: userRecord.recordId,
                                        avatar: userData.头像 || `https://ui-avatars.com/api/?name=${encodeURIComponent(userData.昵称 || userData.用户名 || '用户')}&background=3B82F6&color=fff`
                                    };
                                }
                            });
                        }
                    } catch (userError) {
                        console.warn('批量获取用户信息失败:', userError);
                    }
                }
                
                // 处理备忘录记录
                let memosData = response.data.records.map(record => {
                    // 处理关联字段 user_id
                    let author = { username: '匿名用户', nickname: '匿名用户', id: null, avatar: '' };
                    
                    if (record.fields.user_id && record.fields.user_id.length > 0) {
                        const userId = record.fields.user_id[0];
                        // 从缓存中获取用户信息
                        if (userCache[userId]) {
                            author = userCache[userId];
                        }
                    }
                    
                    // 处理媒体文件（图片和视频）
                    const mediaFiles = [];
                    if (record.fields.picurls) {
                        const urls = record.fields.picurls.split(',').map(url => url.trim()).filter(url => url);
                        urls.forEach(url => {
                            // 根据文件扩展名判断类型
                            const isVideo = /\.(mp4|webm|ogg)$/i.test(url);
                            mediaFiles.push({
                                url: url,
                                type: isVideo ? 'video' : 'image'
                            });
                        });
                    }

                    return {
                        id: record.recordId,
                        content: record.fields.content || '',
                        createdTime: record.fields.posttime || record.fields.createdAt || record.createdTime,
                        updatedTime: record.fields.updatedAt || record.updatedTime,
                        images: mediaFiles.filter(file => file.type === 'image').map(file => file.url), // 保持向后兼容
                        videos: mediaFiles.filter(file => file.type === 'video').map(file => file.url), // 新增视频字段
                        mediaFiles: mediaFiles, // 完整的媒体文件信息
                        user: author,
                        status: record.fields.status || '公开'
                    };
                });
                
                // 前端再次过滤：未登录用户只能看到公开内容
                if (!window.auth || !window.auth.isAuthenticated) {
                    memosData = memosData.filter(memo => memo.status === '公开');
                }
                
                if (page === 1) {
                    this.list = memosData;
                } else {
                    this.list = [...this.list, ...memosData];
                }
                
                this.currentPage = page;
                this.hasMore = memosData.length === limit;
                
                // 如果是第一页且有更多数据，预加载下一页
                if (page === 1 && this.hasMore && !this.preloading) {
                    this.schedulePreload();
                }
            } else {
                // 显示API返回的错误信息
                console.error('API调用失败:', response ? response.error : '未知错误');
                window.appState.showNotification(`数据加载失败: ${response ? response.error : '未知错误'}`, 'error');
            }
        } catch (error) {
            // 显示网络错误信息
            console.error('网络错误:', error.message);
            window.appState.showNotification(`网络错误: ${error.message}`, 'error');
        } finally {
            this.loading = false;
            m.redraw();
        }
    }
    
    // 发布新的 memo
    async createMemo(content, uploadedFiles = [], status = '公开') {
        try {
            // uploadedFiles 现在是已经上传完成的文件对象数组，包含 URL、类型等信息
            // 调用API创建备忘录
            const response = await this.memosService.createMemo(content, uploadedFiles, status);
            
            // 检查响应是否成功
            if (response && response.success) {
                const memoData = response.data;
                const fields = memoData.fields;
                
                // 处理媒体文件（图片和视频）
                const mediaFiles = [];
                if (fields.picurls) {
                    const urls = fields.picurls.split(',').map(url => url.trim()).filter(url => url);
                    urls.forEach(url => {
                        // 根据文件扩展名判断类型
                        const isVideo = /\.(mp4|webm|ogg)$/i.test(url);
                        mediaFiles.push({
                            url: url,
                            type: isVideo ? 'video' : 'image'
                        });
                    });
                }

                // 添加到列表开头
                const newMemo = {
                    id: memoData.recordId,
                    content: fields.content || '',
                    createdTime: fields.createdAt || fields.createdTime,
                    updatedTime: fields.updatedAt || fields.updatedTime,
                    images: mediaFiles.filter(file => file.type === 'image').map(file => file.url), // 保持向后兼容
                    videos: mediaFiles.filter(file => file.type === 'video').map(file => file.url), // 新增视频字段
                    mediaFiles: mediaFiles, // 完整的媒体文件信息
                    user: window.auth.currentUser || { 
                        username: '匿名用户', 
                        nickname: '匿名用户', 
                        id: null, 
                        avatar: '' 
                    },
                    status: fields.status || '公开'
                };
                
                this.list.unshift(newMemo);
                
                // 强制重新渲染，确保新备忘录立即显示
                m.redraw();
                
                // 延迟再次重绘，确保媒体文件正确渲染
                setTimeout(() => {
                    m.redraw();
                }, 50);
                
                console.log('新备忘录已添加到列表:', newMemo); // 调试日志
                return { success: true, data: newMemo };
            } else {
                const errorMessage = response ? response.error : '发布失败';
                window.appState.showNotification(`发布失败: ${errorMessage}`, 'error');
                return { success: false, error: errorMessage };
            }
        } catch (error) {
            window.appState.showNotification(`网络错误: ${error.message}`, 'error');
            return { success: false, error: error.message };
        }
    }
    
    // 预加载下一页数据
    schedulePreload() {
        if (this.preloadTimer) {
            clearTimeout(this.preloadTimer);
        }
        
        this.preloadTimer = setTimeout(() => {
            this.preloadNextPage();
        }, this.preloadDelay);
    }
    
    // 预加载下一页
    async preloadNextPage() {
        if (this.preloading || !this.hasMore) return;
        
        this.preloading = true;
        const nextPage = this.currentPage + 1;
        
        try {
            let response;
            
            // 根据登录状态决定加载公开内容还是所有内容
            if (window.auth && window.auth.isAuthenticated) {
                response = await this.memosService.getMemos(nextPage, APP_CONFIG.recordsPerPage || 10);
            } else {
                response = await this.memosService.getPublicMemos(nextPage, APP_CONFIG.recordsPerPage || 10);
            }
            
            if (response && response.success) {
                // 预处理数据但不添加到列表中，只是缓存
                console.log(`预加载第${nextPage}页数据成功，共${response.data.records.length}条`);
            }
        } catch (error) {
            console.warn('预加载失败:', error);
        } finally {
            this.preloading = false;
        }
    }
    
    // 加载更多数据（优化版本）
    async loadMore() {
        if (this.loading || !this.hasMore) return;
        
        const nextPage = this.currentPage + 1;
        await this.loadMemos(nextPage);
    }
    
    // 刷新数据
    async refresh() {
        this.list = [];
        this.currentPage = 1;
        this.hasMore = true;
        await this.loadMemos(1);
    }
    
    // 清除缓存
    clearCache() {
        this.memosService.clearCache();
        this.userCache.clear();
        if (this.preloadTimer) {
            clearTimeout(this.preloadTimer);
            this.preloadTimer = null;
        }
    }
}