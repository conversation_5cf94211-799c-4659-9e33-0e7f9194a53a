# 时间记录应用 API 实现文档

## 🌐 API 架构概览

### 技术栈
- **API 平台**: Vika API (飞书多维表格)
- **协议**: RESTful API
- **认证**: Bearer <PERSON>ken
- **数据格式**: JSON
- **请求方式**: GET, POST, PATCH, DELETE

### 基础配置
```javascript
// 核心配置 (src/config.js)
const API_CONFIG = {
    datasheetId: 'dstCo3t04QBY3RweuR',     // 主数据表ID
    timeSheetDatasheetId: 'dsta8guxXYK3pCYsVe', // 时间表ID
    userDatasheetId: 'dst13vlcxlMi1EanCl',   // 用户表ID
    token: 'uskcZUvxWXvLIPXN0hUC6DK',        // 认证Token
    baseURL: 'https://api.vika.cn/fusion/v1', // API基础URL
    viewId: 'viwYoodJk54Xt',                 // 默认视图ID
    fieldKey: 'name'                         // 字段键名
};
```

---

## 📊 数据表结构

### 时间表结构 (timeSheetDatasheetId)
| 字段ID | 字段名称 | 数据类型 | 说明 |
|--------|----------|----------|------|
| `fld0znfZ7H9xM` | 备注信息 | 文本 | 记录内容和时间信息 |

### 用户表结构 (userDatasheetId)
| 字段名称 | 数据类型 | 说明 |
|----------|----------|------|
| 用户名 | 文本 | 用户登录名 |
| 密码 | 文本 | 用户密码 |
| 用户ID | 文本 | 唯一标识符 |

---

## 🔧 服务层架构

### 1. 核心API服务 (ApiService.js)
```javascript
/**
 * 统一API服务类
 * 功能特性：
 * - 请求限流 (200ms间隔，最大3并发)
 * - 指数退避重试 (最大3次)
 * - 智能缓存 (2分钟有效期)
 * - 错误处理增强
 * - 性能监控
 */

class ApiService {
    // 请求队列管理
    apiQueue = []           // 待处理请求队列
    isProcessingQueue = false
    requestDelay = 200ms    // 请求间隔
    maxRetries = 3          // 最大重试次数
    maxConcurrentRequests = 3 // 最大并发数
    activeRequests = 0      // 当前活跃请求数
    
    // 缓存系统
    requestCache = new Map()
    cacheTimeout = 2 * 60 * 1000 // 2分钟缓存
}
```

#### 请求限流机制
```javascript
/**
 * 智能请求队列处理
 * - 支持并发请求
 * - 限流保护
 * - 错误重试
 */
async processApiQueue() {
    while (队列有请求 && 活跃请求 < 最大并发) {
        处理请求()
        指数退避重试()
    }
}
```

#### 缓存策略
```javascript
/**
 * 多层缓存系统
 * - GET请求自动缓存
 * - 2分钟有效期
 * - 缓存键基于URL和参数
 */
const cacheKey = `${url}_${JSON.stringify(params)}`
if (缓存有效) {
    return 缓存数据
}
```

### 2. 时间记录服务 (TimeRecordService.js)
```javascript
/**
 * 时间记录专用服务
 * 基于Vika多维表格API
 */
class TimeRecordService {
    constructor() {
        this.apiService = new ApiService()  // 复用核心服务
        this.cache = new Map()              // 业务层缓存
        this.cacheTimeout = 2 * 60 * 1000   // 2分钟缓存
    }
}
```

---

## 🚀 API 端点详解

### 时间记录相关API

#### 1. 获取时间记录列表
```http
GET /datasheets/{timeSheetDatasheetId}/records
```

**请求参数：**
```javascript
{
    viewId: 'viw5mLr0sA5d9',    // 视图ID
    pageSize: 10,              // 每页条数
    pageNum: 1,                // 页码
    fieldKey: 'id'             // 字段键类型
}
```

**响应格式：**
```javascript
{
    success: true,
    data: {
        records: [
            {
                recordId: "rec123",
                fields: {
                    "fld0znfZ7H9xM": "工作内容描述 (2024-08-12 10:30-11:45, 4500秒)"
                }
            }
        ]
    }
}
```

#### 2. 创建时间记录
```http
POST /datasheets/{timeSheetDatasheetId}/records
```

**请求体：**
```javascript
{
    records: [{
        fields: {
            'fld0znfZ7H9xM': '完成用户认证功能开发 (2024-08-12 10:30-11:45, 4500秒)'
        }
    }]
}
```

**创建流程：**
```javascript
async createTimeRecord(recordData) {
    // 1. 数据预处理
    const startTimestamp = Math.floor(new Date(`${recordData.date} ${recordData.startTime}`).getTime() / 1000)
    const endTimestamp = Math.floor(new Date(`${recordData.date} ${recordData.endTime}`).getTime() / 1000)
    
    // 2. 构建请求负载
    const payload = {
        records: [{
            fields: {
                'fld0znfZ7H9xM': `${recordData.content} (${recordData.date} ${recordData.startTime}-${recordData.endTime}, ${recordData.duration}秒)`
            }
        }]
    }
    
    // 3. 发送请求并处理响应
    const response = await this.apiService.post(endpoint, payload)
    
    // 4. 缓存清理
    this.clearCache()
}
```

### 用户认证相关API

#### 用户登录验证
```http
GET /datasheets/{userDatasheetId}/records
```

**请求参数：**
```javascript
{
    viewId: 'viwHzmgPteBwK',
    fieldKey: 'name',
    filterByFormula: 'AND({username} = \'用户名\', {password} = \'密码\')'
}
```

---

## 🔒 错误处理机制

### 错误类型映射
| HTTP状态 | 错误类型 | 用户提示 |
|----------|----------|----------|
| 401 | 认证失败 | "认证失败，请检查API Token" |
| 429 | 限流错误 | "服务繁忙，请稍后再试" |
| 400 | 请求格式错误 | "请求格式错误，请检查数据" |
| NetworkError | 网络错误 | "网络连接失败，请检查网络" |
| CORS | 跨域错误 | "跨域请求被阻止，请检查网络设置" |

### 错误处理流程
```javascript
try {
    const response = await apiCall()
} catch (error) {
    switch (error.type) {
        case 'CORS':
            return '跨域请求被阻止'
        case 'NetworkError':
            return '网络连接失败'
        case 401:
            return '认证失败'
        case 429:
            // 指数退避重试
            await delay(Math.pow(2, retryCount) * 500)
            return retry()
        default:
            return `请求失败: ${error.message}`
    }
}
```

---

## ⚡ 性能优化策略

### 1. 请求优化
- **并发控制**: 最大3个并发请求
- **请求间隔**: 200ms限流保护
- **重试机制**: 指数退避，最大3次重试

### 2. 缓存策略
```javascript
// 多层缓存架构
┌─────────────────┐
│   浏览器缓存     │  ← 2分钟有效期
├─────────────────┤
│  业务层缓存      │  ← 2分钟有效期  
├─────────────────┤
│  API服务缓存     │  ← 2分钟有效期
└─────────────────┘
```

### 3. 数据优化
- **分页加载**: 10条/页，减少单次数据传输
- **字段精简**: 只获取必要字段
- **压缩传输**: JSON格式，减少数据体积

### 4. 性能监控
```javascript
// 集成性能监控
performanceMonitor.start(requestName)
const response = await apiCall()
performanceMonitor.end(requestName)
```

---

## 🧪 测试与调试

### API连接测试
```javascript
/**
 * 完整的API连接测试流程
 */
async testConnection() {
    // 1. 获取表结构信息
    const metaResponse = await get('/datasheets/{id}/fields')
    
    // 2. 获取示例记录
    const sampleResponse = await get('/datasheets/{id}/records', { pageSize: 1 })
    
    // 3. 测试创建记录
    const createResult = await post('/datasheets/{id}/records', testPayload)
    
    // 4. 验证响应格式
    console.log('字段详情:', metaResponse.data.fields)
    console.log('示例记录:', sampleResponse.records)
    console.log('创建测试:', createResult)
}
```

### 调试输出
```javascript
// 详细的调试信息
console.log('📡 API请求:', {
    endpoint: url,
    method: method,
    params: params,
    timestamp: new Date().toISOString()
})

console.log('✅ API响应:', {
    status: response.status,
    dataSize: JSON.stringify(data).length,
    duration: performanceMonitor.getDuration()
})
```

---

## 🔧 使用示例

### 基础使用
```javascript
// 初始化服务
const timeService = new TimeRecordService()

// 获取记录
const records = await timeService.getTimeRecords(1, 10)

// 创建记录
const newRecord = await timeService.createTimeRecord({
    content: '完成API文档编写',
    date: '2024-08-12',
    startTime: '14:00',
    endTime: '15:30',
    duration: 5400
})
```

### 高级用法
```javascript
// 带错误处理的完整流程
try {
    const result = await timeService.createTimeRecord(recordData)
    if (result.success) {
        // 成功处理
        showSuccess('记录创建成功')
        refreshRecords()
    } else {
        // 错误处理
        showError(result.error)
    }
} catch (error) {
    // 网络错误处理
    showNetworkError()
}
```

---

## 📊 监控指标

### 关键指标
| 指标 | 目标值 | 监控方式 |
|------|--------|----------|
| API响应时间 | < 1秒 | performanceMonitor |
| 错误率 | < 5% | 错误计数器 |
| 缓存命中率 | > 70% | 缓存统计 |
| 并发请求数 | ≤ 3 | 队列监控 |

### 性能基准
- **GET请求**: 平均响应时间 < 500ms
- **POST请求**: 平均响应时间 < 800ms
- **缓存命中率**: 目标 > 70%
- **错误重试成功率**: 目标 > 90%

---

*最后更新：2024年8月12日*  
*API版本：v1.0.0*  
*文档版本：v1.0.1*