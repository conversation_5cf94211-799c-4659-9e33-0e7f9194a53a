/**
 * 时间记录数据模型
 * 管理时间记录的状态和业务逻辑
 */
const m = window.m;

import { TimeRecordService } from '../services/TimeRecordService.js';
import { APP_CONFIG } from '../config.js';
import { formatDuration } from '../utils/format.js';

export class TimeRecord {
    constructor() {
        this.timeRecordService = new TimeRecordService();
        this.records = [];
        this.currentPage = 1;
        this.hasMore = true;
        this.loading = false;
        
        // 内容输入
        this.content = '';
    }

    /**
     * 加载时间记录列表
     * @param {number} page - 页码
     * @param {boolean} reset - 是否重置列表
     */
    async loadRecords(page = 1, reset = false) {
        if (this.loading) return;

        this.loading = true;
        if (reset) this.records = [];

        try {
            const response = await this.timeRecordService.getTimeRecords(page, APP_CONFIG.recordsPerPage);
            if (response.success) {
                const records = response.data.records || [];
                // 过滤掉格式化失败的记录
                const formattedRecords = records
                    .map(record => this.formatRecord(record))
                    .filter(record => record !== null);
                
                this.records = reset ? formattedRecords : [...this.records, ...formattedRecords];
                this.currentPage = page;
                this.hasMore = formattedRecords.length === APP_CONFIG.recordsPerPage;
                
                console.log(`成功加载 ${formattedRecords.length} 条时间记录`);
            } else {
                console.error('加载时间记录失败:', response.error);
                window.appState.showNotification(`加载失败: ${response.error}`, 'error');
            }
        } catch (error) {
            console.error('加载时间记录失败:', error);
            // 提供更详细的错误信息
            let errorMessage = '加载时间记录时发生错误';
            if (error.message.includes('验证')) {
                errorMessage = '数据验证失败，请检查数据格式';
            } else if (error.message.includes('网络')) {
                errorMessage = '网络连接失败，请检查网络设置';
            }
            window.appState.showNotification(errorMessage, 'error');
        } finally {
            this.loading = false;
            m.redraw();
        }
    }

    /**
     * 格式化记录数据
     * @param {Object} record - 原始记录数据
     * @returns {Object} 格式化后的记录
     */
    formatRecord(record) {
        // 确保记录结构正确
        if (!record) {
            console.warn('formatRecord: 记录为空');
            return null;
        }
        
        // 标准化记录ID
        const recordId = record.recordId || record.id;
        if (!recordId) {
            console.warn('formatRecord: 记录缺少ID字段', record);
            return null;
        }
        
        // 确保fields字段存在
        const fields = record.fields || {};
        
        // 从时间戳字段获取时间信息
        const startTimestamp = fields['fld3e659QaQ11']; // 开始时间戳(秒)
        const endTimestamp = fields['flddjkaHotlMJ']; // 结束时间戳(秒)
        
        let date = new Date().toISOString().split('T')[0];
        let startTime = '';
        let endTime = '';
        let duration = 0;
        
        if (startTimestamp && endTimestamp) {
            const startDate = new Date(startTimestamp * 1000);
            const endDate = new Date(endTimestamp * 1000);
            
            date = startDate.toISOString().split('T')[0];
            startTime = startDate.toTimeString().slice(0, 8);
            endTime = endDate.toTimeString().slice(0, 8);
            duration = endTimestamp - startTimestamp;
        }
        
        return {
            id: recordId,
            date: date,
            startTime: startTime,
            endTime: endTime,
            duration: duration,
            content: fields['fld0znfZ7H9xM'] || '', // 备注信息
            createdAt: new Date(fields['fld1pawhrPs9x'] || record.createdAt || Date.now()).toISOString(),
            formattedDuration: this.formatDuration(duration)
        };
    }

    /**
     * 格式化时长（秒转时分秒）
     * @param {number} seconds - 秒数
     * @returns {string} 格式化后的时长字符串
     */
    formatDuration(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;
        
        const parts = [];
        if (hours > 0) parts.push(`${hours}小时`);
        if (minutes > 0 || hours > 0) parts.push(`${minutes}分钟`);
        parts.push(`${secs}秒`);
        
        return parts.join('');
    }

    /**
     * 开始计时 - 使用全局状态
     * @returns {boolean} 是否成功开始计时
     */
    startTimer() {
        try {
            console.log('TimeRecord.startTimer: 开始调用');
            console.log('window.appState存在:', !!window.appState);
            console.log('window.appState.startTimer存在:', !!(window.appState && window.appState.startTimer));
            
            if (window.appState && window.appState.startTimer) {
                window.appState.startTimer();
                console.log('TimeRecord.startTimer: 成功调用全局状态方法');
                return true;
            } else {
                console.error('TimeRecord.startTimer: 全局状态或方法不存在');
                return false;
            }
        } catch (error) {
            console.error('TimeRecord.startTimer: 执行失败', error);
            return false;
        }
    }

    /**
     * 结束计时 - 使用全局状态
     * @returns {boolean} 是否成功结束计时
     */
    stopTimer() {
        try {
            window.appState.stopTimer();
            return true;
        } catch (error) {
            console.error('结束计时失败:', error);
            return false;
        }
    }

    /**
     * 重置计时 - 使用全局状态
     * @returns {boolean} 是否成功重置计时器
     */
    resetTimer() {
        try {
            window.appState.resetTimer();
            this.content = '';
            return true;
        } catch (error) {
            console.error('重置计时器失败:', error);
            return false;
        }
    }

    /**
     * 保存时间记录
     * @returns {Promise<boolean>} 是否保存成功
     */
    async saveRecord(content = '') {
        if (!window.appState.startTime || window.appState.accumulatedDuration <= 0) {
            console.error('saveRecord: 没有有效的计时数据');
            return false;
        }
        
        // 使用全局状态的数据
        const startTime = new Date(window.appState.startTime);
        const endTime = new Date(window.appState.startTime + (window.appState.accumulatedDuration * 1000));
        
        const recordData = {
            date: startTime.toISOString().split('T')[0],
            startTime: this.formatTime(startTime),
            endTime: this.formatTime(endTime),
            duration: window.appState.accumulatedDuration,
            content: content || this.content,
            createdAt: new Date().toISOString()
        };
        
        console.log('saveRecord: 准备保存记录:', recordData);
        
        try {
            const response = await this.timeRecordService.createTimeRecord(recordData);
            console.log('saveRecord: 收到响应:', response);
            
            if (response.success) {
                // 添加到记录列表
                this.records.unshift(this.formatRecord(response.data));
                // 重置计时器
                this.resetTimer();
                window.appState.showNotification('时间记录已保存', 'success');
                return true;
            } else {
                console.log('saveRecord: 保存失败，响应错误:', response.error);
                window.appState.showNotification(`保存失败: ${response.error}`, 'error');
                return false;
            }
        } catch (error) {
            console.error('保存时间记录失败:', error);
            window.appState.showNotification('保存时间记录时发生错误', 'error');
            return false;
        }
    }

    /**
     * 格式化时间为HH:MM:SS
     * @param {Date} date - 日期对象
     * @returns {string} 格式化后的时间字符串
     */
    formatTime(date) {
        return date.toTimeString().slice(0, 8);
    }
}

// 挂载到window对象，供浏览器环境使用
window.TimeRecord = TimeRecord;