/**

 * 内容页应用模块

 * 专门处理内容详情页的逻辑

 */



import { API_CONFIG } from './config.js';

import {

    getElement,

    showNotification,

    formatDate

} from './modules/utils.js';

import { ThemeManager } from './modules/theme.js';



// DOM 元素引用

const dom = {

    // 头部

    backButton: getElement('#backButton'),

    lightThemeBtn: getElement('#lightThemeBtn'),

    darkThemeBtn: getElement('#darkThemeBtn'),

    eyeProtectBtn: getElement('#eyeProtectBtn'),



    // 内容区域

    contentCard: getElement('#contentCard'),

    contentLoading: getElement('#contentLoading'),

    contentError: getElement('#contentError'),

    errorMessage: getElement('#errorMessage'),

    retryButton: getElement('#retryButton'),

    contentMain: getElement('#contentMain'),



    // 内容详情

    contentAvatar: getElement('#contentAvatar'),

    contentAuthor: getElement('#contentAuthor'),

    contentDate: getElement('#contentDate'),

    contentStatus: getElement('#contentStatus'),

    contentId: getElement('#contentId'),

    contentText: getElement('#contentText'),

    contentImageContainer: getElement('#contentImageContainer'),

    contentImages: getElement('#contentImages'),

    contentTags: getElement('#contentTags'),

    tagsList: getElement('#tagsList'),



    // 操作按钮

    shareButton: getElement('#shareButton'),

    copyLinkButton: getElement('#copyLinkButton'),

    generateCardBtn: getElement('#generateCardBtn'),



    // 模态窗

    cardModal: getElement('#cardModal'),

    cardPreview: getElement('#cardPreview'),

    downloadCardBtn: getElement('#downloadCardBtn'),

    closeCardModal: getElement('#closeCardModal'),

    cancelCardBtn: getElement('#cancelCardBtn')

};



// 内容页应用类

class ContentApp {

    constructor() {

        this.themeManager = new ThemeManager();

        this.currentContent = null;

        this.isInitialized = false;

    }



    async init() {

        if (this.isInitialized) return;



        try {

            console.log('开始初始化内容页应用...');



            // 等待外部脚本加载完成

            await this.waitForScripts();



            // 重新获取DOM元素（因为组件是异步加载的）

            this.refreshDOMReferences();



            // 初始化主题管理器

            this.themeManager.init();



            // 初始化事件监听

            this.initEventListeners();



            // 加载内容

            await this.loadContent();



            this.isInitialized = true;

            console.log('内容页应用初始化完成');



        } catch (error) {

            console.error('内容页应用初始化失败:', error);

            this.showError('页面初始化失败，请刷新重试');

        }

    }



    /**

     * 重新获取DOM元素引用

     */

    refreshDOMReferences() {

        console.log('刷新内容页DOM元素引用...');



        // 头部

        dom.backButton = getElement('#backButton');

        dom.lightThemeBtn = getElement('#lightThemeBtn');

        dom.darkThemeBtn = getElement('#darkThemeBtn');

        dom.eyeProtectBtn = getElement('#eyeProtectBtn');



        // 内容区域

        dom.contentCard = getElement('#contentCard');

        dom.contentLoading = getElement('#contentLoading');

        dom.contentError = getElement('#contentError');

        dom.errorMessage = getElement('#errorMessage');

        dom.retryButton = getElement('#retryButton');

        dom.contentMain = getElement('#contentMain');



        // 内容详情

        dom.contentAvatar = getElement('#contentAvatar');

        dom.contentAuthor = getElement('#contentAuthor');

        dom.contentDate = getElement('#contentDate');

        dom.contentStatus = getElement('#contentStatus');

        dom.contentId = getElement('#contentId');

        dom.contentText = getElement('#contentText');

        dom.contentImageContainer = getElement('#contentImageContainer');

        dom.contentImages = getElement('#contentImages');

        dom.contentTags = getElement('#contentTags');

        dom.tagsList = getElement('#tagsList');



        // 操作按钮

        dom.shareButton = getElement('#shareButton');

        dom.copyLinkButton = getElement('#copyLinkButton');

        dom.generateCardBtn = getElement('#generateCardBtn');



        // 模态窗

        dom.cardModal = getElement('#cardModal');

        dom.cardPreview = getElement('#cardPreview');

        dom.downloadCardBtn = getElement('#downloadCardBtn');

        dom.closeCardModal = getElement('#closeCardModal');

        dom.cancelCardBtn = getElement('#cancelCardBtn');



        console.log('内容页DOM元素引用已刷新');

    }



    /**

     * 初始化事件监听

     */

    initEventListeners() {

        // 主题切换事件

        dom.lightThemeBtn?.addEventListener('click', () => {

            this.themeManager.setTheme('light');

        });



        dom.darkThemeBtn?.addEventListener('click', () => {

            this.themeManager.setTheme('dark');

        });



        dom.eyeProtectBtn?.addEventListener('click', () => {

            this.themeManager.setTheme('eyeProtect');

        });



        // 返回按钮

        dom.backButton?.addEventListener('click', () => {

            this.goBack();

        });



        // 重试按钮

        dom.retryButton?.addEventListener('click', () => {

            this.loadContent();

        });



        // 分享按钮

        dom.shareButton?.addEventListener('click', () => {

            this.shareContent();

        });



        // 复制链接按钮

        dom.copyLinkButton?.addEventListener('click', () => {

            this.copyLink();

        });



        // 生成卡片按钮

        dom.generateCardBtn?.addEventListener('click', () => {

            this.generateCard();

        });



        // 模态窗关闭按钮

        dom.closeCardModal?.addEventListener('click', () => {

            this.closeCardModal();

        });



        dom.cancelCardBtn?.addEventListener('click', () => {

            this.closeCardModal();

        });



        // 点击模态窗背景关闭

        dom.cardModal?.addEventListener('click', (e) => {

            if (e.target === dom.cardModal) {

                this.closeCardModal();

            }

        });

    }



    /**

     * 获取URL参数

     */

    getQueryParam(param) {

        const urlParams = new URLSearchParams(window.location.search);

        return urlParams.get(param);

    }



    /**

     * 加载内容

     */

    async loadContent() {

        const recordId = this.getQueryParam('id');

        if (!recordId) {

            this.showError('未找到内容ID');

            return;

        }



        try {

            // 显示加载状态

            this.showLoading();



            const response = await fetch(`${API_CONFIG.baseURL}/datasheets/${API_CONFIG.datasheetId}/records?recordIds=${recordId}`, {

                headers: {

                    'Authorization': `Bearer ${API_CONFIG.token}`,

                    'Content-Type': 'application/json'

                }

            });



            if (!response.ok) {

                const errorMessage = response.status === 404 ? '内容不存在' :

                    response.status === 401 ? '无权访问此内容' :

                        response.status === 403 ? '访问被拒绝' :

                            '获取内容失败，请稍后重试';

                throw new Error(errorMessage);

            }



            const data = await response.json();



            if (!data || !data.data || !data.data.records || data.data.records.length === 0) {

                throw new Error('服务器返回数据格式错误');

            }



            const record = data.data.records[0];

            console.log('原始记录数据:', record); // 调试日志



            let content = {

                ...record.fields,

                id: record.recordId,

                created_at: record.fields.posttime || record.fields.createdAt || record.createdTime

            };



            // 获取用户信息

            let author = { username: '匿名用户', nickname: '匿名用户', id: null, avatar: '' };



            console.log('检查用户ID字段:', record.fields.user_id); // 调试日志



            if (record.fields.user_id && record.fields.user_id.length > 0) {

                const userId = record.fields.user_id[0];

                console.log('提取的用户ID:', userId); // 调试日志

                try {

                    // 使用批量查询方式获取用户信息，更可靠

                    const userResponse = await fetch(`${API_CONFIG.baseURL}/datasheets/${API_CONFIG.userDatasheetId}/records?recordIds=${userId}&fieldKey=name`, {

                        headers: {

                            'Authorization': `Bearer ${API_CONFIG.token}`,

                            'Content-Type': 'application/json'

                        }

                    });



                    if (userResponse.ok) {

                        const userData = await userResponse.json();

                        if (userData && userData.data && userData.data.records && userData.data.records.length > 0) {

                            const userFields = userData.data.records[0].fields;

                            author = {

                                username: userFields.用户名 || '用户',

                                nickname: userFields.昵称 || userFields.用户名 || '用户',

                                id: userId,

                                avatar: userFields.头像 || `https://ui-avatars.com/api/?name=${encodeURIComponent(userFields.昵称 || userFields.用户名 || '用户')}&background=3B82F6&color=fff`

                            };

                            console.log('成功获取用户信息:', author);

                        } else {

                            console.warn('用户记录不存在:', userId);

                        }

                    } else {

                        console.warn('用户API调用失败:', userResponse.status, userResponse.statusText);

                    }

                } catch (userError) {

                    console.warn('获取用户信息失败:', userError);

                }

            } else {

                console.log('没有用户ID信息，使用匿名用户');

                // 尝试从其他字段获取作者信息

                if (record.fields.author || record.fields.作者 || record.fields.用户) {

                    const authorName = record.fields.author || record.fields.作者 || record.fields.用户;

                    author = {

                        username: authorName,

                        nickname: authorName,

                        id: null,

                        avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(authorName)}&background=3B82F6&color=fff`

                    };

                    console.log('从其他字段获取作者信息:', author);

                }

            }



            // 将作者信息添加到内容中

            content.author = author;



            // 检查访问权限

            const isLoggedIn = localStorage.getItem('isLoggedIn') === 'true';

            if (content.status === '私密' && !isLoggedIn) {

                this.showError('此内容为私密内容，请登录后查看');

                return;

            }



            await this.showContent(content);



        } catch (error) {

            console.error('加载内容失败:', error);

            this.showError(error.message || '加载内容失败，请稍后重试');

        }

    }



    /**

     * 显示加载状态

     */

    showLoading() {

        dom.contentLoading?.classList.remove('hidden');

        dom.contentError?.classList.add('hidden');

        dom.contentMain?.classList.add('hidden');

    }



    /**

     * 显示错误信息

     */

    showError(message) {

        dom.contentLoading?.classList.add('hidden');

        dom.contentMain?.classList.add('hidden');

        dom.contentError?.classList.remove('hidden');

        if (dom.errorMessage) {

            dom.errorMessage.textContent = message;

        }

    }



    /**

     * 显示内容

     */

    async showContent(content) {

        this.currentContent = content;



        // 设置作者信息

        const authorName = content.author?.nickname || content.author?.username || '匿名用户';

        if (dom.contentAuthor) {

            dom.contentAuthor.textContent = authorName;

        }



        // 设置头像

        if (dom.contentAvatar) {

            const avatar = content.author?.avatar || `https://ui-avatars.com/api/?name=${encodeURIComponent(authorName)}&background=3B82F6&color=fff`;

            dom.contentAvatar.src = avatar;

            dom.contentAvatar.alt = authorName;

        }



        // 设置日期

        if (dom.contentDate) {

            dom.contentDate.textContent = formatDate(content.created_at);

        }



        // 设置记录ID

        if (dom.contentId) {

            dom.contentId.textContent = `#${content.id.slice(-8)}`;

        }



        // 设置状态

        if (dom.contentStatus) {

            dom.contentStatus.textContent = content.status || '公开';

            dom.contentStatus.className = `memo-status ${(content.status || '公开') === '公开' ? 'public' : 'private'}`;

        }



        // 设置内容

        if (dom.contentText) {

            try {

                let renderedContent = '';

                

                // 检查markdown-it是否可用

                if (typeof window.markdownit !== 'undefined') {

                    // 使用window.md如果已经初始化

                    if (typeof window.md !== 'undefined' && window.md) {

                        renderedContent = window.md.render(content.content || '');

                    } else {

                        // 动态初始化markdown-it

                        const md = window.markdownit({

                            html: true,

                            linkify: true,

                            typographer: true,

                            breaks: true,

                            highlight: function (str, lang) {

                                if (typeof window.hljs !== 'undefined' && window.hljs.getLanguage && window.hljs.getLanguage(lang)) {

                                    try {

                                        return window.hljs.highlight(str, { language: lang }).value;

                                    } catch (__) {}

                                }

                                

                                // 返回转义后的代码而不是空字符串

                                try {

                                    return `<pre class="hljs"><code class="language-${lang || 'plaintext'}">${window.markdownit().utils.escapeHtml(str)}</code></pre>`;

                                } catch (escapeError) {

                                    console.error('HTML转义失败:', escapeError);

                                    return `<pre><code class="language-${lang || 'plaintext'}">${str.replace(/[&<>"']/g, c => ({

                                        '&': '&amp;',

                                        '<': '&lt;',

                                        '>': '&gt;',

                                        '"': '&quot;',

                                        "'": '&#39;'

                                    }[c]))}</code></pre>`;

                                }

                            }

                        });

                        

                        renderedContent = md.render(content.content || '');

                    }

                } else {

                    console.warn('markdown-it未加载，使用简单文本渲染');

                    // 降级到简单文本渲染

                    renderedContent = this.formatSimpleContent(content.content || '');

                }

                

                dom.contentText.innerHTML = renderedContent;

            } catch (error) {

                console.error('内容渲染失败:', error);

                // 降级到简单文本渲染

                dom.contentText.innerHTML = this.formatSimpleContent(content.content || '');

            }

        }



        // 设置页面标题

        const textContent = dom.contentText?.textContent || dom.contentText?.innerText || '';

        const title = textContent.length <= 20 ? textContent : textContent.substring(0, 20) + '...';

        document.title = title || '当记 - 内容详情';



        // 处理图片

        this.renderImages(content);



        // 处理标签

        this.renderTags(content);



        // 显示内容区域

        dom.contentLoading?.classList.add('hidden');

        dom.contentError?.classList.add('hidden');

        dom.contentMain?.classList.remove('hidden');

    }



    /**

     * 渲染图片

     */

    renderImages(content) {

        const imageUrls = this.getImageUrls(content);



        if (imageUrls.length > 0 && dom.contentImageContainer && dom.contentImages) {

            dom.contentImageContainer.classList.remove('hidden');

            dom.contentImages.innerHTML = '';



            imageUrls.forEach((imageUrl, index) => {

                const img = document.createElement('img');

                img.src = imageUrl;

                img.alt = `内容图片 ${index + 1}`;

                img.className = 'memo-image w-full';

                img.onclick = () => this.showImageModal(imageUrl);



                dom.contentImages.appendChild(img);

            });

        } else if (dom.contentImageContainer) {

            dom.contentImageContainer.classList.add('hidden');

        }

    }



    /**

     * 渲染标签

     */

    renderTags(content) {

        const tags = content.tags || [];



        if (tags.length > 0 && dom.contentTags && dom.tagsList) {

            dom.contentTags.classList.remove('hidden');

            dom.tagsList.innerHTML = tags.map(tag =>

                `<span class="memo-tag">#${tag}</span>`

            ).join('');

        } else if (dom.contentTags) {

            dom.contentTags.classList.add('hidden');

        }

    }



    /**

     * 获取图片URL数组

     */

    getImageUrls(content) {

        const imageUrl = content.pic_url || content.picurls || content.images || content.图片;

        const urls = [];



        if (imageUrl && typeof imageUrl === 'string' && imageUrl.trim() !== '') {

            urls.push(...imageUrl.split(',').map(url => url.trim()).filter(url => url));

        } else if (Array.isArray(imageUrl)) {

            urls.push(...imageUrl.map(img => typeof img === 'string' ? img : img.url).filter(url => url));

        }



        return urls.filter(url => {

            try {

                const urlObj = new URL(url);

                return ['http:', 'https:'].includes(urlObj.protocol);

            } catch {

                return false;

            }

        });

    }



    /**

     * 显示图片模态框

     */

    showImageModal(imageUrl) {

        const overlay = document.createElement('div');

        overlay.className = 'fixed inset-0 bg-black bg-opacity-75 z-50 flex items-center justify-center p-4';

        overlay.style.backdropFilter = 'blur(4px)';



        const img = document.createElement('img');

        img.src = imageUrl;

        img.className = 'max-w-full max-h-full object-contain rounded-lg';

        img.style.boxShadow = '0 25px 50px rgba(0, 0, 0, 0.5)';



        overlay.appendChild(img);

        overlay.onclick = () => document.body.removeChild(overlay);



        document.body.appendChild(overlay);

    }



    /**

     * 返回上一页

     */

    goBack() {

        if (window.history.length > 1 && document.referrer && new URL(document.referrer).origin === window.location.origin) {

            window.history.back();

        } else {

            window.location.href = 'index.html';

        }

    }



    /**

     * 分享内容

     */

    async shareContent() {

        if (!this.currentContent) return;



        const shareData = {

            title: document.title,

            text: this.currentContent.content.substring(0, 100) + (this.currentContent.content.length > 100 ? '...' : ''),

            url: window.location.href

        };



        if (navigator.share) {

            try {

                await navigator.share(shareData);

                showNotification('分享成功', 'success');

            } catch (err) {

                if (err.name !== 'AbortError') {

                    console.error('分享失败:', err);

                    showNotification('分享失败', 'error');

                }

            }

        } else {

            // 降级方案：复制链接

            const success = await this.copyToClipboard(window.location.href);

            if (success) {

                showNotification('链接已复制到剪贴板', 'success');

            } else {

                showNotification('复制失败', 'error');

            }

        }

    }



    /**

     * 复制链接

     */

    async copyLink() {

        const success = await this.copyToClipboard(window.location.href);

        if (success) {

            showNotification('链接已复制', 'success');

        } else {

            showNotification('复制失败', 'error');

        }

    }



    /**

     * 复制到剪贴板

     */

    async copyToClipboard(text) {

        try {

            await navigator.clipboard.writeText(text);

            return true;

        } catch (err) {

            // 降级方案

            const textArea = document.createElement('textarea');

            textArea.value = text;

            document.body.appendChild(textArea);

            textArea.select();

            const success = document.execCommand('copy');

            document.body.removeChild(textArea);

            return success;

        }

    }



    /**

     * 生成卡片

     */

    async generateCard() {

        if (!this.currentContent) return;



        try {

            this.showCardModal();



            if (dom.cardPreview) {

                dom.cardPreview.innerHTML = '<span class="text-gray-500 dark:text-gray-400">生成中...</span>';

            }



            // 动态加载 QRCode 库

            if (typeof QRCode === 'undefined') {

                await this.loadScript('https://cdn.jsdelivr.net/npm/qrcodejs@1.0.0/qrcode.min.js');

            }



            // 动态加载 html2canvas 库

            if (typeof html2canvas === 'undefined') {

                await this.loadScript('https://cdn.jsdelivr.net/npm/html2canvas@1.4.1/dist/html2canvas.min.js');

            }



            // 获取图片URL

            const imageUrls = this.getImageUrls(this.currentContent);

            const finalImageUrl = imageUrls.length > 0 ? imageUrls[0] : null;



            // 生成卡片图片

            const cardImageUrl = await this.generateCardImage(

                this.currentContent.content,

                finalImageUrl,

                window.location.href

            );



            // 显示生成的卡片

            if (dom.cardPreview) {

                const img = document.createElement('img');

                img.src = cardImageUrl;

                img.alt = '分享卡片';

                img.className = 'max-w-full max-h-[60vh] rounded-lg';

                dom.cardPreview.innerHTML = '';

                dom.cardPreview.appendChild(img);

            }



            // 设置下载链接

            if (dom.downloadCardBtn) {

                dom.downloadCardBtn.onclick = () => {

                    const link = document.createElement('a');

                    link.href = cardImageUrl;

                    link.download = '当记卡片.png';

                    link.click();

                };

            }



        } catch (error) {

            console.error('生成卡片失败:', error);

            if (dom.cardPreview) {

                dom.cardPreview.innerHTML = '<span class="text-red-500">生成卡片失败</span>';

            }

            showNotification('生成卡片失败', 'error');

        }

    }



    /**

     * 生成卡片图片

     */

    async generateCardImage(content, imageUrl, url) {

        // 创建临时卡片元素

        const cardElement = document.createElement('div');

        cardElement.style.cssText = `

            width: 600px;

            padding: 2rem;

            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

            color: white;

            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

            border-radius: 1rem;

            box-shadow: 0 20px 40px rgba(0,0,0,0.1);

        `;



        // 添加内容

        cardElement.innerHTML = `

            <div style="background: rgba(255,255,255,0.1); backdrop-filter: blur(10px); 

                 border-radius: 0.75rem; padding: 1.5rem; min-height: 300px;

                 display: flex; flex-direction: column; justify-content: space-between;">

                <div>

                    <h2 style="font-size: 1.5rem; font-weight: bold; margin-bottom: 1rem; 

                        text-align: center; color: white;">

                        当记

                    </h2>

                    <div style="font-size: 1rem; line-height: 1.6; color: rgba(255,255,255,0.9);

                         max-height: 200px; overflow: hidden; text-overflow: ellipsis;

                         display: -webkit-box; -webkit-line-clamp: 8; -webkit-box-orient: vertical;">

                        ${content.substring(0, 200)}${content.length > 200 ? '...' : ''}

                    </div>

                </div>

                ${imageUrl ? `

                <div style="margin: 1rem 0; text-align: center;">

                    <img src="${imageUrl}" style="max-width: 100%; max-height: 150px; 

                         border-radius: 0.5rem; object-fit: cover;" 

                         onerror="this.style.display='none'">

                </div>

                ` : ''}

                <div style="display: flex; justify-content: space-between; align-items: center; 

                     margin-top: 1rem; padding-top: 1rem; border-top: 1px solid rgba(255,255,255,0.2);">

                    <div style="font-size: 0.875rem; color: rgba(255,255,255,0.8);">

                        扫码查看原文

                    </div>

                    <div id="qrcode-container" style="width: 80px; height: 80px; background: white; 

                         padding: 0.5rem; border-radius: 0.5rem;"></div>

                </div>

            </div>

        `;



        // 添加到DOM中但隐藏

        cardElement.style.position = 'absolute';

        cardElement.style.left = '-9999px';

        cardElement.style.top = '-9999px';

        document.body.appendChild(cardElement);



        try {

            // 生成二维码

            const qrCodeContainer = cardElement.querySelector('#qrcode-container');

            if (qrCodeContainer && typeof QRCode !== 'undefined') {

                new QRCode(qrCodeContainer, {

                    text: url,

                    width: 70,

                    height: 70,

                    colorDark: "#000000",

                    colorLight: "#ffffff",

                    correctLevel: QRCode.CorrectLevel.H

                });

            }



            // 等待一帧确保二维码渲染完成

            await new Promise(resolve => setTimeout(resolve, 100));



            // 使用 html2canvas 生成图片

            const canvas = await html2canvas(cardElement, {

                backgroundColor: null,

                scale: 2,

                useCORS: true

            });



            // 移除临时元素

            document.body.removeChild(cardElement);



            // 返回图片数据URL

            return canvas.toDataURL('image/png');

        } catch (error) {

            // 移除临时元素

            document.body.removeChild(cardElement);

            throw error;

        }

    }



    /**

     * 显示卡片模态窗

     */

    showCardModal() {

        dom.cardModal?.classList.remove('hidden');

        dom.cardModal?.classList.add('show');

    }



    /**

     * 关闭卡片模态窗

     */

    closeCardModal() {

        dom.cardModal?.classList.remove('show');

        dom.cardModal?.classList.add('hidden');



        // 重置预览区域

        if (dom.cardPreview) {

            dom.cardPreview.innerHTML = '<span class="text-gray-500 dark:text-gray-400">生成中...</span>';

        }

    }



    /**

     * 等待外部脚本加载完成

     */

    async waitForScripts() {

        const maxWaitTime = 5000; // 5秒超时

        const checkInterval = 100; // 100ms检查一次

        let waitedTime = 0;



        while (waitedTime < maxWaitTime) {

            // 检查markdown-it是否已加载

            if (typeof window.markdownit !== 'undefined') {

                console.log('外部脚本已加载完成');

                return;

            }

            

            // 等待一段时间后再次检查

            await new Promise(resolve => setTimeout(resolve, checkInterval));

            waitedTime += checkInterval;

        }



        // 超时后尝试从CDN加载

        console.warn('本地脚本加载超时，尝试从CDN加载markdown-it');

        await this.loadMarkdownItFromCDN();

    }



    /**

     * 从CDN加载markdown-it

     */

    async loadMarkdownItFromCDN() {

        return new Promise((resolve, reject) => {

            const script = document.createElement('script');

            script.src = 'https://cdn.jsdelivr.net/npm/markdown-it@13.0.1/dist/markdown-it.min.js';

            script.onload = () => {

                console.log('markdown-it从CDN加载成功');

                resolve();

            };

            script.onerror = () => {

                console.error('markdown-it从CDN加载失败');

                reject(new Error('markdown-it加载失败'));

            };

            document.head.appendChild(script);

        });

    }



    /**

     * 简单文本格式化函数

     */

    formatSimpleContent(content) {

        if (!content) return '';



        return content

            .replace(/\n/g, '<br>')

            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')

            .replace(/\*(.*?)\*/g, '<em>$1</em>')

            .replace(/`(.*?)`/g, '<code>$1</code>')

            .replace(/(https?:\/\/[^\s]+)/g, (url) => {

                // 对长链接进行截断处理

                let displayUrl = url;

                if (url.length > 50) {

                    displayUrl = url.substring(0, 50) + '...';

                }

                return `<a href="${url}" target="_blank" class="memo-link" title="${url}">${displayUrl}</a>`;

            });

    }



    /**

     * 转义HTML特殊字符

     */

    escapeHtml(text) {

        const map = {

            '&': '&amp;',

            '<': '&lt;',

            '>': '&gt;',

            '"': '&quot;',

            "'": '&#039;'

        };

        

        return text.replace(/[&<>"']/g, function(m) { return map[m]; });

    }



    /**

     * 动态加载脚本

     */

    loadScript(src) {

        return new Promise((resolve, reject) => {

            const script = document.createElement('script');

            script.src = src;

            script.onload = resolve;

            script.onerror = reject;

            document.head.appendChild(script);

        });

    }

}



// 创建全局实例

const contentApp = new ContentApp();



// 导出到全局

window.contentApp = contentApp;



export default contentApp;