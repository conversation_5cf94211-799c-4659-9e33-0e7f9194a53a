/**
 * 时间记录模块样式
 * 提供现代化、响应式的用户界面
 */

.simple-time-record {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 计时器区域 */
.timer-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  padding: 30px;
  text-align: center;
  margin-bottom: 24px;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
}

.timer-display {
  margin-bottom: 24px;
}

.timer-display .duration {
  font-size: 3.5rem;
  font-weight: 300;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  font-variant-numeric: tabular-nums;
  letter-spacing: 0.05em;
}

.timer-controls {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
}

.timer-controls button {
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  min-width: 100px;
}

.timer-controls button:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.timer-controls button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* 输入区域 */
.input-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e5e9;
}

.input-section textarea {
  width: 100%;
  min-height: 120px;
  padding: 16px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 1rem;
  line-height: 1.5;
  resize: vertical;
  font-family: inherit;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.input-section textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.input-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
  flex-wrap: wrap;
  gap: 12px;
}

.input-info span {
  color: #6b7280;
  font-size: 0.875rem;
}

.input-info button {
  background: #667eea;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.input-info button:hover:not(:disabled) {
  background: #5a67d8;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.input-info button:disabled {
  background: #d1d5db;
  cursor: not-allowed;
  transform: none;
}

/* 错误提示 */
.error-message {
  background: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 16px;
  font-size: 0.875rem;
}

/* 快捷键提示 */
.shortcuts-hint {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 24px;
  text-align: center;
  font-size: 0.875rem;
  color: #64748b;
}

.shortcuts-hint kbd {
  background: #e2e8f0;
  border: 1px solid #cbd5e1;
  border-radius: 4px;
  padding: 2px 6px;
  font-size: 0.75rem;
  font-family: monospace;
  margin: 0 2px;
}

/* 历史记录区域 */
.records-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e5e9;
}

.records-section h3 {
  margin: 0 0 20px 0;
  color: #1f2937;
  font-size: 1.25rem;
  font-weight: 600;
}

.loading {
  text-align: center;
  padding: 40px;
  color: #6b7280;
  font-size: 0.875rem;
}

.empty {
  text-align: center;
  padding: 40px;
  color: #9ca3af;
  font-size: 0.875rem;
}

.records-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.record-item {
  background: #f9fafb;
  border: 1px solid #f3f4f6;
  border-radius: 8px;
  padding: 16px;
  transition: all 0.3s ease;
}

.record-item:hover {
  background: #f3f4f6;
  border-color: #e5e7eb;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.record-time {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 0.875rem;
  color: #6b7280;
  flex-wrap: wrap;
  gap: 8px;
}

.record-time .duration {
  background: #667eea;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: 500;
  font-size: 0.75rem;
}

.record-content {
  color: #374151;
  line-height: 1.5;
  word-wrap: break-word;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .simple-time-record {
    padding: 16px;
  }
  
  .timer-section {
    padding: 20px;
  }
  
  .timer-display .duration {
    font-size: 2.5rem;
  }
  
  .timer-controls {
    flex-direction: column;
    align-items: center;
  }
  
  .timer-controls button {
    width: 100%;
    max-width: 200px;
  }
  
  .input-section,
  .records-section {
    padding: 16px;
  }
  
  .input-info {
    flex-direction: column;
    align-items: stretch;
  }
  
  .input-info button {
    width: 100%;
  }
  
  .record-time {
    flex-direction: column;
    align-items: flex-start;
  }
}

@media (max-width: 480px) {
  .timer-display .duration {
    font-size: 2rem;
  }
  
  .shortcuts-hint {
    font-size: 0.75rem;
  }
  
  .shortcuts-hint kbd {
    font-size: 0.625rem;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.record-item {
  animation: fadeIn 0.3s ease-out;
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .simple-time-record {
    color: #f9fafb;
  }
  
  .input-section,
  .records-section {
    background: #1f2937;
    border-color: #374151;
  }
  
  .input-section textarea {
    background: #374151;
    border-color: #4b5563;
    color: #f9fafb;
  }
  
  .input-section textarea:focus {
    border-color: #667eea;
  }
  
  .records-section h3 {
    color: #f9fafb;
  }
  
  .record-item {
    background: #374151;
    border-color: #4b5563;
  }
  
  .record-item:hover {
    background: #4b5563;
    border-color: #6b7280;
  }
  
  .record-content {
    color: #e5e7eb;
  }
  
  .shortcuts-hint {
    background: #374151;
    border-color: #4b5563;
    color: #9ca3af;
  }
  
  .shortcuts-hint kbd {
    background: #4b5563;
    border-color: #6b7280;
    color: #e5e7eb;
  }
}