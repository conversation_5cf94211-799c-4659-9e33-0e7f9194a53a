/**

 * 组件加载器 - 用于动态加载HTML组件

 */

export class ComponentLoader {

    constructor() {

        this.cache = new Map();

    }



    /**

     * 加载单个组件

     * @param {string} componentPath - 组件文件路径

     * @returns {Promise<string>} 组件HTML内容

     */

    async loadComponent(componentPath) {

        if (this.cache.has(componentPath)) {

            return this.cache.get(componentPath);

        }



        try {

            const response = await fetch(componentPath);

            if (!response.ok) {

                throw new Error(`Failed to load component: ${componentPath}`);

            }

            const html = await response.text();

            this.cache.set(componentPath, html);

            return html;

        } catch (error) {

            console.error('Error loading component:', error);

            return '';

        }

    }



    /**

     * 将组件插入到指定容器

     * @param {string} containerId - 容器元素ID

     * @param {string} componentPath - 组件文件路径

     */

    async insertComponent(containerId, componentPath) {

        const container = document.getElementById(containerId);

        if (!container) {

            console.error(`Container not found: ${containerId}`);

            return;

        }



        const html = await this.loadComponent(componentPath);

        container.innerHTML = html;

    }



    /**

     * 批量加载组件

     * @param {Array} components - 组件配置数组 [{containerId, componentPath}]

     */

    async loadComponents(components) {

        const promises = components.map(({ containerId, componentPath }) =>

            this.insertComponent(containerId, componentPath)

        );

        await Promise.all(promises);

    }

}



// 创建全局实例

export const componentLoader = new ComponentLoader();