/**
 * 备忘录模块 - Vika API版本
 * 专注于与Vika API的交互，提供备忘录的增删改查功能
 */

import { generateId, formatDate, showNotification } from './utils.js';
import { APIService } from './api.js';

class Memo {
    constructor(data = {}) {
        this.id = data.id || generateId('memo');
        this.content = data.content || '';
        this.status = data.status || '公开'; // 公开, 私密
        this.tags = data.tags || [];
        this.attachments = data.attachments || []; // 文件附件
        this.createdAt = data.createdAt ? new Date(data.createdAt) : new Date();
        this.updatedAt = data.updatedAt ? new Date(data.updatedAt) : new Date();
        this.author = data.author || null;
        this.likes = data.likes || 0;
        this.comments = data.comments || [];
        this.isMarkdown = data.isMarkdown || false;
        this.metadata = data.metadata || {};
    }

    /**
     * 添加附件
     */
    addAttachment(file) {
        const attachment = {
            id: generateId('attachment'),
            name: file.name,
            size: file.size,
            type: file.type,
            url: file.url || URL.createObjectURL(file),
            uploadedAt: new Date()
        };
        this.attachments.push(attachment);
        this.updatedAt = new Date();
        return attachment;
    }

    /**
     * 获取格式化的创建时间
     */
    getFormattedDate() {
        return formatDate(this.createdAt);
    }

    /**
     * 检查是否包含图片
     */
    hasImages() {
        return this.attachments.some(att => att.type.startsWith('image/'));
    }

    /**
     * 检查是否包含视频
     */
    hasVideos() {
        return this.attachments.some(att => att.type.startsWith('video/'));
    }
}

class MemoManager {
    constructor() {
        this.memos = [];
        this.listeners = new Set();
        this.currentPage = 1;
        this.pageSize = 10;
        this.filters = {
            status: 'all', // all, 公开, 私密
            tags: [],
            search: ''
        };
        // 添加用户缓存
        this.userCache = new Map();
        this.userCacheExpiry = 5 * 60 * 1000; // 5分钟缓存
    }

    /**
     * 从Vika API加载备忘录
     */
    async loadMemos(page = 1, pageSize = this.pageSize) {
        try {
            const result = await APIService.getMemos(page, pageSize);
            
            if (result && result.data && result.data.records) {
                // 收集所有唯一的用户ID
                const userIds = [...new Set(
                    result.data.records
                        .filter(record => record.fields.user_id && record.fields.user_id.length > 0)
                        .map(record => record.fields.user_id[0])
                )];
                
                // 批量获取用户信息，避免频繁API调用
                const userCache = {};
                if (userIds.length > 0) {
                    try {
                        // 获取所有用户信息（一次性调用）
                        const usersResult = await APIService.getUsers(100);
                        if (usersResult && usersResult.data && usersResult.data.records) {
                            usersResult.data.records.forEach(userRecord => {
                                if (userIds.includes(userRecord.recordId)) {
                                    const userData = userRecord.fields;
                                    userCache[userRecord.recordId] = {
                                        username: userData.用户名 || '用户',
                                        nickname: userData.昵称 || userData.用户名 || '用户',
                                        id: userRecord.recordId,
                                        avatar: userData.头像 || `https://ui-avatars.com/api/?name=${encodeURIComponent(userData.昵称 || userData.用户名 || '用户')}&background=3B82F6&color=fff`
                                    };
                                }
                            });
                        }
                    } catch (userError) {
                        console.warn('批量获取用户信息失败:', userError);
                    }
                }
                
                // 处理备忘录记录
                this.memos = result.data.records.map(record => {
                    // 处理关联字段 user_id
                    let author = { username: '匿名用户', nickname: '匿名用户', id: null, avatar: '' };
                    
                    if (record.fields.user_id && record.fields.user_id.length > 0) {
                        const userId = record.fields.user_id[0];
                        // 从缓存中获取用户信息
                        if (userCache[userId]) {
                            author = userCache[userId];
                        }
                    }
                    
                    return new Memo({
                        id: record.recordId,
                        content: record.fields.content || '',
                        tags: record.fields.tags || [],
                        // 使用正确的时间字段名
                        createdAt: record.fields.posttime ? new Date(record.fields.posttime) : (record.fields.createdAt ? new Date(record.fields.createdAt) : new Date(record.createdTime)),
                        author: author,
                        // 处理图片字段 picurls（逗号分隔的URL字符串）
                        attachments: (() => {
                            const picurls = record.fields.picurls;
                            if (picurls && typeof picurls === 'string') {
                                // 将逗号分隔的URL字符串转换为数组
                                return picurls.split(',').filter(url => url.trim()).map(url => ({
                                    id: generateId('attachment'),
                                    name: 'image',
                                    type: 'image/webp',
                                    url: url.trim(),
                                    uploadedAt: new Date()
                                }));
                            }
                            // 回退到其他可能的字段名
                            const otherImages = record.fields.images || record.fields.图片 || record.fields.attachments || [];
                            return otherImages.map(img => ({
                                id: generateId('attachment'),
                                name: 'image',
                                type: 'image/jpeg',
                                url: typeof img === 'string' ? img : (img.url || img),
                                uploadedAt: new Date()
                            }));
                        })(),
                        status: record.fields.status || '公开',
                        metadata: {
                            vikaId: record.recordId,
                            vikaId_num: record.fields.id // 保存数字ID
                        }
                    });
                });
                
                return {
                    memos: this.memos,
                    currentPage: page,
                    totalPages: Math.ceil((result.data.total || 0) / pageSize),
                    totalCount: result.data.total || 0,
                    hasMore: result.data.records.length === pageSize && this.memos.length < (result.data.total || 0)
                };
            }
        } catch (error) {
            console.error('从Vika API加载数据失败:', error);
            
            // 根据错误类型显示不同的提示
            if (error.message.includes('401')) {
                showNotification('❌ API认证失败，请检查token配置', 'error', 5000);
            } else if (error.message.includes('403')) {
                showNotification('❌ API权限不足，请检查数据表权限', 'error', 5000);
            } else if (error.message.includes('404')) {
                showNotification('❌ 数据表不存在，请检查datasheetId', 'error', 5000);
            } else {
                showNotification('❌ 网络连接失败，请检查网络状态', 'error', 5000);
            }
            
            throw error;
        }
    }

    /**
     * 创建新备忘录
     */
    async createMemo(data) {
        try {
            // 获取当前用户信息
            const currentUser = this.getCurrentUser();
            
            const now = new Date().toISOString();
            
            const apiResult = await APIService.createMemo({
                content: data.content,
                images: data.attachments ? data.attachments.filter(att => att.type.startsWith('image/')).map(att => att.url) : [],
                tags: data.tags || [],
                userId: currentUser ? currentUser.id : null, // 使用关联字段
                status: data.status || '公开', // 添加状态字段
                posttime: now // 添加发布时间字段
            });
            
            if (apiResult && apiResult.data && apiResult.data.records && apiResult.data.records[0]) {
                const newRecord = apiResult.data.records[0];
                const memo = new Memo({
                    id: newRecord.recordId,
                    content: newRecord.fields.content || '',
                    tags: newRecord.fields.tags || [],
                    createdAt: newRecord.fields.posttime || newRecord.fields.createdAt || newRecord.createdTime,
                    status: newRecord.fields.status || '公开',
                    // 处理 picurls 字段 - 可能是逗号分隔的字符串
                    attachments: (() => {
                        const picurls = newRecord.fields.picurls;
                        if (picurls && typeof picurls === 'string') {
                            return picurls.split(',').filter(url => url.trim()).map(url => ({
                                id: generateId('attachment'),
                                name: 'image',
                                type: 'image/webp',
                                url: url.trim(),
                                uploadedAt: new Date()
                            }));
                        }
                        return [];
                    })(),
                    metadata: {
                        vikaId: newRecord.recordId
                    }
                });
                
                this.memos.unshift(memo);
                this.notify('create', memo);
                showNotification('✅ 备忘录创建成功！', 'success', 2000);
                return memo;
            }
        } catch (error) {
            console.error('创建备忘录失败:', error);
            
            if (error.message.includes('401')) {
                showNotification('❌ API认证失败，无法创建备忘录', 'error', 5000);
            } else if (error.message.includes('403')) {
                showNotification('❌ API权限不足，无法创建备忘录', 'error', 5000);
            } else {
                showNotification('❌ 创建备忘录失败，请检查网络连接', 'error', 3000);
            }
            
            throw error;
        }
    }

    /**
     * 删除备忘录
     */
    async deleteMemo(id) {
        try {
            await APIService.deleteMemo(id);
            
            const index = this.memos.findIndex(memo => memo.id === id);
            if (index !== -1) {
                const deleted = this.memos.splice(index, 1)[0];
                this.notify('delete', deleted);
                showNotification('✅ 备忘录删除成功！', 'success', 2000);
                return deleted;
            }
        } catch (error) {
            console.error('删除备忘录失败:', error);
            
            if (error.message.includes('401')) {
                showNotification('❌ API认证失败，无法删除备忘录', 'error', 5000);
            } else if (error.message.includes('403')) {
                showNotification('❌ API权限不足，无法删除备忘录', 'error', 5000);
            } else {
                showNotification('❌ 删除备忘录失败，请检查网络连接', 'error', 3000);
            }
            
            throw error;
        }
    }

    /**
     * 获取单个备忘录
     */
    getMemo(id) {
        return this.memos.find(memo => memo.id === id);
    }

    /**
     * 获取分页数据
     */
    getPaginatedMemos(page = 1, pageSize = this.pageSize) {
        const startIndex = (page - 1) * pageSize;
        const endIndex = startIndex + pageSize;
        
        return {
            memos: this.memos.slice(startIndex, endIndex),
            currentPage: page,
            totalPages: Math.ceil(this.memos.length / pageSize),
            totalCount: this.memos.length,
            hasMore: endIndex < this.memos.length
        };
    }

    // 事件监听系统
    addListener(callback) {
        this.listeners.add(callback);
    }

    removeListener(callback) {
        this.listeners.delete(callback);
    }

    notify(action, data = null) {
        this.listeners.forEach(callback => {
            try {
                callback(action, data);
            } catch (error) {
                console.error('事件监听器错误:', error);
            }
        });
    }

    /**
     * 加载更多备忘录（分页）
     */
    async loadMoreMemos(page, pageSize = this.pageSize) {
        try {
            const result = await APIService.getMemos(page, pageSize);
            
            if (result && result.data && result.data.records) {
                // 收集所有唯一的用户ID
                const userIds = [...new Set(
                    result.data.records
                        .filter(record => record.fields.user_id && record.fields.user_id.length > 0)
                        .map(record => record.fields.user_id[0])
                )];
                
                // 批量获取用户信息，避免频繁API调用
                const userCache = {};
                if (userIds.length > 0) {
                    try {
                        // 获取所有用户信息（一次性调用）
                        const usersResult = await APIService.getUsers(100);
                        if (usersResult && usersResult.data && usersResult.data.records) {
                            usersResult.data.records.forEach(userRecord => {
                                if (userIds.includes(userRecord.recordId)) {
                                    const userData = userRecord.fields;
                                    userCache[userRecord.recordId] = {
                                        username: userData.用户名 || '用户',
                                        nickname: userData.昵称 || userData.用户名 || '用户',
                                        id: userRecord.recordId,
                                        avatar: userData.头像 || `https://ui-avatars.com/api/?name=${encodeURIComponent(userData.昵称 || userData.用户名 || '用户')}&background=3B82F6&color=fff`
                                    };
                                }
                            });
                        }
                    } catch (userError) {
                        console.warn('批量获取用户信息失败:', userError);
                    }
                }
                
                // 处理新的备忘录记录
                const newMemos = result.data.records.map(record => {
                    // 处理关联字段 user_id
                    let author = { username: '匿名用户', nickname: '匿名用户', id: null, avatar: '' };
                    
                    if (record.fields.user_id && record.fields.user_id.length > 0) {
                        const userId = record.fields.user_id[0];
                        // 从缓存中获取用户信息
                        if (userCache[userId]) {
                            author = userCache[userId];
                        }
                    }
                    
                    return new Memo({
                        id: record.recordId,
                        content: record.fields.content || '',
                        tags: record.fields.tags || [],
                        // 使用正确的时间字段名
                        createdAt: record.fields.posttime ? new Date(record.fields.posttime) : (record.fields.createdAt ? new Date(record.fields.createdAt) : new Date(record.createdTime)),
                        author: author,
                        // 处理图片字段 picurls（逗号分隔的URL字符串）
                        attachments: (() => {
                            const picurls = record.fields.picurls;
                            if (picurls && typeof picurls === 'string') {
                                // 将逗号分隔的URL字符串转换为数组
                                return picurls.split(',').filter(url => url.trim()).map(url => ({
                                    id: generateId('attachment'),
                                    name: 'image',
                                    type: 'image/webp',
                                    url: url.trim(),
                                    uploadedAt: new Date()
                                }));
                            }
                            // 回退到其他可能的字段名
                            const otherImages = record.fields.images || record.fields.图片 || record.fields.attachments || [];
                            return otherImages.map(img => ({
                                id: generateId('attachment'),
                                name: 'image',
                                type: 'image/jpeg',
                                url: typeof img === 'string' ? img : (img.url || img),
                                uploadedAt: new Date()
                            }));
                        })(),
                        status: record.fields.status || '公开',
                        metadata: {
                            vikaId: record.recordId,
                            vikaId_num: record.fields.id // 保存数字ID
                        }
                    });
                });
                
                // 将新的备忘录添加到现有列表中
                this.memos = [...this.memos, ...newMemos];
                
                return {
                    memos: newMemos, // 只返回新加载的备忘录
                    currentPage: page,
                    totalPages: Math.ceil((result.data.total || 0) / pageSize),
                    totalCount: result.data.total || 0,
                    hasMore: newMemos.length === pageSize
                };
            }
        } catch (error) {
            console.error('加载更多备忘录失败:', error);
            
            // 根据错误类型显示不同的提示
            if (error.message.includes('401')) {
                showNotification('❌ API认证失败，请检查token配置', 'error', 5000);
            } else if (error.message.includes('403')) {
                showNotification('❌ API权限不足，请检查数据表权限', 'error', 5000);
            } else if (error.message.includes('404')) {
                showNotification('❌ 数据表不存在，请检查datasheetId', 'error', 5000);
            } else {
                showNotification('❌ 网络连接失败，请检查网络状态', 'error', 5000);
            }
            
            throw error;
        }
    }

    /**
     * 获取缓存的用户信息
     */
    getCachedUser(userId) {
        const cached = this.userCache.get(userId);
        if (cached && (Date.now() - cached.timestamp) < this.userCacheExpiry) {
            return cached.data;
        }
        return null;
    }

    /**
     * 设置用户信息缓存
     */
    setCachedUser(userId, userData) {
        this.userCache.set(userId, {
            data: userData,
            timestamp: Date.now()
        });
    }

    /**
     * 清除过期的用户缓存
     */
    clearExpiredUserCache() {
        const now = Date.now();
        for (const [userId, cached] of this.userCache.entries()) {
            if ((now - cached.timestamp) >= this.userCacheExpiry) {
                this.userCache.delete(userId);
            }
        }
    }

    /**
     * 获取当前用户（从localStorage）
     */
    getCurrentUser() {
        try {
            const userStr = localStorage.getItem('currentUser');
            return userStr ? JSON.parse(userStr) : null;
        } catch {
            return null;
        }
    }
}

// 导出模块
export { Memo, MemoManager };