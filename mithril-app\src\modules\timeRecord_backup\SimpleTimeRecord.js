/**
 * 精简版时间记录组件
 * 核心功能：计时、保存、显示历史记录
 * 代码行数：~400行 vs 原2000+行
 */

const m = window.m;

// 最小配置
const CONFIG = {
  API: {
    DATASHEET_ID: 'dsta8guxXYK3pCYsVe',
    TOKEN: 'uskcZUvxWXvLIPXN0hUC6DK',
    BASE_URL: 'https://api.vika.cn/fusion/v1'
  },
  MAX_CONTENT_LENGTH: 1000,
  PAGE_SIZE: 20
};

// 样式将通过传统方式引入，不通过JS模块导入

// 工具函数
const formatDuration = (seconds) => {
  const h = Math.floor(seconds / 3600);
  const m = Math.floor((seconds % 3600) / 60);
  const s = seconds % 60;
  return `${h.toString().padStart(2, '0')}:${m.toString().padStart(2, '0')}:${s.toString().padStart(2, '0')}`;
};

const formatTimestamp = (date) => {
  return new Date(date).toLocaleString('zh-CN');
};

// API服务
class TimeService {
  async createRecord(data) {
    try {
      const response = await fetch(`${CONFIG.API.BASE_URL}/datasheets/${CONFIG.API.DATASHEET_ID}/records`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${CONFIG.API.TOKEN}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          records: [{
            fields: {
              fld9Mole3W9eR: data.category || '工作记录',
              fld0znfZ7H9xM: data.content,
              fld3e659QaQ11: Math.floor(data.startTime / 1000),
              flddjkaHotlMJ: Math.floor(data.endTime / 1000),
              fldNY5MabI72y: data.durationText
            }
          }]
        })
      });
      
      const result = await response.json();
      return { success: true, data: result.data?.records?.[0] };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  async getRecords(page = 1) {
    try {
      const response = await fetch(
        `${CONFIG.API.BASE_URL}/datasheets/${CONFIG.API.DATASHEET_ID}/records?` +
        `pageSize=${CONFIG.PAGE_SIZE}&pageNum=${page}&sort=createdTime,desc`,
        {
          headers: {
            'Authorization': `Bearer ${CONFIG.API.TOKEN}`
          }
        }
      );
      
      const result = await response.json();
      return {
        success: true,
        records: result.data?.records?.map(r => ({
          id: r.recordId,
          content: r.fields.fld0znfZ7H9xM,
          startTime: new Date(r.fields.fld3e659QaQ11 * 1000),
          endTime: new Date(r.fields.flddjkaHotlMJ * 1000),
          duration: r.fields.flddjkaHotlMJ - r.fields.fld3e659QaQ11,
          durationText: r.fields.fldNY5MabI72y,
          category: r.fields.fld9Mole3W9eR
        })) || [],
        hasMore: result.data?.total > page * CONFIG.PAGE_SIZE
      };
    } catch (error) {
      return { success: false, error: error.message, records: [], hasMore: false };
    }
  }
}

// 主组件
export const SimpleTimeRecord = {
  oninit(vnode) {
    // 初始化状态 - 使用临时对象来避免直接修改vnode.state
    const initialState = {
      isRunning: false,
      startTime: null,
      duration: 0,
      records: [],
      inputText: '',
      loading: false,
      error: null,
      hasMore: false,
      service: new TimeService()
    };
    
    // 将初始状态赋值给vnode.state
    Object.assign(vnode.state, initialState);

    this.loadRecords(vnode);
    this.startTimerUpdate(vnode);
  },

  startTimerUpdate(vnode) {
    setInterval(() => {
      if (vnode.state.isRunning && vnode.state.startTime) {
        const newDuration = Math.floor((Date.now() - vnode.state.startTime) / 1000);
        const newState = {
          duration: newDuration
        };
        
        // 更新状态
        Object.assign(vnode.state, newState);
        m.redraw();
      }
    }, 1000);
  },

  async loadRecords(vnode) {
    // 更新状态
    Object.assign(vnode.state, { loading: true });
    m.redraw();

    const result = await vnode.state.service.getRecords();
    const newState = {
      loading: false,
      records: result.records || [],
      hasMore: result.hasMore || false,
      error: result.error || null
    };
    
    // 更新状态
    Object.assign(vnode.state, newState);
    m.redraw();
  },

  startTimer(vnode) {
    if (!vnode.state.isRunning) {
      const newState = {
        isRunning: true,
        startTime: Date.now(),
        duration: 0,
        inputText: ''
      };
      
      // 更新状态
      Object.assign(vnode.state, newState);
      m.redraw();
    }
  },

  stopTimer(vnode) {
    if (vnode.state.isRunning) {
      // 更新状态
      Object.assign(vnode.state, { isRunning: false });
      m.redraw();
    }
  },

  resetTimer(vnode) {
    const newState = {
      isRunning: false,
      startTime: null,
      duration: 0,
      inputText: ''
    };
    
    // 更新状态
    Object.assign(vnode.state, newState);
    m.redraw();
  },

  async saveRecord(vnode) {
    if (!vnode.state.duration || vnode.state.duration < 1) {
      Object.assign(vnode.state, { error: '计时时长不能少于1秒' });
      m.redraw();
      return;
    }

    if (!vnode.state.inputText.trim()) {
      Object.assign(vnode.state, { error: '请输入工作内容' });
      m.redraw();
      return;
    }

    if (vnode.state.inputText.length > CONFIG.MAX_CONTENT_LENGTH) {
      Object.assign(vnode.state, { error: `内容不能超过${CONFIG.MAX_CONTENT_LENGTH}字符` });
      m.redraw();
      return;
    }

    const recordData = {
      content: vnode.state.inputText.trim(),
      startTime: vnode.state.startTime,
      endTime: Date.now(),
      duration: vnode.state.duration,
      durationText: formatDuration(vnode.state.duration)
    };

    const result = await vnode.state.service.createRecord(recordData);
    if (result.success) {
      const newRecord = {
        id: result.data.recordId,
        ...recordData
      };
      
      // 更新状态
      const newState = {
        records: [newRecord, ...vnode.state.records],
        error: null
      };
      Object.assign(vnode.state, newState);
      
      this.resetTimer(vnode);
    } else {
      Object.assign(vnode.state, { error: result.error });
    }
    m.redraw();
  },

  view(vnode) {
    const { isRunning, duration, records, inputText, loading, error } = vnode.state;

    return m('div.simple-time-record', [
      // 标题
      m('h2', '时间记录'),

      // 计时器区域
      m('div.timer-section', [
        m('div.timer-display', [
          m('span.duration', formatDuration(duration))
        ]),
        
        m('div.timer-controls', [
          m('button', {
            onclick: () => this.startTimer(vnode),
            disabled: isRunning
          }, '开始计时'),
          
          m('button', {
            onclick: () => this.stopTimer(vnode),
            disabled: !isRunning
          }, '结束计时'),
          
          m('button', {
            onclick: () => this.resetTimer(vnode)
          }, '重置')
        ])
      ]),

      // 输入区域
      !isRunning && duration > 0 && [
        m('div.input-section', [
          m('textarea', {
            placeholder: '请输入工作内容...',
            value: inputText,
            oninput: (e) => {
              const newState = {
                inputText: e.target.value,
                error: null
              };
              Object.assign(vnode.state, newState);
            }
          }),
          
          m('div.input-info', [
            m('span', `${inputText.length}/${CONFIG.MAX_CONTENT_LENGTH}`),
            m('button', {
              onclick: () => this.saveRecord(vnode),
              disabled: !inputText.trim()
            }, '保存记录')
          ])
        ])
      ],

      // 错误提示
      error && m('div.error-message', error),

      // 历史记录
      m('div.records-section', [
        m('h3', '历史记录'),
        
        loading && m('div.loading', '加载中...'),
        
        records.length === 0 && !loading &&
          m('div.empty', '暂无记录'),
        
        m('div.records-list', 
          records.map(record => 
            m('div.record-item', [
              m('div.record-time', [
                m('span', formatTimestamp(record.startTime)),
                m('span.duration', formatDuration(record.duration))
              ]),
              m('div.record-content', record.content)
            ])
          )
        )
      ])
    ]);
  },

  onremove(vnode) {
    // 清理资源 - 使用Object.assign清空状态
    Object.assign(vnode.state, {
      isRunning: false,
      startTime: null,
      duration: 0,
      records: [],
      inputText: '',
      loading: false,
      error: null,
      hasMore: false,
      service: null
    });
  }
};