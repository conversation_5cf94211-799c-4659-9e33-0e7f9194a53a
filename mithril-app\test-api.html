<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API连接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>API连接测试工具</h1>
        <p>用于诊断Vika API连接问题</p>
        
        <button onclick="testAllAPIs()">测试所有API</button>
        <button onclick="testTimeRecordAPI()">测试时间记录API</button>
        <button onclick="clearLog()">清空日志</button>
        
        <div id="results"></div>
        <div id="log" class="log"></div>
    </div>

    <script type="module">
        import { TimeRecordService } from './src/services/TimeRecordService.js';

        const resultsDiv = document.getElementById('results');
        const logDiv = document.getElementById('log');

        function addLog(message) {
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function addResult(service, success, message, responseTime = 0) {
            const div = document.createElement('div');
            div.className = `test-result ${success ? 'success' : 'error'}`;
            div.innerHTML = `
                <strong>${service}</strong>: ${success ? '✅ 成功' : '❌ 失败'} 
                ${responseTime > 0 ? `(${responseTime}ms)` : ''}<br>
                ${message}
            `;
            resultsDiv.appendChild(div);
        }

        async function testTimeRecordAPI() {
            addLog('开始测试时间记录API...');
            
            try {
                const service = new TimeRecordService();
                const startTime = Date.now();
                
                const result = await service.testConnection();
                const responseTime = Date.now() - startTime;
                
                if (result) {
                    addResult('时间记录API', true, '连接正常', responseTime);
                    addLog('时间记录API连接成功');
                } else {
                    addResult('时间记录API', false, '服务返回失败', responseTime);
                    addLog('时间记录API连接失败');
                }
            } catch (error) {
                addResult('时间记录API', false, error.message);
                addLog(`时间记录API异常: ${error.message}`);
            }
        }

        async function testAllAPIs() {
            resultsDiv.innerHTML = '';
            addLog('开始测试所有API...');
            
            await testTimeRecordAPI();
            // 这里可以添加其他API的测试
        }

        window.testAllAPIs = testAllAPIs;
        window.testTimeRecordAPI = testTimeRecordAPI;
        window.clearLog = () => {
            logDiv.innerHTML = '';
            resultsDiv.innerHTML = '';
        };

        // 页面加载时自动测试
        testAllAPIs();
    </script>
</body>
</html>