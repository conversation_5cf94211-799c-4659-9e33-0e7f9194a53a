/**

 * 优化版内容页应用

 * 解决脚本加载超时和渲染性能问题

 */



import { APIOptimizer } from './modules/apiOptimizer.js';

import { ContentOptimizer } from './modules/contentOptimizer.js';



class OptimizedContentApp {

    constructor() {

        this.apiOptimizer = new APIOptimizer();

        this.contentOptimizer = new ContentOptimizer();

        this.recordId = null;

        this.recordData = null;

        this.userInfo = null;

        this.startTime = Date.now();

        

        // 启动性能监控

        this.apiOptimizer.startPerformanceMonitoring();

        

        // 预加载内容资源

        this.contentOptimizer.preloadContentResources();

        

        // 注册到全局以便调试

        window.contentApp = this;

    }



    /**

     * 初始化应用

     */

    async init() {

        console.log('开始初始化优化版内容页应用...');

        

        try {

            // 从URL获取记录ID

            this.recordId = this.getRecordIdFromUrl();

            if (!this.recordId) {

                throw new Error('未找到记录ID');

            }



            // 记录页面浏览行为

            this.apiOptimizer.recordPageView(this.recordId);

            

            // 记录开始阅读时间

            this.readStartTime = Date.now();



            // 并行加载数据和准备渲染器

            const [recordData] = await Promise.all([

                this.loadRecordData(),

                this.contentOptimizer.createOptimizedMarkdownRenderer() // 预热渲染器

            ]);



            this.recordData = recordData;



            // 快速显示基础信息

            this.renderBasicInfo();



            // 异步加载用户信息和渲染内容

            this.loadUserInfoAndRenderContent();



            // 页面卸载时记录阅读时间

            this.setupReadingTimeTracking();



            console.log('优化版内容页应用初始化完成');



        } catch (error) {

            console.error('内容页应用初始化失败:', error);

            this.showError('页面加载失败，请刷新重试');

        }

    }



    /**

     * 从URL获取记录ID

     */

    getRecordIdFromUrl() {

        const urlParams = new URLSearchParams(window.location.search);

        return urlParams.get('id');

    }



    /**

     * 加载记录数据（优化版）

     */

    async loadRecordData() {

        const cacheKey = `record_${this.recordId}`;

        

        // 检查缓存

        const cachedData = this.apiOptimizer.getCache(cacheKey);

        if (cachedData) {

            console.log('使用缓存的记录数据');

            return cachedData;

        }



        console.log('从API加载记录数据...');

        

        const response = await fetch(`https://api.vika.cn/fusion/v1/datasheets/dstCo3t04QBY3RweuR/records/${this.recordId}?fieldKey=name`, {

            headers: {

                'Authorization': 'Bearer uskcZUvxWXvLIPXN0hUC6DK',

                'Content-Type': 'application/json'

                }

        });



        if (!response.ok) {

            throw new Error(`记录加载失败: ${response.status}`);

        }



        const data = await response.json();

        

        // 缓存数据

        this.apiOptimizer.setCache(cacheKey, data, 'record');

        

        return data;

    }



    /**

     * 快速渲染基础信息

     */

    renderBasicInfo() {

        if (!this.recordData || !this.recordData.data) {

            return;

        }



        const record = this.recordData.data;

        const fields = record.fields || {};



        // 更新页面标题

        document.title = (fields.标题 || '备忘录详情') + ' - 当记';



        // 快速显示内容框架

        const container = document.getElementById('content-container') || document.body;

        

        container.innerHTML = `

            <div class="content-page">

                <div class="content-header">

                    <h1 class="content-title">${fields.标题 || '无标题'}</h1>

                    <div class="content-meta">

                        <div class="author-info loading">

                            <div class="author-avatar skeleton"></div>

                            <div class="author-details">

                                <div class="author-name skeleton-text"></div>

                                <div class="post-time">${fields.posttime || ''}</div>

                            </div>

                        </div>

                    </div>

                </div>

                <div class="content-body">

                    <div id="content-text" class="content-loading">

                        <div class="loading-spinner"></div>

                        <span>正在加载内容...</span>

                    </div>

                </div>

            </div>

        `;



        console.log('基础信息渲染完成');

    }



    /**

     * 异步加载用户信息和渲染内容

     */

    async loadUserInfoAndRenderContent() {

        const record = this.recordData.data;

        const fields = record.fields || {};



        try {

            // 并行执行用户信息加载和内容渲染

            const promises = [];



            // 加载用户信息

            if (fields.用户) {

                promises.push(this.loadUserInfo(fields.用户));

            }



            // 渲染内容

            if (fields.内容) {

                promises.push(this.renderContent(fields.内容));

            }



            await Promise.all(promises);



        } catch (error) {

            console.error('异步加载失败:', error);

        }

    }



    /**

     * 加载用户信息（优化版）

     */

    async loadUserInfo(userId) {

        try {

            console.log('加载用户信息:', userId);

            

            // 使用批量用户获取（即使只有一个用户，也能利用缓存）

            const users = await this.apiOptimizer.getBatchUsers([userId]);

            const userInfo = users[userId];



            if (userInfo) {

                this.userInfo = userInfo;

                this.updateAuthorInfo(userInfo);

            }



        } catch (error) {

            console.error('用户信息加载失败:', error);

            this.updateAuthorInfo({

                nickname: '未知用户',

                avatar: 'https://ui-avatars.com/api/?name=Unknown&background=gray&color=fff'

            });

        }

    }



    /**

     * 更新作者信息显示

     */

    updateAuthorInfo(userInfo) {

        const authorInfoElement = document.querySelector('.author-info');

        if (!authorInfoElement) return;



        authorInfoElement.classList.remove('loading');

        authorInfoElement.innerHTML = `

            <img src="${userInfo.avatar}" alt="${userInfo.nickname}" class="author-avatar" onerror="this.src='https://ui-avatars.com/api/?name=${encodeURIComponent(userInfo.nickname)}&background=gray&color=fff'">

            <div class="author-details">

                <div class="author-name">${userInfo.nickname}</div>

                <div class="post-time">${this.formatDate(this.recordData.data.fields.posttime) || ''}</div>

            </div>

        `;



        console.log('作者信息更新完成');

    }



    /**

     * 格式化日期

     */

    formatDate(timestamp) {

        if (!timestamp) return '';

        

        try {

            const date = new Date(timestamp);

            if (isNaN(date.getTime())) return timestamp;

            

            const now = new Date();

            const diffInSeconds = Math.floor((now - date) / 1000);

            

            // 1分钟内显示"刚刚"

            if (diffInSeconds < 60) {

                return '刚刚';

            }

            

            // 1小时内显示"X分钟前"

            const diffInMinutes = Math.floor(diffInSeconds / 60);

            if (diffInMinutes < 60) {

                return `${diffInMinutes}分钟前`;

            }

            

            // 24小时内显示"X小时前"

            const diffInHours = Math.floor(diffInMinutes / 60);

            if (diffInHours < 24) {

                return `${diffInHours}小时前`;

            }

            

            // 7天内显示"X天前"

            const diffInDays = Math.floor(diffInHours / 24);

            if (diffInDays < 7) {

                return `${diffInDays}天前`;

            }

            

            // 超过7天显示完整日期

            return date.toLocaleString('zh-CN');

        } catch (e) {

            return timestamp;

        }

    }



    /**

     * 渲染内容（优化版）

     */

    async renderContent(content) {

        const contentElement = document.getElementById('content-text');

        if (!contentElement) return;



        try {

            // 使用优化的内容渲染器

            await this.contentOptimizer.renderContent(content, contentElement);

            

            // 内容渲染完成后的处理

            this.onContentRendered();



        } catch (error) {

            console.error('内容渲染失败:', error);

            contentElement.innerHTML = `

                <div class="content-error">

                    <p>内容渲染失败，显示原始文本：</p>

                    <pre style="white-space: pre-wrap;">${this.escapeHtml(content)}</pre>

                </div>

            `;

        }

    }



    /**

     * HTML转义

     */

    escapeHtml(text) {

        const map = {

            '&': '&amp;',

            '<': '&lt;',

            '>': '&gt;',

            '"': '&quot;',

            "'": '&#039;'

        };

        

        return text.replace(/[&<>"']/g, function(m) { return map[m]; });

    }



    /**

     * 内容渲染完成后的处理

     */

    onContentRendered() {

        // 添加代码复制功能

        this.addCodeCopyButtons();

        

        // 添加图片懒加载

        this.setupImageLazyLoading();

        

        // 添加链接处理

        this.setupLinkHandling();

        

        console.log('内容后处理完成');

    }



    /**

     * 添加代码复制按钮

     */

    addCodeCopyButtons() {

        const codeBlocks = document.querySelectorAll('pre code');

        codeBlocks.forEach(block => {

            const button = document.createElement('button');

            button.className = 'copy-code-btn';

            button.textContent = '复制';

            button.onclick = () => {

                navigator.clipboard.writeText(block.textContent);

                button.textContent = '已复制';

                setTimeout(() => {

                    button.textContent = '复制';

                }, 2000);

            };

            

            block.parentElement.style.position = 'relative';

            block.parentElement.appendChild(button);

        });

    }



    /**

     * 设置图片懒加载

     */

    setupImageLazyLoading() {

        const images = document.querySelectorAll('img');

        

        if ('IntersectionObserver' in window) {

            const imageObserver = new IntersectionObserver((entries) => {

                entries.forEach(entry => {

                    if (entry.isIntersecting) {

                        const img = entry.target;

                        if (img.dataset.src) {

                            img.src = img.dataset.src;

                            img.removeAttribute('data-src');

                            imageObserver.unobserve(img);

                        }

                    }

                });

            });



            images.forEach(img => {

                if (img.dataset.src) {

                    imageObserver.observe(img);

                }

            });

        }

    }



    /**

     * 设置链接处理

     */

    setupLinkHandling() {

        const links = document.querySelectorAll('a[href^="http"]');

        links.forEach(link => {

            link.target = '_blank';

            link.rel = 'noopener noreferrer';

        });

    }



    /**

     * 设置阅读时间追踪

     */

    setupReadingTimeTracking() {

        // 页面可见性变化监听

        document.addEventListener('visibilitychange', () => {

            if (document.hidden && this.readStartTime) {

                this.recordReadingTime();

            } else if (!document.hidden && this.readStartTime) {

                // 页面重新可见时重置开始时间

                this.readStartTime = Date.now();

            }

        });



        // 页面卸载前记录阅读时间

        window.addEventListener('beforeunload', () => {

            this.recordReadingTime();

        });



        // 定期记录阅读时间（每30秒）

        this.readingInterval = setInterval(() => {

            this.recordReadingTime();

        }, 30000);

    }



    /**

     * 记录阅读时间

     */

    recordReadingTime() {

        if (this.readStartTime) {

            const readTime = Date.now() - this.readStartTime;

            if (readTime > 1000) { // 只记录超过1秒的阅读时间

                this.apiOptimizer.recordReadingTime(readTime);

            }

            this.readStartTime = Date.now();

        }

    }



    /**

     * 清理资源

     */

    cleanup() {

        if (this.readingInterval) {

            clearInterval(this.readingInterval);

        }

        this.recordReadingTime();

    }



    /**

     * 显示错误信息

     */

    showError(message) {

        const container = document.getElementById('content-container') || document.body;

        container.innerHTML = `

            <div class="error-page">

                <div class="error-icon">⚠️</div>

                <h2>加载失败</h2>

                <p>${message}</p>

                <button onclick="location.reload()" class="retry-btn">重新加载</button>

            </div>

        `;

    }



    /**

     * 获取性能报告

     */

    getPerformanceReport() {

        const apiReport = this.apiOptimizer.getPerformanceReport();

        const contentStats = this.contentOptimizer.getLoadingStats();

        

        return {

            api: apiReport,

            content: contentStats,

            recordId: this.recordId,

            loadTime: Date.now() - (this.startTime || Date.now())

        };

    }

}



// 自动初始化

document.addEventListener('DOMContentLoaded', async () => {

    try {

        window.contentApp = new OptimizedContentApp();

        await window.contentApp.init();

    } catch (error) {

        console.error('应用初始化失败:', error);

        

        // 显示错误页面

        document.getElementById('content-container').innerHTML = `

            <div class="error-page">

                <div class="error-icon">⚠️</div>

                <h2>应用初始化失败</h2>

                <p>请刷新页面重试，或联系技术支持</p>

                <button onclick="location.reload()" class="retry-btn">重新加载</button>

                <details style="margin-top: 20px; text-align: left; max-width: 400px;">

                    <summary>错误详情</summary>

                    <pre style="font-size: 12px; color: #666; margin-top: 10px;">${error.message}</pre>

                </details>

            </div>

        `;

    }

});



// 注册到全局以便调试

window.OptimizedContentApp = OptimizedContentApp;