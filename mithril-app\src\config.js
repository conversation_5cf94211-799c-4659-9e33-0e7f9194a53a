/**
 * 配置文件模块
 * 包含API配置和常量定义
 */

export const API_CONFIG = {
    datasheetId: 'dstCo3t04QBY3RweuR', // 恢复笔记表ID
    timeSheetDatasheetId: 'dsta8guxXYK3pCYsVe', // 时间表ID
    userDatasheetId: 'dst13vlcxlMi1EanCl',
    token: 'uskcZUvxWXvLIPXN0hUC6DK',
    baseURL: 'https://api.vika.cn/fusion/v1',
    viewId: 'viwYoodJk54Xt',
    userViewId: 'viwHzmgPteBwK',
    fieldKey: 'name'
};

// 时间记录模块配置引用
export { TIME_RECORD_API_CONFIG } from './config/timeRecordConfig.js';

export const APP_CONFIG = {
    recordsPerPage: 15, // 增加每页记录数，减少请求次数
    maxFilesPerMemo: 6, // 每个备忘录最多6个文件（图片+视频）
    debounceDelay: 150, // 减少防抖延迟
    maxFileSize: 50 * 1024 * 1024, // 50MB（视频文件更大）
    supportedImageTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
    supportedVideoTypes: ['video/mp4', 'video/webm', 'video/ogg'],
    autoSaveDelay: 2000, // 减少自动保存延迟
    notificationDuration: 2500, // 减少通知显示时间

    // 性能优化配置
    preloadDelay: 800, // 预加载延迟
    cacheTimeout: 2 * 60 * 1000, // 2分钟缓存
    userCacheTimeout: 10 * 60 * 1000, // 用户信息缓存10分钟
    maxConcurrentRequests: 3, // 最大并发请求数
    requestTimeout: 15000, // 请求超时时间15秒（视频上传需要更长时间）
};

export const STORAGE_KEYS = {
    THEME: 'theme',
    TOKEN: 'authToken',
    USER: 'currentUser',
    MEMOS: 'memos',
    SETTINGS: 'app_settings'
};