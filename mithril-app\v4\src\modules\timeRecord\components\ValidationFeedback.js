/**
 * 验证反馈组件
 * 任务: 7.3 - 创建验证错误的用户提示
 * 需求: 6.5 - 用户输入无效数据时系统应显示验证错误信息
 */

const m = (typeof window !== 'undefined' && window.m) || global.m;

export const ValidationFeedback = {
    /**
     * 渲染验证反馈组件
     * @param {Object} vnode - Mithril vnode
     * @param {Object} vnode.attrs.validation - 验证结果对象
     * @param {string} vnode.attrs.field - 字段名称
     * @param {boolean} vnode.attrs.showWarnings - 是否显示警告
     * @param {Function} vnode.attrs.onDismiss - 关闭回调
     */
    view(vnode) {
        const { validation, field = '', showWarnings = true, onDismiss } = vnode.attrs;
        
        if (!validation) {
            return null;
        }
        
        const { errors = [], warnings = [], suggestions = [] } = validation.details || validation;
        const hasErrors = errors.length > 0;
        const hasWarnings = warnings.length > 0;
        
        if (!hasErrors && (!showWarnings || !hasWarnings)) {
            return null;
        }
        
        return m('div.validation-feedback', {
            class: hasErrors ? 'has-errors' : 'has-warnings'
        }, [
            // 错误信息
            hasErrors && m('div.validation-errors', [
                m('div.validation-header', [
                    m('span.validation-icon', '⚠️'),
                    m('span.validation-title', '输入错误'),
                    onDismiss && m('button.validation-close', {
                        onclick: onDismiss,
                        title: '关闭'
                    }, '×')
                ]),
                m('div.validation-content', [
                    m('div.validation-message', validation.error || validation.errorMessage),
                    suggestions.length > 0 && m('div.validation-suggestions', [
                        m('div.suggestions-title', '修复建议:'),
                        m('ul.suggestions-list', 
                            suggestions.map(suggestion => 
                                m('li.suggestion-item', suggestion)
                            )
                        )
                    ])
                ])
            ]),
            
            // 警告信息
            showWarnings && hasWarnings && !hasErrors && m('div.validation-warnings', [
                m('div.validation-header', [
                    m('span.validation-icon', '💡'),
                    m('span.validation-title', '输入提醒'),
                    onDismiss && m('button.validation-close', {
                        onclick: onDismiss,
                        title: '关闭'
                    }, '×')
                ]),
                m('div.validation-content', [
                    m('div.validation-message', validation.warningMessage || 
                        warnings.map(w => w.message).join('; ')
                    )
                ])
            ])
        ]);
    }
};

/**
 * 内联验证反馈组件（用于表单字段旁边）
 */
export const InlineValidationFeedback = {
    view(vnode) {
        const { validation, compact = false } = vnode.attrs;
        
        if (!validation || (!validation.error && !validation.details?.hasWarnings)) {
            return null;
        }
        
        const hasErrors = validation.error || (validation.details?.errors?.length > 0);
        const hasWarnings = validation.details?.hasWarnings;
        
        return m('div.inline-validation', {
            class: [
                hasErrors ? 'has-errors' : '',
                hasWarnings && !hasErrors ? 'has-warnings' : '',
                compact ? 'compact' : ''
            ].filter(Boolean).join(' ')
        }, [
            hasErrors && m('div.inline-error', [
                m('span.inline-icon', '⚠️'),
                m('span.inline-message', validation.error)
            ]),
            
            hasWarnings && !hasErrors && m('div.inline-warning', [
                m('span.inline-icon', '💡'),
                m('span.inline-message', 
                    validation.details.warnings.map(w => w.message).join('; ')
                )
            ])
        ]);
    }
};

/**
 * 字符计数器组件（带验证状态）
 */
export const CharacterCounter = {
    view(vnode) {
        const { 
            current = 0, 
            max = 1000, 
            showWarning = true,
            warningThreshold = 0.9 
        } = vnode.attrs;
        
        const percentage = current / max;
        const isNearLimit = percentage >= warningThreshold;
        const isOverLimit = current > max;
        
        return m('div.character-counter', {
            class: [
                isOverLimit ? 'over-limit' : '',
                isNearLimit && !isOverLimit ? 'near-limit' : ''
            ].filter(Boolean).join(' ')
        }, [
            m('span.counter-text', `${current}/${max}`),
            isNearLimit && m('span.counter-icon', 
                isOverLimit ? '❌' : '⚠️'
            ),
            isOverLimit && m('span.counter-message', '超出字符限制')
        ]);
    }
};

/**
 * 验证状态指示器
 */
export const ValidationStatus = {
    view(vnode) {
        const { validation, showSuccess = true } = vnode.attrs;
        
        if (!validation) {
            return null;
        }
        
        const hasErrors = validation.error || (validation.details?.errors?.length > 0);
        const hasWarnings = validation.details?.hasWarnings;
        const isValid = validation.isValid;
        
        let status, icon, message;
        
        if (hasErrors) {
            status = 'error';
            icon = '❌';
            message = '输入有误';
        } else if (hasWarnings) {
            status = 'warning';
            icon = '⚠️';
            message = '需要注意';
        } else if (isValid && showSuccess) {
            status = 'success';
            icon = '✅';
            message = '输入正确';
        } else {
            return null;
        }
        
        return m('div.validation-status', {
            class: `status-${status}`
        }, [
            m('span.status-icon', icon),
            m('span.status-message', message)
        ]);
    }
};

/**
 * 实时验证提示组件
 */
export const LiveValidationTip = {
    oninit(vnode) {
        vnode.state.showTip = false;
        vnode.state.tipTimeout = null;
    },
    
    onremove(vnode) {
        if (vnode.state.tipTimeout) {
            clearTimeout(vnode.state.tipTimeout);
        }
    },
    
    view(vnode) {
        const { validation, delay = 1000 } = vnode.attrs;
        
        // 延迟显示提示，避免用户输入时频繁显示
        if (validation && validation.error && !vnode.state.showTip) {
            if (vnode.state.tipTimeout) {
                clearTimeout(vnode.state.tipTimeout);
            }
            
            vnode.state.tipTimeout = setTimeout(() => {
                vnode.state.showTip = true;
                if (m && m.redraw) m.redraw();
            }, delay);
        } else if (!validation || !validation.error) {
            vnode.state.showTip = false;
            if (vnode.state.tipTimeout) {
                clearTimeout(vnode.state.tipTimeout);
                vnode.state.tipTimeout = null;
            }
        }
        
        if (!vnode.state.showTip || !validation || !validation.error) {
            return null;
        }
        
        return m('div.live-validation-tip', {
            class: 'fade-in'
        }, [
            m('div.tip-content', [
                m('span.tip-icon', '💡'),
                m('span.tip-message', validation.error),
                validation.suggestions && validation.suggestions.length > 0 && 
                m('div.tip-suggestions', 
                    validation.suggestions.map(suggestion => 
                        m('div.tip-suggestion', `💡 ${suggestion}`)
                    )
                )
            ])
        ]);
    }
};

/**
 * 批量验证结果摘要组件
 */
export const ValidationSummary = {
    view(vnode) {
        const { validations = {}, title = '验证结果' } = vnode.attrs;
        
        const allErrors = [];
        const allWarnings = [];
        let totalFields = 0;
        let validFields = 0;
        
        Object.entries(validations).forEach(([field, validation]) => {
            totalFields++;
            if (validation && validation.isValid) {
                validFields++;
            }
            
            if (validation && validation.details) {
                if (validation.details.errors) {
                    allErrors.push(...validation.details.errors.map(e => ({ field, ...e })));
                }
                if (validation.details.warnings) {
                    allWarnings.push(...validation.details.warnings.map(w => ({ field, ...w })));
                }
            }
        });
        
        const hasErrors = allErrors.length > 0;
        const hasWarnings = allWarnings.length > 0;
        
        if (!hasErrors && !hasWarnings) {
            return m('div.validation-summary.success', [
                m('div.summary-header', [
                    m('span.summary-icon', '✅'),
                    m('span.summary-title', '所有输入都正确')
                ])
            ]);
        }
        
        return m('div.validation-summary', {
            class: hasErrors ? 'has-errors' : 'has-warnings'
        }, [
            m('div.summary-header', [
                m('span.summary-icon', hasErrors ? '❌' : '⚠️'),
                m('span.summary-title', title),
                m('span.summary-stats', `${validFields}/${totalFields} 字段正确`)
            ]),
            
            hasErrors && m('div.summary-errors', [
                m('div.summary-section-title', `错误 (${allErrors.length})`),
                m('ul.summary-list', 
                    allErrors.map(error => 
                        m('li.summary-item.error', [
                            m('span.item-field', error.field),
                            m('span.item-message', error.message)
                        ])
                    )
                )
            ]),
            
            hasWarnings && m('div.summary-warnings', [
                m('div.summary-section-title', `提醒 (${allWarnings.length})`),
                m('ul.summary-list', 
                    allWarnings.map(warning => 
                        m('li.summary-item.warning', [
                            m('span.item-field', warning.field),
                            m('span.item-message', warning.message)
                        ])
                    )
                )
            ])
        ]);
    }
};