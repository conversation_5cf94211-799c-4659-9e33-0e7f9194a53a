/**
 * 视频放大播放功能工具
 * 当用户点击视频播放时，视频会在当前备忘录卡片内放大显示
 * 暂停时自动恢复原始大小
 */

class VideoEnhancer {
    constructor() {
        // 存储正在放大的视频信息
        this.enlargedVideos = new Map();
    }

    /**
     * 为视频元素添加放大播放功能
     * @param {HTMLVideoElement} videoElement - 视频元素
     */
    enhanceVideo(videoElement) {
        if (!videoElement || videoElement.tagName !== 'VIDEO') {
            console.warn('VideoEnhancer: 无效的视频元素');
            return;
        }

        // 避免重复绑定
        if (videoElement.dataset.enhanced === 'true') {
            return;
        }

        // 标记已增强
        videoElement.dataset.enhanced = 'true';

        // 绑定播放事件
        videoElement.addEventListener('play', (e) => {
            this.enlargeVideo(videoElement);
        });

        // 绑定暂停事件
        videoElement.addEventListener('pause', (e) => {
            this.restoreVideo(videoElement);
        });

        // 绑定结束事件
        videoElement.addEventListener('ended', (e) => {
            this.restoreVideo(videoElement);
        });
    }

    /**
     * 放大视频显示
     * @param {HTMLVideoElement} videoElement - 视频元素
     */
    enlargeVideo(videoElement) {
        // 如果已经放大，直接返回
        if (this.enlargedVideos.has(videoElement)) {
            return;
        }

        try {
            // 创建包装容器
            const wrapper = document.createElement('div');
            wrapper.className = 'video-enlarged-wrapper';
            wrapper.style.cssText = `
                position: relative;
                width: 100%;
                margin: 15px 0;
                z-index: 15;
                transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                display: flex;
                justify-content: center;
                align-items: center;
                background: rgba(0, 0, 0, 0.02);
                border-radius: 20px;
                padding: 10px;
            `;

            // 保存原始状态
            const originalState = {
                parent: videoElement.parentNode,
                nextSibling: videoElement.nextSibling,
                wrapper: wrapper,
                styles: {
                    maxWidth: videoElement.style.maxWidth || '',
                    maxHeight: videoElement.style.maxHeight || '',
                    minHeight: videoElement.style.minHeight || '',
                    boxShadow: videoElement.style.boxShadow || '',
                    border: videoElement.style.border || '',
                    borderRadius: videoElement.style.borderRadius || '',
                    width: videoElement.style.width || '',
                    height: videoElement.style.height || '',
                    transition: videoElement.style.transition || '',
                    transform: videoElement.style.transform || '',
                    zIndex: videoElement.style.zIndex || ''
                }
            };

            // 存储原始状态
            this.enlargedVideos.set(videoElement, originalState);

            // 重新定位视频元素
            originalState.parent.removeChild(videoElement);
            wrapper.appendChild(videoElement);
            originalState.parent.insertBefore(wrapper, originalState.nextSibling);

            // 获取卡片容器的尺寸来计算最佳放大尺寸
            const memoCard = videoElement.closest('.memo-card') || videoElement.closest('.memo-item') || videoElement.closest('.memo-item-wrapper');
            let maxWidth = '100%';
            let maxHeight = '500px';

            if (memoCard) {
                const cardRect = memoCard.getBoundingClientRect();
                const viewportHeight = window.innerHeight;
                const viewportWidth = window.innerWidth;

                // 计算可用空间，考虑卡片内的其他内容
                const availableWidth = Math.max(cardRect.width - 60, 320); // 留出边距
                const availableHeight = Math.min(
                    viewportHeight * 0.7,  // 不超过视口高度的70%
                    cardRect.height * 1.2, // 可以稍微超出卡片高度
                    700 // 最大高度限制
                );

                maxWidth = availableWidth + 'px';
                maxHeight = availableHeight + 'px';

                console.log('视频放大尺寸计算:', {
                    cardWidth: cardRect.width,
                    cardHeight: cardRect.height,
                    maxWidth,
                    maxHeight
                });
            }

            // 应用放大样式
            videoElement.style.maxWidth = maxWidth;
            videoElement.style.maxHeight = maxHeight;
            videoElement.style.minHeight = '200px'; // 确保最小高度
            videoElement.style.boxShadow = '0 8px 24px rgba(0, 0, 0, 0.2)';
            videoElement.style.border = '3px solid var(--primary, #007bff)';
            videoElement.style.borderRadius = '16px';
            videoElement.style.width = '100%';
            videoElement.style.height = 'auto';
            videoElement.style.transition = 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
            videoElement.style.transform = 'scale(1.05)';
            videoElement.style.zIndex = '20';

            console.log('视频已放大显示');

        } catch (error) {
            console.error('放大视频时出错:', error);
            // 清理可能的残留状态
            this.enlargedVideos.delete(videoElement);
        }
    }

    /**
     * 恢复视频原始大小
     * @param {HTMLVideoElement} videoElement - 视频元素
     */
    restoreVideo(videoElement) {
        const originalState = this.enlargedVideos.get(videoElement);
        if (!originalState) {
            return;
        }

        try {
            const { parent, nextSibling, wrapper, styles } = originalState;

            // 从包装容器中移除视频
            wrapper.removeChild(videoElement);
            
            // 将视频放回原位置
            if (nextSibling && nextSibling.parentNode === parent) {
                parent.insertBefore(videoElement, nextSibling);
            } else {
                parent.appendChild(videoElement);
            }
            
            // 移除包装容器
            if (wrapper.parentNode) {
                wrapper.parentNode.removeChild(wrapper);
            }

            // 恢复原始样式
            Object.keys(styles).forEach(property => {
                videoElement.style[property] = styles[property];
            });

            // 清理存储的状态
            this.enlargedVideos.delete(videoElement);

            console.log('视频已恢复原始大小');

        } catch (error) {
            console.error('恢复视频大小时出错:', error);
            // 强制清理状态
            this.enlargedVideos.delete(videoElement);
        }
    }

    /**
     * 批量增强页面中的所有视频元素
     * @param {Element} container - 容器元素，默认为document
     */
    enhanceAllVideos(container = document) {
        const videos = container.querySelectorAll('video.memo-video');
        videos.forEach(video => {
            this.enhanceVideo(video);
        });
    }

    /**
     * 清理所有放大的视频
     */
    cleanup() {
        this.enlargedVideos.forEach((originalState, videoElement) => {
            this.restoreVideo(videoElement);
        });
        this.enlargedVideos.clear();
    }

    /**
     * 获取当前放大的视频数量
     */
    getEnlargedCount() {
        return this.enlargedVideos.size;
    }
}

// 创建全局实例
const videoEnhancer = new VideoEnhancer();

// 导出实例和类
export { videoEnhancer, VideoEnhancer };
export default videoEnhancer;
