// 使用全局的 Mithril 实例而不是导入
const m = window.m;

export const UpdateLogModal = {
    view: function(vnode) {
        if (!window.appState.showChangelogModal) {
            return null;
        }
        
        return m('.modal.show', {
            onclick: (e) => {
                if (e.target.classList.contains('modal')) {
                    window.appState.toggleChangelogModal(false);
                }
            }
        }, [
            m('.modal-content', [
                m('span.close', {
                    onclick: () => window.appState.toggleChangelogModal(false)
                }, '×'),
                m('h2', '更新日志'),
                m('.changelog-content', [
                    m('.changelog-item', [
                        m('h3', 'v1.0.0'),
                        m('p', '初始版本发布')
                    ])
                ])
            ])
        ]);
    }
};