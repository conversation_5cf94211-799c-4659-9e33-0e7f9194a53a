// 使用全局的 Mithril 实例
const m = window.m;

// 导入工具函数和优化组件
import { formatDate } from '../utils/format.js';
import { markdownRenderer } from '../utils/markdown.js';
import { LazyImage, debounce } from '../utils/renderOptimizer.js';
import videoEnhancer from '../utils/videoEnhancer.js';

/**
 * 优化的备忘录列表组件
 * 支持虚拟滚动、懒加载、性能监控等功能
 */
export const OptimizedMemosList = {
    oninit: function(vnode) {
        this.visibleItems = [];
        this.itemHeight = 200; // 估算的项目高度
        this.containerHeight = window.innerHeight - 200; // 容器高度
        this.scrollTop = 0;
        this.buffer = 3; // 缓冲区项目数
        this.lastListLength = 0; // 记录上次列表长度
        
        // 防抖的滚动处理
        this.debouncedScroll = debounce((scrollTop) => {
            this.updateVisibleItems(scrollTop);
            m.redraw();
        }, 16); // 60fps

        // 初始化可见项目
        this.updateVisibleItems(0);
    },

    /**
     * 更新可见项目
     */
    updateVisibleItems: function(scrollTop) {
        const items = window.memos.list;
        if (!items.length) {
            this.visibleItems = [];
            return;
        }

        const visibleCount = Math.ceil(this.containerHeight / this.itemHeight);
        const startIndex = Math.max(0, Math.floor(scrollTop / this.itemHeight) - this.buffer);
        const endIndex = Math.min(items.length, startIndex + visibleCount + this.buffer * 2);

        this.visibleItems = items.slice(startIndex, endIndex).map((item, index) => ({
            ...item,
            virtualIndex: startIndex + index,
            actualIndex: startIndex + index
        }));

        this.scrollTop = scrollTop;
        
        // 如果是新添加的项目（在顶部），确保它们可见
        if (scrollTop === 0 && items.length > 0) {
            // 确保至少显示前几个项目
            const minVisible = Math.min(5, items.length);
            if (this.visibleItems.length < minVisible) {
                this.visibleItems = items.slice(0, minVisible).map((item, index) => ({
                    ...item,
                    virtualIndex: index,
                    actualIndex: index
                }));
            }
        }
    },

    /**
     * 处理滚动事件
     */
    handleScroll: function(e) {
        this.debouncedScroll(e.target.scrollTop);
    },

    /**
     * 渲染单个备忘录项目
     */
    renderMemoItem: function(memo, index) {
        const itemStyle = {
            position: 'absolute',
            top: `${memo.virtualIndex * this.itemHeight}px`,
            width: '100%',
            minHeight: `${this.itemHeight}px`,
            padding: '0 var(--space-sm)'
        };

        const result = m('.memo-item-wrapper', {
            key: memo.id || memo.actualIndex,
            style: itemStyle
        }, [
            m('.memo-card.optimized', [
                m('.memo-header', [
                    m('.memo-time', formatDate(memo.createdTime || new Date().toISOString())),
                    memo.status === '私密' ? m('.memo-status.private', '🔒 私密') : null
                ]),
                
                // 使用 Markdown 渲染内容，带缓存
                m('.memo-content.markdown-rendered', {
                    innerHTML: this.renderMemoContent(memo.content)
                }),
                
                // 优化的媒体文件显示
                this.renderMediaFiles(memo)
            ])
        ]);

        return result;
    },

    /**
     * 渲染备忘录内容（带缓存）
     */
    renderMemoContent: function(content) {
        if (!content) return '';
        
        // 简单的内容缓存
        const cacheKey = `content-${content.length}-${content.substring(0, 50)}`;
        if (this.contentCache && this.contentCache[cacheKey]) {
            return this.contentCache[cacheKey];
        }

        const rendered = markdownRenderer.render(content);
        
        // 缓存渲染结果
        if (!this.contentCache) this.contentCache = {};
        this.contentCache[cacheKey] = rendered;
        
        return rendered;
    },

    /**
     * 渲染媒体文件
     */
    renderMediaFiles: function(memo) {
        const mediaFiles = memo.mediaFiles || [];
        
        // 如果没有 mediaFiles，尝试从旧字段构建
        if (!mediaFiles.length) {
            const legacyMediaFiles = [];
            
            // 处理旧的 images 字段
            const images = memo.images || [];
            images.forEach(url => {
                if (url && url.trim()) {
                    legacyMediaFiles.push({
                        url: url.trim(),
                        type: 'image'
                    });
                }
            });
            
            // 处理旧的 videos 字段
            const videos = memo.videos || [];
            videos.forEach(url => {
                if (url && url.trim()) {
                    legacyMediaFiles.push({
                        url: url.trim(),
                        type: 'video'
                    });
                }
            });
            
            // 如果还是没有媒体文件，返回null
            if (!legacyMediaFiles.length) return null;
            
            return m('.memo-media', legacyMediaFiles.map((media, index) => {
                if (media.type === 'image') {
                    return m(LazyImage, {
                        key: `legacy-img-${memo.id}-${index}`,
                        src: media.url,
                        alt: '备忘录图片',
                        className: 'memo-image',
                        loading: 'lazy',
                        onclick: (e) => this.showImagePreview(media.url, e)
                    });
                } else if (media.type === 'video') {
                    return m('video', {
                        key: `legacy-video-${memo.id}-${index}`,
                        src: media.url,
                        class: 'memo-video',
                        controls: true,
                        preload: 'metadata',
                        onclick: (e) => e.stopPropagation(),
                        oncreate: (vnode) => {
                            // 为视频添加放大播放功能
                            videoEnhancer.enhanceVideo(vnode.dom);
                        }
                    });
                }
                return null;
            }).filter(Boolean));
        }

        // 处理新的 mediaFiles 格式
        return m('.memo-media', mediaFiles.map((media, index) => {
            if (media.type === 'image') {
                return m(LazyImage, {
                    key: `media-img-${memo.id}-${index}`,
                    src: media.url,
                    alt: '备忘录图片',
                    className: 'memo-image',
                    loading: 'lazy',
                    onclick: (e) => this.showImagePreview(media.url, e)
                });
            } else if (media.type === 'video') {
                return m('video', {
                    key: `media-video-${memo.id}-${index}`,
                    src: media.url,
                    class: 'memo-video',
                    controls: true,
                    preload: 'metadata',
                    onclick: (e) => e.stopPropagation(),
                    oncreate: (vnode) => {
                        // 为视频添加放大播放功能
                        videoEnhancer.enhanceVideo(vnode.dom);
                    }
                });
            }
            return null;
        }).filter(Boolean));
    },

    /**
     * 显示图片预览
     */
    showImagePreview: function(imageUrl, e) {
        e.preventDefault();
        e.stopPropagation();

        const modal = document.createElement('div');
        modal.className = 'modal show image-preview-modal';
        modal.innerHTML = `
            <div class="modal-content image-preview-content">
                <span class="close" onclick="this.parentElement.parentElement.remove()">&times;</span>
                <div class="image-container">
                    <div class="image-loading">
                        <div class="loading-spinner"></div>
                        <p>加载中...</p>
                    </div>
                    <img src="${imageUrl}" alt="图片预览" class="preview-image" style="display: none;">
                </div>
            </div>
        `;
        document.body.appendChild(modal);
        
        // 图片加载处理
        const img = modal.querySelector('.preview-image');
        const loading = modal.querySelector('.image-loading');
        
        img.onload = function() {
            loading.style.display = 'none';
            img.style.display = 'block';
            img.style.opacity = '0';
            img.style.transition = 'opacity 0.3s ease';
            setTimeout(() => {
                img.style.opacity = '1';
            }, 10);
        };
        
        img.onerror = function() {
            loading.innerHTML = '<p style="color: var(--muted);">图片加载失败</p>';
        };
        
        // 点击模态框背景关闭
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                modal.remove();
            }
        });
        
        // ESC键关闭
        const handleEsc = function(e) {
            if (e.key === 'Escape') {
                modal.remove();
                document.removeEventListener('keydown', handleEsc);
            }
        };
        document.addEventListener('keydown', handleEsc);
    },

    view: function(vnode) {
        // 检查列表是否有新项目添加
        const currentListLength = window.memos.list.length;
        if (currentListLength > this.lastListLength) {
            // 有新项目添加，滚动到顶部并更新可见项目
            this.scrollTop = 0;
            this.updateVisibleItems(0);
            this.lastListLength = currentListLength;
            
            // 如果容器存在，滚动到顶部
            setTimeout(() => {
                const container = document.querySelector('.optimized-memos-list');
                if (container) {
                    container.scrollTop = 0;
                }
            }, 10);
        } else {
            this.lastListLength = currentListLength;
        }

        if (window.memos.loading && window.memos.list.length === 0) {
            return m('.loading-container', [
                m('.loading-spinner'),
                m('p', '正在加载精彩内容...')
            ]);
        }
        
        if (window.memos.list.length === 0 && !window.memos.loading) {
            return m('.empty-state', [
                m('h3', '还没有任何记录'),
                m('p', '开始记录你的第一个想法吧！')
            ]);
        }

        const totalHeight = window.memos.list.length * this.itemHeight;

        const result = m('.optimized-memos-list', {
            style: {
                height: `${this.containerHeight}px`,
                overflow: 'auto',
                position: 'relative'
            },
            onscroll: (e) => this.handleScroll(e)
        }, [
            m('.memos-content', {
                style: {
                    height: `${totalHeight}px`,
                    position: 'relative'
                }
            }, this.visibleItems.map((memo, index) => 
                this.renderMemoItem(memo, index)
            ))
        ]);

        return result;
    }
};