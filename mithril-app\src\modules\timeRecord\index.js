// 简化版时间记录模块入口
// 用于替代完整版的复杂实现

// 导入组件
import { SimpleTimeRecord } from './TimeRecord.js';

// 导出模块
export {
  SimpleTimeRecord
};

// 配置常量
export const MODULE_CONFIG = {
  name: 'timeRecord',
  version: '1.0.0',
  description: '简化版时间记录模块'
};

// 工厂函数
export function createTimeRecordModule() {
  return {
    config: MODULE_CONFIG,
    component: SimpleTimeRecord
  };
}

// 模块信息
export const MODULE_INFO = {
  id: 'time-record-module',
  name: '时间记录模块',
  version: '1.0.0',
  description: '用于记录和查看时间的简化版模块',
  author: 'Developer'
};