/**

 * 加载骨架屏样式

 * 提供优雅的加载状态视觉反馈

 */



/* 骨架屏容器 */

.loading-skeleton {

    padding: 20px;

}



/* 骨架屏项目 */

.skeleton-item {

    display: flex;

    align-items: flex-start;

    padding: 15px;

    margin-bottom: 15px;

    background: #f8f9fa;

    border-radius: 8px;

    animation: skeleton-pulse 1.5s ease-in-out infinite;

}



/* 骨架屏头像 */

.skeleton-avatar {

    width: 40px;

    height: 40px;

    border-radius: 50%;

    background: linear-gradient(90deg, #e2e8f0 25%, #f1f5f9 50%, #e2e8f0 75%);

    background-size: 200% 100%;

    animation: skeleton-shimmer 1.5s infinite;

    margin-right: 12px;

    flex-shrink: 0;

}



/* 骨架屏内容区域 */

.skeleton-content {

    flex: 1;

}



/* 骨架屏文本行 */

.skeleton-line {

    height: 16px;

    background: linear-gradient(90deg, #e2e8f0 25%, #f1f5f9 50%, #e2e8f0 75%);

    background-size: 200% 100%;

    animation: skeleton-shimmer 1.5s infinite;

    border-radius: 4px;

    margin-bottom: 8px;

}



.skeleton-line.short {

    width: 60%;

}



/* 骨架屏动画 */

@keyframes skeleton-pulse {

    0%, 100% {

        opacity: 1;

    }

    50% {

        opacity: 0.8;

    }

}



@keyframes skeleton-shimmer {

    0% {

        background-position: -200% 0;

    }

    100% {

        background-position: 200% 0;

    }

}



/* 备忘录项目样式 */

.memo-item {

    background: white;

    border: 1px solid #e2e8f0;

    border-radius: 8px;

    padding: 16px;

    margin-bottom: 12px;

    transition: all 0.2s ease;

}



.memo-item:hover {

    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    border-color: #cbd5e0;

}



/* 备忘录头部 */

.memo-header {

    display: flex;

    align-items: center;

    margin-bottom: 12px;

}



.user-avatar {

    width: 40px;

    height: 40px;

    border-radius: 50%;

    margin-right: 12px;

    object-fit: cover;

}



.user-info {

    display: flex;

    flex-direction: column;

}



.username {

    font-weight: 600;

    color: #2d3748;

    font-size: 14px;

}



.post-time {

    color: #718096;

    font-size: 12px;

    margin-top: 2px;

}



/* 备忘录内容 */

.memo-content {

    color: #4a5568;

    line-height: 1.6;

    margin-bottom: 12px;

    word-wrap: break-word;

}



/* 备忘录操作按钮 */

.memo-actions {

    display: flex;

    gap: 8px;

    justify-content: flex-end;

}



.memo-actions button {

    padding: 6px 12px;

    border: 1px solid #e2e8f0;

    background: white;

    color: #4a5568;

    border-radius: 4px;

    cursor: pointer;

    font-size: 12px;

    transition: all 0.2s ease;

}



.memo-actions button:hover {

    background: #f7fafc;

    border-color: #cbd5e0;

}



/* 加载状态 */

.loading-indicator {

    display: flex;

    align-items: center;

    justify-content: center;

    padding: 20px;

    color: #718096;

}



.loading-spinner {

    width: 20px;

    height: 20px;

    border: 2px solid #e2e8f0;

    border-top: 2px solid #3182ce;

    border-radius: 50%;

    animation: spin 1s linear infinite;

    margin-right: 8px;

}



@keyframes spin {

    0% { transform: rotate(0deg); }

    100% { transform: rotate(360deg); }

}



/* 错误状态 */

.error-message {

    background: #fed7d7;

    color: #c53030;

    padding: 12px;

    border-radius: 6px;

    margin: 16px 0;

    border: 1px solid #feb2b2;

}



/* 成功状态 */

.success-message {

    background: #c6f6d5;

    color: #2f855a;

    padding: 12px;

    border-radius: 6px;

    margin: 16px 0;

    border: 1px solid #9ae6b4;

}



/* 分页控件 */

.pagination {

    display: flex;

    justify-content: center;

    align-items: center;

    gap: 12px;

    margin: 20px 0;

}



.pagination button {

    padding: 8px 16px;

    border: 1px solid #e2e8f0;

    background: white;

    color: #4a5568;

    border-radius: 6px;

    cursor: pointer;

    transition: all 0.2s ease;

}



.pagination button:hover:not(:disabled) {

    background: #f7fafc;

    border-color: #cbd5e0;

}



.pagination button:disabled {

    opacity: 0.5;

    cursor: not-allowed;

}



.pagination .current-page {

    font-weight: 600;

    color: #2d3748;

}



/* 搜索框 */

.search-container {

    margin-bottom: 20px;

}



.search-input {

    width: 100%;

    padding: 12px 16px;

    border: 1px solid #e2e8f0;

    border-radius: 8px;

    font-size: 14px;

    transition: border-color 0.2s ease;

}



.search-input:focus {

    outline: none;

    border-color: #3182ce;

    box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);

}



/* 性能统计 */

.performance-stats {

    background: #f7fafc;

    border: 1px solid #e2e8f0;

    border-radius: 6px;

    padding: 12px;

    margin: 16px 0;

    font-size: 12px;

    color: #4a5568;

}



.performance-stats .stat-item {

    display: inline-block;

    margin-right: 16px;

}



.performance-stats .stat-value {

    font-weight: 600;

    color: #2d3748;

}



/* 响应式设计 */

@media (max-width: 768px) {

    .memo-item {

        padding: 12px;

    }

    

    .memo-header {

        margin-bottom: 8px;

    }

    

    .user-avatar {

        width: 32px;

        height: 32px;

    }

    

    .memo-actions {

        justify-content: flex-start;

    }

    

    .pagination {

        flex-wrap: wrap;

        gap: 8px;

    }

}