/**
 * 时间记录模块配置文件
 * 包含vika API字段映射和相关常量
 */

// Vika API 字段映射配置
export const TIME_RECORD_FIELDS = {
    // 时间记录分类
    CATEGORY: 'fld9Mole3W9eR',
    // 备注信息
    CONTENT: 'fld0znfZ7H9xM', 
    // 开始时间戳(秒)
    START_TIMESTAMP: 'fld3e659QaQ11',
    // 结束时间戳(秒)
    END_TIMESTAMP: 'flddjkaHotlMJ',
    // 持续时间文本描述
    DURATION_TEXT: 'fldNY5MabI72y',
    // 记录创建时间
    CREATED_TIME: 'fld1pawhrPs9x',
    // 用户
    USER: 'fldS1LvP1QVhN'
};

// 时间记录模块API配置
export const TIME_RECORD_API_CONFIG = {
    // 数据表ID
    DATASHEET_ID: 'dsta8guxXYK3pCYsVe',
    // 视图ID
    VIEW_ID: 'viw5mLr0sA5d9',
    // API基础URL
    BASE_URL: 'https://api.vika.cn/fusion/v1',
    // 认证token
    TOKEN: 'uskcZUvxWXvLIPXN0hUC6DK',
    // 字段键类型
    FIELD_KEY: 'id',
    // API限流配置 (每秒最多5次请求)
    RATE_LIMIT: {
        MAX_REQUESTS: 5,
        TIME_WINDOW: 1000 // 1秒
    }
};

// 时间记录模块业务配置
export const TIME_RECORD_CONFIG = {
    // 分页配置
    PAGINATION: {
        DEFAULT_PAGE_SIZE: 20,
        MAX_PAGE_SIZE: 100
    },
    // 计时器配置
    TIMER: {
        UPDATE_INTERVAL: 1000, // 每秒更新一次显示
        AUTO_SAVE_INTERVAL: 30000 // 30秒自动保存草稿
    },
    // 数据验证配置
    VALIDATION: {
        MAX_CONTENT_LENGTH: 1000,
        MIN_DURATION: 1, // 最小记录时长1秒
        MAX_CATEGORY_LENGTH: 50
    },
    // 默认值配置
    DEFAULTS: {
        CATEGORY: '工作记录',
        CONTENT_PLACEHOLDER: '请输入工作内容...'
    },
    // 错误重试配置
    RETRY: {
        MAX_ATTEMPTS: 3,
        DELAY: 1000, // 1秒
        BACKOFF_MULTIPLIER: 2
    }
};

// 时间格式化配置
export const TIME_FORMAT_CONFIG = {
    // 时长显示格式
    DURATION_FORMATS: {
        FULL: 'HH:mm:ss', // 完整格式：01:30:45
        SHORT: 'mm:ss',   // 短格式：30:45
        TEXT: 'text'      // 文本格式：1小时30分钟
    },
    // 时间戳显示格式
    TIMESTAMP_FORMATS: {
        DATE_TIME: 'YYYY-MM-DD HH:mm:ss',
        DATE_ONLY: 'YYYY-MM-DD',
        TIME_ONLY: 'HH:mm:ss'
    }
};

// 错误消息配置
export const TIME_RECORD_ERRORS = {
    NETWORK_ERROR: '网络连接失败，请检查网络设置',
    API_LIMIT_ERROR: 'API请求过于频繁，请稍后再试',
    VALIDATION_ERROR: '输入数据格式不正确',
    PERMISSION_ERROR: '没有访问权限，请重新登录',
    SAVE_ERROR: '保存失败，请稍后重试',
    LOAD_ERROR: '加载数据失败，请刷新页面',
    TIMER_ERROR: '计时器操作失败',
    UNKNOWN_ERROR: '操作失败，请稍后重试'
};

// 成功消息配置
export const TIME_RECORD_MESSAGES = {
    SAVE_SUCCESS: '记录保存成功',
    DELETE_SUCCESS: '记录删除成功',
    TIMER_STARTED: '计时开始',
    TIMER_STOPPED: '计时结束',
    TIMER_RESET: '计时器已重置'
};