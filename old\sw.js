const CACHE_NAME = 'memo-app-v1.0.2';
const urlsToCache = [
  '/',
  '/index.html',
  '/content.html',
  '/css/main.css',
  '/css/loading-skeleton.css',
  '/css/optimized-content.css',
  '/css/vendor/github.min.css',
  '/js/app.js',
  '/js/config.js',
  '/js/content-app.js',
  '/js/optimized-content-app.js',
  '/js/modules/apiOptimizer.js',
  '/js/modules/contentOptimizer.js',
  '/components/header.html',
  '/components/login-banner.html',
  '/components/login-modal.html',
  '/components/post-section.html',
  '/components/memos-list.html',
  '/components/load-more-button.html',
  '/components/footer.html',
  '/components/update-log-modal.html'
];

// 安装事件 - 缓存核心资源
self.addEventListener('install', event => {
  console.log('Service Worker installing...');
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(cache => {
        console.log('缓存核心资源');
        return cache.addAll(urlsToCache);
      })
      .catch(err => {
        console.error('缓存核心资源失败:', err);
      })
  );
  self.skipWaiting(); // 强制更新到新的Service Worker
});

// 激活事件 - 清理旧缓存
self.addEventListener('activate', event => {
  console.log('Service Worker activating...');
  event.waitUntil(
    caches.keys().then(cacheNames => {
      return Promise.all(
        cacheNames.map(cacheName => {
          if (cacheName !== CACHE_NAME) {
            console.log('删除旧缓存:', cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    })
  );
  return self.clients.claim(); // 立即控制所有页面
});

// 获取事件 - 实现离线功能
self.addEventListener('fetch', event => {
  // 对于API请求，尝试网络优先，失败时使用缓存
  if (event.request.url.includes('api.vika.cn')) {
    event.respondWith(
      fetch(event.request)
        .then(response => {
          // 对API响应进行缓存
          const responseClone = response.clone();
          caches.open(CACHE_NAME).then(cache => {
            cache.put(event.request, responseClone);
          });
          return response;
        })
        .catch(() => {
          // 网络失败时尝试从缓存获取
          return caches.match(event.request)
            .then(response => {
              if (response) {
                console.log('使用缓存的API响应:', event.request.url);
                return response;
              }
              // 缓存也没有则返回错误响应
              return new Response(JSON.stringify({ 
                error: '网络不可用且无缓存数据',
                offline: true 
              }), {
                headers: { 'Content-Type': 'application/json' },
                status: 503
              });
            });
        })
    );
    return;
  }

  // 对于静态资源，使用缓存优先策略
  event.respondWith(
    caches.match(event.request)
      .then(response => {
        // 缓存命中直接返回
        if (response) {
          console.log('缓存命中:', event.request.url);
          return response;
        }

        // 缓存未命中，从网络获取
        console.log('缓存未命中，从网络获取:', event.request.url);
        return fetch(event.request)
          .then(response => {
            // 检查响应是否有效
            if (!response || response.status !== 200 || response.type !== 'basic') {
              return response;
            }

            // 克隆响应并存入缓存
            const responseToCache = response.clone();
            caches.open(CACHE_NAME)
              .then(cache => {
                cache.put(event.request, responseToCache);
              });

            return response;
          })
          .catch(error => {
            console.error('网络请求失败:', error);
            
            // 对于HTML文档请求，返回离线页面
            if (event.request.headers.get('accept').includes('text/html')) {
              return caches.match('/offline.html') || 
                     new Response(`
                      <!DOCTYPE html>
                      <html>
                      <head>
                        <meta charset="utf-8">
                        <meta name="viewport" content="width=device-width, initial-scale=1">
                        <title>离线状态</title>
                        <style>
                          body { 
                            font-family: Arial, sans-serif; 
                            text-align: center; 
                            padding: 50px; 
                            background: #f0f0f0;
                          }
                          .offline-icon {
                            font-size: 48px;
                            margin-bottom: 20px;
                          }
                        </style>
                      </head>
                      <body>
                        <div class="offline-icon">🌐</div>
                        <h1>当前处于离线状态</h1>
                        <p>请检查网络连接后重试</p>
                        <button onclick="location.reload()">重新加载</button>
                      </body>
                      </html>
                     `, {
                       headers: { 'Content-Type': 'text/html' }
                     });
            }
            
            // 其他资源直接返回错误
            return new Response('网络不可用', { status: 503 });
          });
      })
  );
});

// 消息事件 - 接收来自主线程的消息
self.addEventListener('message', event => {
  if (event.data && event.data.action === 'skipWaiting') {
    self.skipWaiting();
  }
});