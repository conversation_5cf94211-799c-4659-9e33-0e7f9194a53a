<!-- 卡片生成模态窗组件 -->

<div id="cardModal" class="modal hidden">

    <div class="modal-content">

        <div class="modal-header">

            <h3 class="modal-title">分享卡片</h3>

            <button id="closeCardModal" class="modal-close" title="关闭">

                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">

                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />

                </svg>

            </button>

        </div>

        

        <div class="modal-body">

            <div id="cardPreview" class="flex justify-center items-center min-h-[200px] bg-gray-50 dark:bg-gray-700 rounded-lg">

                <span class="text-gray-500 dark:text-gray-400">生成中...</span>

            </div>

        </div>

        

        <div class="modal-footer">

            <button id="downloadCardBtn" class="btn btn-primary">

                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">

                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>

                </svg>

                下载卡片

            </button>

            <button id="cancelCardBtn" class="btn btn-ghost">取消</button>

        </div>

    </div>

</div>