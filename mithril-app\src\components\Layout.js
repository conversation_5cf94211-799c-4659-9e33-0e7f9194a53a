// 使用全局的 Mithril 实例而不是导入
const m = window.m;

// 使用相对路径导入组件
import { Header } from './Header.js';
import { LoginBanner } from './LoginBanner.js';
import { PostSection } from './PostSection.js';
import { MemosList } from './MemosList.js';
import { OptimizedMemosList } from './OptimizedMemosList.js';
import { LoadMoreButton } from './LoadMoreButton.js';
import { Footer } from './Footer.js';
import { LoginModal } from './LoginModal.js';
import { UpdateLogModal } from './UpdateLogModal.js';
import { SearchBox } from './SearchBox.js';
// 移除性能监控依赖

export const Layout = {
    oninit: function(vnode) {
        // 检测是否使用优化版本
        this.useOptimizedList = window.memos.list.length > 20 || window.location.search.includes('optimized=true');

        // 初始化搜索状态
        this.searchQuery = '';
        this.listComponentRef = null;

        // 初始化时加载数据
        window.memos.loadMemos(1);

        // 监听认证状态变化事件
        this.authListener = (e) => {
            // 认证状态改变时重新加载内容
            window.memos.list = [];
            window.memos.currentPage = 1;
            window.memos.hasMore = true;
            window.memos.loadMemos(1);
        };

        window.addEventListener('auth:login', this.authListener);
        window.addEventListener('auth:logout', this.authListener);
    },

    handleSearch: function(query) {
        this.searchQuery = query;
        if (this.listComponentRef && this.listComponentRef.handleSearch) {
            this.listComponentRef.handleSearch(query);
        }
        m.redraw();
    },
    
    onremove: function(vnode) {
        // 移除事件监听器
        window.removeEventListener('auth:login', this.authListener);
        window.removeEventListener('auth:logout', this.authListener);
    },
    
    view: function(vnode) {
        // 动态选择使用优化版本还是标准版本
        const ListComponent = this.useOptimizedList ? OptimizedMemosList : MemosList;

        // 检查是否有传入的组件属性
        const hasComponentProp = vnode.attrs && vnode.attrs.component;

        return m('.app', [
            m(Header),
            m('main.main-content', [
                m('.container', [
                    // 如果有传入的组件属性，渲染该组件（用于时间记录页面）
                    hasComponentProp ? m(vnode.attrs.component) : [
                        m(LoginBanner),
                        m(PostSection),
                        // 添加搜索框
                        m(SearchBox, {
                            onSearch: (query) => this.handleSearch(query)
                        }),
                        m(ListComponent, {
                            oncreate: (vnode) => {
                                // 保存列表组件引用以便搜索
                                this.listComponentRef = vnode.state;
                            }
                        }),
                        // 优化版本不需要加载更多按钮（使用虚拟滚动）
                        !this.useOptimizedList ? m(LoadMoreButton) : null
                    ]
                ])
            ]),
            m(Footer),
            m(LoginModal),
            m(UpdateLogModal)
        ]);
    }
};