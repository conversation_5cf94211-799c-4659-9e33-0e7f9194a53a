<!DOCTYPE html>

<html lang="zh-CN">



<head>

    <meta charset="UTF-8">

    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <title>当记 - 内容详情</title>

    <link rel="stylesheet" href="css/main.css">

    <link rel="stylesheet" href="css/vendor/github.min.css">

    <script charset="UTF-8" id="LA_COLLECT" src="//sdk.51.la/js-sdk-pro.min.js"></script>

<script>LA.init({id:"Jqud4Iv4jX9nBtrP",ck:"Jqud4Iv4jX9nBtrP"})</script>

    <!-- 使用CDN链接替代本地文件 -->

    <script src="https://cdn.tailwindcss.com"></script>

    <script>

      // 配置Tailwind CSS以避免生产环境警告

      tailwind.config = {

        corePlugins: {

          preflight: false,

        }

      }

    </script>

    <script src="https://cdn.jsdelivr.net/npm/markdown-it@13.0.1/dist/markdown-it.min.js"></script>

    <script src="https://cdn.jsdelivr.net/npm/highlight.js@11.8.0/lib/index.min.js"></script>

    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>

</head>



<body class="main-container">

    <div class="content-wrapper">

        <!-- 内容页头部组件 -->

        <div id="contentHeaderComponent"></div>



        <!-- 内容主体组件 -->

        <div id="contentMainComponent"></div>



        <!-- 卡片生成模态窗组件 -->

        <div id="cardModalComponent"></div>

    </div>



    <!-- 初始化脚本 -->

    <script type="module">

        import { componentLoader } from './js/modules/componentLoader.js';



        // 定义组件配置

        const components = [

            { containerId: 'contentHeaderComponent', componentPath: 'components/content-header.html' },

            { containerId: 'contentMainComponent', componentPath: 'components/content-main.html' },

            { containerId: 'cardModalComponent', componentPath: 'components/card-modal.html' }

        ];



        // 页面加载完成后初始化组件

        document.addEventListener('DOMContentLoaded', async () => {

            try {

                console.log('开始加载内容页组件...');



                // 加载所有组件

                await componentLoader.loadComponents(components);

                console.log('所有组件加载完成');



                // 等待一小段时间确保DOM完全渲染

                await new Promise(resolve => setTimeout(resolve, 100));



                // 动态导入内容页应用模块

                await import('./js/content-app.js');

                console.log('内容页应用模块加载完成');



                // 手动触发应用初始化

                if (window.contentApp && typeof window.contentApp.init === 'function') {

                    console.log('手动初始化内容页应用...');

                    await window.contentApp.init();

                    console.log('内容页应用手动初始化完成');

                }



            } catch (error) {

                console.error('内容页组件加载失败:', error);



                // 显示错误信息

                document.body.innerHTML = `

                    <div class="error-container" style="display: flex; justify-content: center; align-items: center; height: 100vh; flex-direction: column;">

                        <h2 style="color: #ef4444; margin-bottom: 1rem;">内容页加载失败</h2>

                        <p style="color: #6b7280; margin-bottom: 2rem;">${error.message}</p>

                        <button onclick="location.reload()" style="padding: 0.5rem 1rem; background: #3b82f6; color: white; border: none; border-radius: 0.375rem; cursor: pointer;">

                            重新加载

                        </button>

                    </div>

                `;

            }

        });

    </script>

</body>



</html>