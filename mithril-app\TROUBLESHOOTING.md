# API故障排除指南

## 当前问题：Vika API 500错误

### 症状
- 时间记录API返回500 Internal Server Error
- 其他API（备忘录、用户）正常工作
- 浏览器控制台显示：`GET https://api.vika.cn/fusion/v1/datasheets/dsta8guxXYK3pCYsVe/records 500`

### 可能原因

#### 1. 视图ID配置问题
- 时间记录表使用的视图ID `viw5mLr0sA5d9` 可能已失效或权限不足
- 解决方法：检查Vika后台的视图配置

#### 2. 请求参数问题
- 排序参数 `sort=createdTime,desc` 可能不被支持
- 解决方法：已移除排序参数，简化请求

#### 3. 数据表配置问题
- 数据表ID `dsta8guxXYK3pCYsVe` 可能已变更
- 解决方法：确认数据表ID正确性

### 快速诊断

#### 浏览器控制台测试
在浏览器控制台中运行以下命令：

```javascript
// 快速测试所有API连接
await apiTester.quickTest();

// 获取详细错误信息
await apiTester.detailedDiagnosis();

// 单独测试时间记录API
await new TimeRecordService().testConnection();
```

#### 手动测试
1. 打开浏览器开发者工具 (F12)
2. 切换到Network标签
3. 刷新页面
4. 查看时间记录API请求的详细信息

### 解决方案

#### 方案1：更新视图配置
1. 登录Vika后台
2. 找到时间记录数据表
3. 检查并更新视图ID配置
4. 更新 `src/config/timeRecordConfig.js` 中的 `VIEW_ID`

#### 方案2：使用无视图请求
已实施：移除了视图ID参数，直接请求数据表数据

#### 方案3：检查API权限
1. 确认API Token有访问该数据表的权限
2. 检查数据表是否被删除或移动
3. 验证baseURL配置正确

### 配置文件检查

#### 时间记录配置 (`src/config/timeRecordConfig.js`)
```javascript
export const TIME_RECORD_API_CONFIG = {
  DATASHEET_ID: 'dsta8guxXYK3pCYsVe', // 确认此ID正确
  VIEW_ID: 'viw5mLr0sA5d9', // 可尝试移除或更新
  BASE_URL: 'https://api.vika.cn/fusion/v1',
  TOKEN: '你的token' // 确认token有效
};
```

### 调试步骤

1. **检查网络连接**
   - 确保能正常访问 `https://api.vika.cn`

2. **验证Token**
   - 在请求头中检查Authorization是否正确

3. **测试端点**
   - 使用curl测试：
   ```bash
   curl -H "Authorization: Bearer YOUR_TOKEN" \
        "https://api.vika.cn/fusion/v1/datasheets/dsta8guxXYK3pCYsVe/records?pageSize=1"
   ```

4. **检查响应**
   - 查看完整的错误响应体
   - 检查是否有更详细的错误信息

### 备用方案

如果API持续报错，应用会自动切换到离线模式：
- 时间记录功能仍可正常使用
- 数据仅存储在内存中
- 不会同步到云端

### 联系支持

如果以上方法都无法解决问题：
1. 收集完整的错误日志
2. 截图Vika后台的数据表配置
3. 联系技术支持并提供：
   - 数据表ID: dsta8guxXYK3pCYsVe
   - 错误时间
   - 完整的错误响应