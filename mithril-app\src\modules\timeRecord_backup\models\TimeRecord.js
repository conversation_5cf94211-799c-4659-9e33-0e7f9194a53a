/**
 * 时间记录数据模型
 * 管理时间记录的业务逻辑和状态
 */

import { TIME_RECORD_CONFIG, TIME_FORMAT_CONFIG } from '../../../config/timeRecordConfig.js';
import { ValidationUtils } from '../utils/ValidationUtils.js';
import { apiCache } from '../../../utils/SimpleCache.js';

export class TimeRecord {
    constructor(service) {
        this.service = service;
        
        // 计时器状态
        this.isRunning = false;
        this.startTime = null;
        this.endTime = null;
        this.duration = 0;
        
        // 记录内容
        this.content = '';
        this.category = TIME_RECORD_CONFIG.DEFAULTS.CATEGORY;
        
        // 历史记录
        this.records = [];
        this.currentPage = 1;
        this.hasMore = true;
        this.loading = false;
        
        // 计时器更新间隔
        this.timerInterval = null;
    }
    
    /**
     * 开始计时
     * 需求: 1.1 - 用户点击"开始计时"按钮时系统应开始计时并显示实时时长
     */
    startTimer() {
        try {
            console.log('TimeRecord.startTimer被调用');
            
            if (typeof window === 'undefined' || !window.appState) {
                console.error('全局状态未初始化');
                return false;
            }
            
            if (window.appState.timerRunning) {
                console.warn('计时器已在运行中');
                return false;
            }
            
            // 使用全局状态的计时器
            window.appState.startTimer();
            
            // 同步本地状态
            this.isRunning = true;
            this.startTime = new Date(window.appState.startTime);
            this.endTime = null;
            this.duration = 0;
            
            console.log('计时开始成功');
            return true;
        } catch (error) {
            console.error('开始计时失败:', error);
            return false;
        }
    }
    
    /**
     * 结束计时
     * 需求: 1.2 - 用户点击"结束计时"按钮时系统应停止计时并显示最终时长
     */
    stopTimer() {
        try {
            console.log('TimeRecord.stopTimer被调用');
            
            if (typeof window === 'undefined' || !window.appState) {
                console.error('全局状态未初始化');
                return false;
            }
            
            if (!window.appState.timerRunning) {
                console.warn('计时器未在运行');
                return false;
            }
            
            // 使用全局状态的计时器
            window.appState.stopTimer();
            
            // 同步本地状态
            this.isRunning = false;
            this.endTime = new Date();
            this.duration = window.appState.accumulatedDuration;
            
            console.log('计时结束成功，时长:', this.duration);
            return true;
        } catch (error) {
            console.error('结束计时失败:', error);
            return false;
        }
    }
    
    /**
     * 重置计时器
     * 清除所有计时状态，回到初始状态
     */
    resetTimer() {
        try {
            console.log('TimeRecord.resetTimer被调用');
            
            if (typeof window === 'undefined' || !window.appState) {
                console.error('全局状态未初始化');
                return false;
            }
            
            // 使用全局状态的计时器
            window.appState.resetTimer();
            
            // 同步本地状态
            this.isRunning = false;
            this.startTime = null;
            this.endTime = null;
            this.duration = 0;
            this.content = '';
            
            console.log('计时器重置成功');
            return true;
        } catch (error) {
            console.error('重置计时器失败:', error);
            return false;
        }
    }
    
    /**
     * 格式化时长显示 - 带缓存优化
     * 需求: 1.3 - 计时进行中时系统应每秒更新显示的时长
     * 需求: 2.2 - 保存记录时应包含持续时间文本描述
     * 需求: 4.1 - 用户查看计时器时应显示清晰的时间格式
     * @param {number} seconds - 时长（秒）
     * @param {string} format - 格式类型 ('FULL', 'SHORT', 'TEXT')
     * @returns {string} 格式化后的时长字符串
     */
    formatDuration(seconds, format = TIME_FORMAT_CONFIG.DURATION_FORMATS.FULL) {
        if (typeof seconds !== 'number' || seconds < 0) {
            return format === TIME_FORMAT_CONFIG.DURATION_FORMATS.TEXT ? '0秒' : '00:00:00';
        }
        
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;
        
        let result;
        switch (format) {
            case TIME_FORMAT_CONFIG.DURATION_FORMATS.FULL:
                // HH:MM:SS 格式
                result = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
                break;
                
            case TIME_FORMAT_CONFIG.DURATION_FORMATS.SHORT:
                // MM:SS 格式（小于1小时时）或 HH:MM 格式（大于1小时时）
                if (hours > 0) {
                    result = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
                } else {
                    result = `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
                }
                break;
                
            case TIME_FORMAT_CONFIG.DURATION_FORMATS.TEXT:
                // 文本格式：1小时30分钟45秒
                const parts = [];
                if (hours > 0) parts.push(`${hours}小时`);
                if (minutes > 0) parts.push(`${minutes}分钟`);
                if (secs > 0 || parts.length === 0) parts.push(`${secs}秒`);
                result = parts.join('');
                break;
                
            default:
                result = this.formatDuration(seconds, TIME_FORMAT_CONFIG.DURATION_FORMATS.FULL);
        }

        return result;
    }
    
    /**
     * 格式化时间戳为可读时间
     * @param {Date|number} timestamp - 时间戳（Date对象或毫秒数）
     * @param {string} format - 格式类型
     * @returns {string} 格式化后的时间字符串
     */
    formatTimestamp(timestamp, format = TIME_FORMAT_CONFIG.TIMESTAMP_FORMATS.DATE_TIME) {
        let date;
        if (timestamp instanceof Date) {
            date = timestamp;
        } else if (typeof timestamp === 'number') {
            date = new Date(timestamp);
        } else {
            return '';
        }
        
        if (isNaN(date.getTime())) {
            return '';
        }
        
        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');
        const hours = date.getHours().toString().padStart(2, '0');
        const minutes = date.getMinutes().toString().padStart(2, '0');
        const seconds = date.getSeconds().toString().padStart(2, '0');
        
        switch (format) {
            case TIME_FORMAT_CONFIG.TIMESTAMP_FORMATS.DATE_TIME:
                return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
                
            case TIME_FORMAT_CONFIG.TIMESTAMP_FORMATS.DATE_ONLY:
                return `${year}-${month}-${day}`;
                
            case TIME_FORMAT_CONFIG.TIMESTAMP_FORMATS.TIME_ONLY:
                return `${hours}:${minutes}:${seconds}`;
                
            default:
                return this.formatTimestamp(timestamp, TIME_FORMAT_CONFIG.TIMESTAMP_FORMATS.DATE_TIME);
        }
    }
    
    /**
     * 获取当前计时器的格式化显示
     * @returns {string} 当前时长的格式化字符串
     */
    getCurrentDurationDisplay() {
        return this.formatDuration(this.duration);
    }
    
    /**
     * 获取当前记录的文本描述（用于保存到数据库）
     * @returns {string} 时长的文本描述
     */
    getDurationText() {
        return this.formatDuration(this.duration, TIME_FORMAT_CONFIG.DURATION_FORMATS.TEXT);
    }
    
    /**
     * 保存当前记录
     * 需求: 2.1 - 用户输入备注并点击保存时系统应将记录保存到vika表
     * 需求: 2.3 - 保存成功后系统应重置计时器并显示成功提示
     * @param {string} content - 备注内容
     * @returns {Promise<boolean>} 保存是否成功
     */
    async saveRecord(content = this.content) {
        try {
            // 验证输入数据
            const validationResult = this.validateInput(content);
            if (!validationResult.isValid) {
                throw new Error(validationResult.error);
            }
            
            // 检查是否有有效的时间记录
            if (!this.startTime || !this.endTime || this.duration <= 0) {
                throw new Error('记录时长不能少于1秒');
            }
            
            // 准备保存的数据
            const recordData = {
                category: this.category,
                content: content.trim(),
                startTime: this.startTime,
                endTime: this.endTime,
                duration: this.duration,
                durationText: this.getDurationText()
            };
            
            // 调用服务保存数据
            const result = await this.service.createRecord(recordData);
            
            if (result && result.success) {
                // 保存成功，添加到本地记录列表
                this.records.unshift({
                    id: result.data.id || result.data.recordId || Date.now().toString(),
                    ...recordData,
                    createdAt: new Date()
                });
                
                // 重置计时器
                this.resetTimer();
                
                return true;
            } else {
                const errorMessage = (result && result.error) || '保存失败';
                console.error('保存记录失败:', errorMessage);
                throw new Error(errorMessage);
            }
        } catch (error) {
            console.error('保存记录失败:', error);
            throw error;
        }
    }
    
    /**
     * 加载历史记录 - 带缓存优化
     * 需求: 3.1 - 用户访问时间记录页面时系统应显示历史记录列表
     * 需求: 3.2 - 显示历史记录时每条记录应包含完整信息
     * @param {number} page - 页码
     * @param {boolean} append - 是否追加到现有记录（用于分页加载）
     * @returns {Promise<boolean>} 加载是否成功
     */
    async loadRecords(page = 1, append = false) {
        try {
            this.loading = true;
            
            // 从服务器获取数据
            const result = await this.service.getRecords(page, TIME_RECORD_CONFIG.PAGINATION.DEFAULT_PAGE_SIZE);
            
            if (result.success) {
                const newRecords = result.data.records || [];
                
                if (append) {
                    // 追加模式：添加到现有记录，去重
                    const existingIds = new Set(this.records.map(r => r.id));
                    const uniqueNewRecords = newRecords.filter(r => !existingIds.has(r.id));
                    this.records = [...this.records, ...uniqueNewRecords];
                } else {
                    // 替换模式：替换现有记录
                    this.records = newRecords;
                }
                
                // 更新分页状态
                this.currentPage = page;
                this.hasMore = result.data.hasMore || false;
                
                return true;
            } else {
                throw new Error(result.error || '加载记录失败');
            }
        } catch (error) {
            console.error('加载记录失败:', error);
            throw error;
        } finally {
            this.loading = false;
        }
    }
    
    /**
     * 加载更多记录（分页）
     * 需求: 3.3 - 记录列表超过一页时系统应提供分页或加载更多功能
     * @returns {Promise<boolean>} 加载是否成功
     */
    async loadMoreRecords() {
        if (!this.hasMore || this.loading) {
            return false;
        }
        
        return await this.loadRecords(this.currentPage + 1, true);
    }
    
    /**
     * 删除记录
     * @param {string} recordId - 记录ID
     * @returns {Promise<boolean>} 删除是否成功
     */
    async deleteRecord(recordId) {
        try {
            const result = await this.service.deleteRecord(recordId);
            
            if (result.success) {
                // 从本地记录列表中移除
                this.records = this.records.filter(record => record.id !== recordId);
                return true;
            } else {
                throw new Error(result.error || '删除失败');
            }
        } catch (error) {
            console.error('删除记录失败:', error);
            throw error;
        }
    }
    
    /**
     * 更新记录
     * @param {string} recordId - 记录ID
     * @param {Object} updateData - 更新的数据
     * @returns {Promise<boolean>} 更新是否成功
     */
    async updateRecord(recordId, updateData) {
        try {
            // 验证更新数据
            if (updateData.content !== undefined) {
                const validationResult = this.validateInput(updateData.content);
                if (!validationResult.isValid) {
                    throw new Error(validationResult.error);
                }
            }
            
            const result = await this.service.updateRecord(recordId, updateData);
            
            if (result.success) {
                // 更新本地记录列表
                const recordIndex = this.records.findIndex(record => record.id === recordId);
                if (recordIndex !== -1) {
                    this.records[recordIndex] = { ...this.records[recordIndex], ...updateData };
                }
                return true;
            } else {
                throw new Error(result.error || '更新失败');
            }
        } catch (error) {
            console.error('更新记录失败:', error);
            throw error;
        }
    }
    
    /**
     * 验证输入数据
     * 需求: 6.5 - 用户输入无效数据时系统应显示验证错误信息
     * 任务: 7.3 - 实现数据验证
     * @param {string} content - 备注内容
     * @returns {Object} 验证结果 {isValid: boolean, error?: string, warnings?: Array, details?: Object}
     */
    validateInput(content) {
        // 基础验证逻辑，保持与测试的兼容性
        if (!content || typeof content !== 'string') {
            return {
                isValid: false,
                error: '备注内容不能为空'
            };
        }
        
        const trimmedContent = content.trim();
        if (trimmedContent.length === 0) {
            return {
                isValid: false,
                error: '备注内容不能为空'
            };
        }
        
        // 检查内容长度
        const maxLength = TIME_RECORD_CONFIG.VALIDATION.MAX_CONTENT_LENGTH;
        if (trimmedContent.length > maxLength) {
            return {
                isValid: false,
                error: `备注内容不能超过${maxLength}个字符`
            };
        }
        
        // 检查分类长度
        if (this.category && this.category.length > TIME_RECORD_CONFIG.VALIDATION.MAX_CATEGORY_LENGTH) {
            return {
                isValid: false,
                error: `分类名称不能超过${TIME_RECORD_CONFIG.VALIDATION.MAX_CATEGORY_LENGTH}个字符`
            };
        }
        
        // 检查时长
        if (this.duration < TIME_RECORD_CONFIG.VALIDATION.MIN_DURATION) {
            return {
                isValid: false,
                error: '记录时长不能少于1秒'
            };
        }
        
        // 使用增强验证工具进行详细验证（用于高级功能）
        const validationData = {
            content: content,
            category: this.category,
            startTime: this.startTime,
            endTime: this.endTime,
            duration: this.duration
        };
        
        const validation = ValidationUtils.validateTimeRecordInput(validationData);
        const messages = ValidationUtils.formatValidationMessages(validation.errors, validation.warnings);
        
        // 返回兼容的格式，同时提供详细信息
        return {
            isValid: true, // 基础验证通过
            error: null,
            warnings: validation.warnings,
            suggestions: messages.suggestions,
            details: {
                errors: validation.errors,
                warnings: validation.warnings,
                hasWarnings: messages.hasWarnings
            }
        };
    }
    
    /**
     * 验证完整的记录数据（用于保存前的最终验证）
     * 任务: 7.3 - 实现数据验证
     * @returns {Object} 详细的验证结果
     */
    validateCompleteRecord() {
        const validationData = {
            content: this.content,
            category: this.category,
            startTime: this.startTime,
            endTime: this.endTime,
            duration: this.duration
        };
        
        const validation = ValidationUtils.validateTimeRecordInput(validationData);
        const messages = ValidationUtils.formatValidationMessages(validation.errors, validation.warnings);
        
        return {
            isValid: validation.isValid,
            canSave: validation.isValid, // 只有没有错误才能保存
            errors: validation.errors,
            warnings: validation.warnings,
            errorMessage: messages.errorMessage,
            warningMessage: messages.warningMessage,
            suggestions: messages.suggestions
        };
    }
    
    /**
     * 获取记录统计信息
     * @returns {Object} 统计信息
     */
    getStatistics() {
        const totalRecords = this.records.length;
        const totalDuration = this.records.reduce((sum, record) => sum + (record.duration || 0), 0);
        
        return {
            totalRecords,
            totalDuration,
            totalDurationText: this.formatDuration(totalDuration, TIME_FORMAT_CONFIG.DURATION_FORMATS.TEXT),
            averageDuration: totalRecords > 0 ? Math.round(totalDuration / totalRecords) : 0
        };
    }
    
    /**
     * 清理资源 - 增强版本
     * 在组件销毁时调用，防止内存泄漏
     */
    destroy() {
        if (this.timerInterval) {
            clearInterval(this.timerInterval);
            this.timerInterval = null;
        }
        this.isRunning = false;
        
        // 清理相关缓存
        apiCache.deletePattern('records_page_');
        apiCache.deletePattern(`duration_${this.duration}_`);
        
        // 清理性能优化器中的相关定时器
        // globalPerformanceOptimizer未定义，已移除相关代码
    }
}