/**
 * @fileoverview HTML模块加载器
 * @description 提供动态加载HTML组件模块的功能
 */

/**
 * 模块加载器类
 */
export class ModuleLoader {
    /**
     * 加载HTML文件并插入到指定容器
     * @param {string} url - HTML文件路径
     * @param {string} containerId - 目标容器ID
     * @returns {Promise<void>}
     */
    static async loadModule(url, containerId) {
        try {
            const response = await fetch(url);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const html = await response.text();
            const container = document.getElementById(containerId);
            if (container) {
                container.innerHTML = html;
            } else {
                console.warn(`容器 ${containerId} 未找到`);
            }
        } catch (error) {
            console.error(`加载模块失败: ${url}`, error);
        }
    }

    /**
     * 批量加载多个模块
     * @param {Array<{url: string, containerId: string}>} modules - 模块配置数组
     * @returns {Promise<void[]>}
     */
    static async loadModules(modules) {
        const promises = modules.map(module => 
            this.loadModule(module.url, module.containerId)
        );
        return Promise.all(promises);
    }

    /**
     * 初始化应用模块
     */
    static async initApp() {
        const modules = [
            { url: './components/header.html', containerId: 'headerContainer' },
            { url: './components/login-modal.html', containerId: 'loginModalContainer' },
            { url: './components/post-section.html', containerId: 'postSectionContainer' },
            { url: './components/memos-list.html', containerId: 'memosListContainer' },
            { url: './components/update-log-modal.html', containerId: 'updateModalContainer' }
        ];

        await this.loadModules(modules);
    }
}