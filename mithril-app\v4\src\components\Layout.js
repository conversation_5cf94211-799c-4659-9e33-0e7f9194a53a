// 使用全局的 Mithril 实例而不是导入
const m = window.m;

// 使用相对路径导入组件
import { Header } from './Header.js';
import { LoginBanner } from './LoginBanner.js';
import { PostSection } from './PostSection.js';
import { MemosList } from './MemosList.js';
import { OptimizedMemosList } from './OptimizedMemosList.js';
import { LoadMoreButton } from './LoadMoreButton.js';
import { Footer } from './Footer.js';
import { LoginModal } from './LoginModal.js';
import { UpdateLogModal } from './UpdateLogModal.js';
// 移除性能监控依赖

export const Layout = {
    oninit: function(vnode) {
        // 检测是否使用优化版本
        this.useOptimizedList = window.memos.list.length > 20 || window.location.search.includes('optimized=true');
        
        // 初始化时加载数据
        window.memos.loadMemos(1);
        
        // 监听认证状态变化事件
        this.authListener = (e) => {
            // 认证状态改变时重新加载内容
            window.memos.list = [];
            window.memos.currentPage = 1;
            window.memos.hasMore = true;
            window.memos.loadMemos(1);
        };
        
        window.addEventListener('auth:login', this.authListener);
        window.addEventListener('auth:logout', this.authListener);
    },
    
    onremove: function(vnode) {
        // 移除事件监听器
        window.removeEventListener('auth:login', this.authListener);
        window.removeEventListener('auth:logout', this.authListener);
    },
    
    view: function(vnode) {
        // 动态选择使用优化版本还是标准版本
        const ListComponent = this.useOptimizedList ? OptimizedMemosList : MemosList;
        
        return m('.app', [
            m(Header),
            m('main.main-content', [
                m('.container', [
                    // 如果有子组件，渲染子组件（用于时间记录页面）
                    Array.isArray(vnode.children) && vnode.children.length > 0 && vnode.children[0] ? vnode.children : [
                        m(LoginBanner),
                        m(PostSection),
                        m(ListComponent),
                        // 优化版本不需要加载更多按钮（使用虚拟滚动）
                        !this.useOptimizedList ? m(LoadMoreButton) : null
                    ]
                ])
            ]),
            m(Footer),
            m(LoginModal),
            m(UpdateLogModal)
        ]);
    }
};