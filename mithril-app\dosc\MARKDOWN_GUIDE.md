# Markdown 支持指南

## 概述

当记应用现已完全支持 Markdown 语法，让你能够更丰富地表达想法和格式化内容。

## 支持的 Markdown 语法

### 标题
```markdown
# 一级标题
## 二级标题
### 三级标题
#### 四级标题
##### 五级标题
###### 六级标题
```

### 文本格式
```markdown
**粗体文本**
*斜体文本*
~~删除线文本~~
`行内代码`
```

### 代码块
````markdown
```javascript
function hello() {
    console.log("Hello, World!");
}
```
````

### 链接和图片
```markdown
[链接文本](https://example.com)
![图片描述](https://example.com/image.jpg)
```

### 列表
```markdown
- 无序列表项 1
- 无序列表项 2
  - 嵌套项目

1. 有序列表项 1
2. 有序列表项 2
```

### 引用
```markdown
> 这是一个引用块
> 可以包含多行内容
```

### 分割线
```markdown
---
```

### 表格
```markdown
| 列标题1 | 列标题2 | 列标题3 |
|---------|---------|---------|
| 数据1   | 数据2   | 数据3   |
| 数据4   | 数据5   | 数据6   |
```

## 编辑器功能

### 工具栏
- **𝐁** - 粗体 (Ctrl+B)
- **𝐼** - 斜体 (Ctrl+I)
- **̶S̶** - 删除线
- **</>** - 行内代码
- **{ }** - 代码块
- **🔗** - 链接 (Ctrl+K)
- **H** - 标题
- **•** - 列表
- **"** - 引用
- **👁️** - 预览模式切换

### 键盘快捷键
- `Ctrl+B` / `Cmd+B` - 粗体
- `Ctrl+I` / `Cmd+I` - 斜体
- `Ctrl+K` / `Cmd+K` - 插入链接
- `Ctrl+Enter` / `Cmd+Enter` - 快速发布
- `Tab` - 插入缩进

### 实时预览
点击工具栏中的眼睛图标 👁️ 可以在编辑模式和预览模式之间切换，实时查看 Markdown 渲染效果。

## 性能优化

### 自动优化
- **内容缓存**: 已渲染的 Markdown 内容会被缓存，避免重复解析
- **懒加载**: 图片和媒体文件采用懒加载技术，提升页面加载速度
- **虚拟滚动**: 大量内容时自动启用虚拟滚动，保持流畅体验
- **防抖处理**: 输入时使用防抖技术，减少不必要的重新渲染

### 手动优化
在 URL 中添加参数可以启用特定优化：
- `?optimized=true` - 强制启用优化版本
- `?debug=true` - 启用性能监控

## 最佳实践

### 内容组织
1. **使用标题结构化内容**
   ```markdown
   # 主题
   ## 子主题
   ### 详细内容
   ```

2. **合理使用列表**
   ```markdown
   ## 待办事项
   - [ ] 完成项目文档
   - [ ] 代码审查
   - [x] 已完成的任务
   ```

3. **代码分享**
   ````markdown
   ## 代码示例
   ```javascript
   // 添加语言标识符以获得语法高亮
   const greeting = "Hello, World!";
   console.log(greeting);
   ```
   ````

### 性能考虑
1. **避免过长的单个段落** - 使用换行和分段提高可读性
2. **合理使用图片** - 大图片会影响加载速度
3. **代码块不要过长** - 长代码建议使用外部链接

### 兼容性
- 支持标准 Markdown 语法
- 兼容 GitHub Flavored Markdown (GFM) 的大部分特性
- 自动处理 URL 链接
- 支持 HTML 实体转义

## 故障排除

### 常见问题

**Q: 为什么我的 Markdown 没有正确渲染？**
A: 检查语法是否正确，特别注意空格和换行。某些语法需要前后有空行。

**Q: 图片无法显示怎么办？**
A: 确保图片 URL 可访问，支持 HTTPS 协议。

**Q: 代码块没有语法高亮？**
A: 在代码块开头添加语言标识符，如 ```javascript。

**Q: 预览模式下内容显示异常？**
A: 尝试刷新页面或切换回编辑模式再切换到预览模式。

### 性能问题

**Q: 输入时感觉卡顿？**
A: 这可能是由于内容过多导致的。尝试：
1. 分段发布内容
2. 在 URL 中添加 `?optimized=true`
3. 清除浏览器缓存

**Q: 页面加载缓慢？**
A: 检查网络连接，大量图片可能影响加载速度。

## 更新日志

### v1.0.0
- ✅ 完整的 Markdown 语法支持
- ✅ 实时预览功能
- ✅ 工具栏和快捷键
- ✅ 性能优化和缓存
- ✅ 响应式设计
- ✅ 懒加载和虚拟滚动

## 技术细节

### 架构
- **解析器**: 自定义轻量级 Markdown 解析器
- **渲染器**: 高性能 HTML 渲染引擎
- **缓存**: 智能内容缓存系统
- **优化**: 虚拟滚动和懒加载技术

### 浏览器支持
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

### 移动端优化
- 触摸友好的工具栏
- 自适应布局
- 优化的滚动性能
- 减少动画以节省电量

---

如有问题或建议，请通过应用内反馈功能联系我们。