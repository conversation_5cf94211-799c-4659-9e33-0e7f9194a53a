/**

 * CSS变量设计系统

 * 定义全局设计令牌

 */



:root {

  /* 主色调 */

  --color-primary: #3B82F6;

  --color-primary-hover: #2563EB;

  --color-secondary: #8B5CF6;

  --color-secondary-hover: #7C3AED;

  --color-accent: #10B981;

  --color-accent-hover: #059669;

  

  /* 中性色 */

  --color-gray-50: #F9FAFB;

  --color-gray-100: #F3F4F6;

  --color-gray-200: #E5E7EB;

  --color-gray-300: #D1D5DB;

  --color-gray-400: #9CA3AF;

  --color-gray-500: #6B7280;

  --color-gray-600: #4B5563;

  --color-gray-700: #374151;

  --color-gray-800: #1F2937;

  --color-gray-900: #111827;

  

  /* 状态色 */

  --color-success: #10B981;

  --color-success-hover: #059669;

  --color-warning: #F59E0B;

  --color-warning-hover: #D97706;

  --color-error: #EF4444;

  --color-error-hover: #DC2626;

  --color-info: #3B82F6;

  --color-info-hover: #2563EB;

  

  /* 字体 */

  --font-sans: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;

  --font-mono: 'JetBrains Mono', 'Fira Code', 'SF Mono', Monaco, Consolas, monospace;

  

  /* 字体大小 */

  --text-xs: 0.75rem;

  --text-sm: 0.875rem;

  --text-base: 1rem;

  --text-lg: 1.125rem;

  --text-xl: 1.25rem;

  --text-2xl: 1.5rem;

  --text-3xl: 1.875rem;

  --text-4xl: 2.25rem;

  

  /* 间距 */

  --space-1: 0.25rem;

  --space-2: 0.5rem;

  --space-3: 0.75rem;

  --space-4: 1rem;

  --space-5: 1.25rem;

  --space-6: 1.5rem;

  --space-8: 2rem;

  --space-10: 2.5rem;

  --space-12: 3rem;

  --space-16: 4rem;

  --space-20: 5rem;

  

  /* 圆角 */

  --radius-sm: 0.25rem;

  --radius-md: 0.375rem;

  --radius-lg: 0.5rem;

  --radius-xl: 0.75rem;

  --radius-2xl: 1rem;

  --radius-full: 9999px;

  

  /* 阴影 */

  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);

  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);

  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

  

  /* 动画 */

  --duration-fast: 150ms;

  --duration-normal: 250ms;

  --duration-slow: 350ms;

  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);

  --ease-out: cubic-bezier(0, 0, 0.2, 1);

  --ease-in: cubic-bezier(0.4, 0, 1, 1);

  

  /* Z-index */

  --z-dropdown: 1000;

  --z-sticky: 1020;

  --z-fixed: 1030;

  --z-modal-backdrop: 1040;

  --z-modal: 1050;

  --z-popover: 1060;

  --z-tooltip: 1070;

  --z-toast: 1080;

}



/* 深色主题变量 */

.dark {

  --color-bg: var(--color-gray-900);

  --color-bg-secondary: var(--color-gray-800);

  --color-bg-tertiary: var(--color-gray-700);

  --color-text: var(--color-gray-100);

  --color-text-secondary: var(--color-gray-300);

  --color-text-tertiary: var(--color-gray-400);

  --color-border: var(--color-gray-600);

  --color-border-light: var(--color-gray-700);

}



/* 浅色主题变量 */

.light {

  --color-bg: white;

  --color-bg-secondary: var(--color-gray-50);

  --color-bg-tertiary: var(--color-gray-100);

  --color-text: var(--color-gray-900);

  --color-text-secondary: var(--color-gray-700);

  --color-text-tertiary: var(--color-gray-500);

  --color-border: var(--color-gray-200);

  --color-border-light: var(--color-gray-100);

}



/* 护眼主题变量 */

.eye-protect {

  --color-bg: #F5F7E7;

  --color-bg-secondary: #EDEFD9;

  --color-bg-tertiary: #E5E7CB;

  --color-text: #2D3748;

  --color-text-secondary: #4A5568;

  --color-text-tertiary: #718096;

  --color-border: #CBD5E0;

  --color-border-light: #E2E8F0;

  --color-primary: #38A169;

  --color-primary-hover: #2F855A;

}