/* Markdown 编辑器样式 */
.markdown-editor {
    border: var(--border-width) solid var(--border);
    border-radius: var(--radius);
    background: var(--background);
    overflow: hidden;
    transition: var(--transition);
}

.markdown-editor:focus-within {
    border-color: var(--primary);
    box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1);
}

/* 工具栏 */
.markdown-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-xs) var(--space-sm);
    background: var(--surface);
    border-bottom: var(--border-width) solid var(--border);
    gap: var(--space-xs);
}

.toolbar-group {
    display: flex;
    gap: 2px;
    align-items: center;
}

.toolbar-btn {
    padding: 6px 8px;
    border: none;
    background: transparent;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    color: var(--secondary);
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 28px;
    height: 28px;
}

.toolbar-btn:hover {
    background: var(--background);
    color: var(--primary);
}

.toolbar-btn.active {
    background: var(--primary);
    color: var(--background);
}

.preview-toggle {
    font-size: 16px;
}

/* 编辑器内容区域 */
.editor-content {
    position: relative;
    min-height: 100px;
}

.markdown-textarea {
    width: 100%;
    border: none;
    outline: none;
    padding: var(--space-sm);
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    font-size: var(--font-size-base);
    line-height: 1.6;
    resize: vertical;
    background: transparent;
    color: var(--primary);
    word-wrap: break-word;
    overflow-wrap: break-word;
}

.markdown-textarea::placeholder {
    color: var(--muted);
}

/* 预览区域 */
.markdown-preview {
    padding: var(--space-sm);
    min-height: 100px;
    line-height: 1.6;
    color: var(--primary);
    word-wrap: break-word;
    overflow-wrap: break-word;
}

.preview-empty {
    color: var(--muted);
    font-style: italic;
    text-align: center;
    padding: var(--space-lg);
}

/* 状态栏 */
.editor-status {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-xs) var(--space-sm);
    background: var(--surface);
    border-top: var(--border-width) solid var(--border);
    font-size: var(--font-size-sm);
    color: var(--muted);
}

.status-info {
    display: flex;
    gap: var(--space-sm);
}

.status-tips {
    font-size: 11px;
}

/* Markdown 渲染样式 */

/* 通用 blockquote 样式 - 确保在所有容器中都能正确显示 */
.markdown-preview blockquote,
.markdown-rendered blockquote,
blockquote {
    position: relative !important;
    margin: var(--space-md) 0 !important;
    padding: var(--space-md) var(--space-md) var(--space-md) calc(var(--space-md) + 8px) !important;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%) !important;
    border-radius: var(--radius) !important;
    color: var(--secondary) !important;
    font-style: italic !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05) !important;
    border: 1px solid rgba(0, 0, 0, 0.08) !important;
}

.markdown-preview blockquote::before,
.markdown-rendered blockquote::before,
blockquote::before {
    content: '' !important;
    position: absolute !important;
    left: 0 !important;
    top: 0 !important;
    bottom: 0 !important;
    width: 6px !important;
    background: linear-gradient(180deg, #3b82f6 0%, #1d4ed8 100%) !important;
    border-radius: 3px 0 0 3px !important;
    box-shadow: 0 0 8px rgba(59, 130, 246, 0.3) !important;
}

.markdown-preview h1,
.markdown-preview h2,
.markdown-preview h3,
.markdown-preview h4,
.markdown-preview h5,
.markdown-preview h6 {
    margin: var(--space-md) 0 var(--space-sm) 0;
    font-weight: 600;
    line-height: 1.3;
    color: var(--primary);
}

.markdown-preview h1 { font-size: 1.8em; border-bottom: 2px solid var(--border); padding-bottom: var(--space-xs); }
.markdown-preview h2 { font-size: 1.5em; border-bottom: 1px solid var(--border); padding-bottom: var(--space-xs); }
.markdown-preview h3 { font-size: 1.3em; }
.markdown-preview h4 { font-size: 1.1em; }
.markdown-preview h5 { font-size: 1em; }
.markdown-preview h6 { font-size: 0.9em; color: var(--secondary); }

.markdown-preview p {
    margin: var(--space-sm) 0;
    line-height: 1.6;
}

.markdown-preview strong {
    font-weight: 600;
    color: var(--primary);
}

.markdown-preview em {
    font-style: italic;
    color: var(--secondary);
}

.markdown-preview del {
    text-decoration: line-through;
    color: var(--muted);
}

.markdown-preview code {
    background: var(--surface);
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    font-size: 0.9em;
    color: var(--primary);
    border: 1px solid var(--border);
}

.markdown-preview pre {
    background: var(--surface);
    border: var(--border-width) solid var(--border);
    border-radius: var(--radius);
    padding: var(--space-sm);
    margin: var(--space-sm) 0;
    overflow-x: auto;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    font-size: 0.9em;
    line-height: 1.4;
}

.markdown-preview pre code {
    background: none;
    border: none;
    padding: 0;
    font-size: inherit;
}

.markdown-preview blockquote {
    position: relative;
    margin: var(--space-md) 0;
    padding: var(--space-md) var(--space-md) var(--space-md) calc(var(--space-md) + 8px);
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: var(--radius);
    color: var(--secondary);
    font-style: italic;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.08);
}

.markdown-preview blockquote::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 6px;
    background: linear-gradient(180deg, #3b82f6 0%, #1d4ed8 100%);
    border-radius: 3px 0 0 3px;
    box-shadow: 0 0 8px rgba(59, 130, 246, 0.3);
}

.markdown-preview ul,
.markdown-preview ol {
    margin: var(--space-sm) 0;
    padding-left: var(--space-lg);
}

.markdown-preview li {
    margin: var(--space-xs) 0;
    line-height: 1.5;
}

.markdown-preview a {
    color: var(--primary);
    text-decoration: underline;
    word-wrap: break-word;
    word-break: break-all;
    overflow-wrap: break-word;
}

.markdown-preview a:hover {
    opacity: 0.8;
}

.markdown-preview img {
    max-width: 100%;
    height: auto;
    border-radius: var(--radius);
    border: var(--border-width) solid var(--border);
    margin: var(--space-xs) 0;
}

.markdown-preview hr {
    border: none;
    border-top: 2px solid var(--border);
    margin: var(--space-lg) 0;
}

/* 代码块语言标签 */
.code-block {
    position: relative;
}

.code-block[data-lang]:not([data-lang=""]):before {
    content: attr(data-lang);
    position: absolute;
    top: var(--space-xs);
    right: var(--space-xs);
    background: var(--border);
    color: var(--secondary);
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    text-transform: uppercase;
    font-weight: 500;
}

/* 表格样式 */
.markdown-preview .markdown-table {
    border-collapse: collapse;
    width: 100%;
    margin: var(--space-md) 0;
    border-radius: var(--radius);
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid var(--border);
    background: var(--background);
}

.markdown-preview .markdown-table th {
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    color: var(--primary);
    font-weight: 600;
    padding: var(--space-sm) var(--space-md);
    text-align: left;
    border-bottom: 2px solid var(--border);
    font-size: var(--font-size-sm);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.markdown-preview .markdown-table td {
    padding: var(--space-sm) var(--space-md);
    border-bottom: 1px solid var(--border);
    color: var(--primary);
    line-height: 1.5;
}

.markdown-preview .markdown-table tbody tr:hover {
    background: var(--surface);
    transition: background-color 0.2s ease;
}

.markdown-preview .markdown-table tbody tr:nth-child(even) {
    background: rgba(248, 250, 252, 0.5);
}

.markdown-preview .markdown-table tbody tr:nth-child(even):hover {
    background: var(--surface);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .markdown-toolbar {
        flex-wrap: wrap;
        gap: var(--space-xs);
    }
    
    .toolbar-group {
        gap: 1px;
    }
    
    .toolbar-btn {
        min-width: 24px;
        height: 24px;
        padding: 4px 6px;
        font-size: 12px;
    }
    
    .editor-status {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--space-xs);
    }
    
    .status-tips {
        display: none; /* 在移动端隐藏快捷键提示 */
    }
    
    .markdown-preview pre {
        font-size: 0.8em;
        overflow-x: auto;
    }
    
    .markdown-preview h1 { font-size: 1.5em; }
    .markdown-preview h2 { font-size: 1.3em; }
    .markdown-preview h3 { font-size: 1.2em; }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
    :root {
        --primary: #fff;
        --secondary: #ccc;
        --muted: #999;
        --border: #333;
        --background: #1a1a1a;
        --surface: #2a2a2a;
    }
    
    .markdown-preview code {
        background: #2a2a2a;
        color: #e6e6e6;
    }
    
    .markdown-preview pre {
        background: #2a2a2a;
        color: #e6e6e6;
    }
    
    .code-block[data-lang]:not([data-lang=""]):before {
        background: #333;
        color: #ccc;
    }
    
    /* 暗色主题下的引用样式 */
    .markdown-preview blockquote {
        background: linear-gradient(135deg, #2a2a2a 0%, #1e1e1e 100%);
        border-color: rgba(255, 255, 255, 0.1);
        color: #ccc;
    }
    
    .markdown-preview blockquote::before {
        background: linear-gradient(180deg, #60a5fa 0%, #3b82f6 100%);
        box-shadow: 0 0 8px rgba(96, 165, 250, 0.3);
    }
    
    /* 暗色主题下的表格样式 */
    .markdown-preview .markdown-table {
        background: #1a1a1a;
        border-color: #333;
    }
    
    .markdown-preview .markdown-table th {
        background: linear-gradient(135deg, #2a2a2a 0%, #1e1e1e 100%);
        color: #fff;
        border-bottom-color: #333;
    }
    
    .markdown-preview .markdown-table td {
        border-bottom-color: #333;
        color: #ccc;
    }
    
    .markdown-preview .markdown-table tbody tr:hover {
        background: #2a2a2a;
    }
    
    .markdown-preview .markdown-table tbody tr:nth-child(even) {
        background: rgba(42, 42, 42, 0.3);
    }
}

/* 动画效果 */
.markdown-editor.preview-mode .editor-content {
    animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(4px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 滚动条样式 */
.markdown-textarea::-webkit-scrollbar,
.markdown-preview::-webkit-scrollbar {
    width: 6px;
}

.markdown-textarea::-webkit-scrollbar-track,
.markdown-preview::-webkit-scrollbar-track {
    background: var(--surface);
}

.markdown-textarea::-webkit-scrollbar-thumb,
.markdown-preview::-webkit-scrollbar-thumb {
    background: var(--border);
    border-radius: 3px;
}

.markdown-textarea::-webkit-scrollbar-thumb:hover,
.markdown-preview::-webkit-scrollbar-thumb:hover {
    background: var(--secondary);
}