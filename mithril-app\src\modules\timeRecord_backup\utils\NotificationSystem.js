/**
 * 用户操作反馈通知系统
 * 需求: 6.4, 6.5 - 添加操作成功的提示消息，实现数据保存状态的视觉反馈，添加加载状态指示器
 */

// 移除国际化依赖，使用简单的中文文本

// 通知类型
export const NOTIFICATION_TYPES = {
    SUCCESS: 'success',
    ERROR: 'error',
    WARNING: 'warning',
    INFO: 'info',
    LOADING: 'loading'
};

// 通知位置
export const NOTIFICATION_POSITIONS = {
    TOP_RIGHT: 'top-right',
    TOP_LEFT: 'top-left',
    TOP_CENTER: 'top-center',
    BOTTOM_RIGHT: 'bottom-right',
    BOTTOM_LEFT: 'bottom-left',
    BOTTOM_CENTER: 'bottom-center'
};

export class NotificationSystem {
    constructor() {
        this.notifications = [];
        this.listeners = [];
        this.nextId = 1;
        this.defaultDuration = 5000; // 5秒
        this.maxNotifications = 5;
        this.position = NOTIFICATION_POSITIONS.TOP_RIGHT;
        
        // 初始化通知容器
        this.initNotificationContainer();
    }
    
    /**
     * 初始化通知容器
     */
    initNotificationContainer() {
        if (typeof document !== 'undefined') {
            // 创建通知容器
            this.container = document.createElement('div');
            this.container.className = 'notification-container';
            this.container.setAttribute('data-position', this.position);
            document.body.appendChild(this.container);
            
            // 添加基础样式
            this.addNotificationStyles();
        }
    }
    
    /**
     * 添加通知样式
     */
    addNotificationStyles() {
        if (typeof document !== 'undefined' && !document.getElementById('notification-styles')) {
            const style = document.createElement('style');
            style.id = 'notification-styles';
            style.textContent = `
                .notification-container {
                    position: fixed;
                    z-index: 10000;
                    pointer-events: none;
                    max-width: 400px;
                }
                
                .notification-container[data-position="top-right"] {
                    top: 20px;
                    right: 20px;
                }
                
                .notification-container[data-position="top-left"] {
                    top: 20px;
                    left: 20px;
                }
                
                .notification-container[data-position="top-center"] {
                    top: 20px;
                    left: 50%;
                    transform: translateX(-50%);
                }
                
                .notification-container[data-position="bottom-right"] {
                    bottom: 20px;
                    right: 20px;
                }
                
                .notification-container[data-position="bottom-left"] {
                    bottom: 20px;
                    left: 20px;
                }
                
                .notification-container[data-position="bottom-center"] {
                    bottom: 20px;
                    left: 50%;
                    transform: translateX(-50%);
                }
                
                .notification {
                    pointer-events: auto;
                    margin-bottom: 10px;
                    padding: 12px 16px;
                    border-radius: 8px;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                    display: flex;
                    align-items: center;
                    gap: 12px;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    font-size: 14px;
                    line-height: 1.4;
                    max-width: 100%;
                    word-wrap: break-word;
                    animation: slideIn 0.3s ease-out;
                    transition: all 0.3s ease;
                }
                
                .notification.removing {
                    animation: slideOut 0.3s ease-in forwards;
                }
                
                @keyframes slideIn {
                    from {
                        opacity: 0;
                        transform: translateX(100%);
                    }
                    to {
                        opacity: 1;
                        transform: translateX(0);
                    }
                }
                
                @keyframes slideOut {
                    from {
                        opacity: 1;
                        transform: translateX(0);
                    }
                    to {
                        opacity: 0;
                        transform: translateX(100%);
                    }
                }
                
                .notification.success {
                    background-color: #f0f9ff;
                    border: 1px solid #0ea5e9;
                    color: #0c4a6e;
                }
                
                .notification.error {
                    background-color: #fef2f2;
                    border: 1px solid #ef4444;
                    color: #7f1d1d;
                }
                
                .notification.warning {
                    background-color: #fffbeb;
                    border: 1px solid #f59e0b;
                    color: #78350f;
                }
                
                .notification.info {
                    background-color: #f8fafc;
                    border: 1px solid #64748b;
                    color: #334155;
                }
                
                .notification.loading {
                    background-color: #f1f5f9;
                    border: 1px solid #3b82f6;
                    color: #1e40af;
                }
                
                .notification-icon {
                    flex-shrink: 0;
                    font-size: 16px;
                }
                
                .notification-content {
                    flex: 1;
                    min-width: 0;
                }
                
                .notification-title {
                    font-weight: 600;
                    margin-bottom: 2px;
                }
                
                .notification-message {
                    opacity: 0.9;
                }
                
                .notification-actions {
                    display: flex;
                    gap: 8px;
                    margin-top: 8px;
                }
                
                .notification-action {
                    padding: 4px 8px;
                    border: none;
                    border-radius: 4px;
                    font-size: 12px;
                    cursor: pointer;
                    transition: background-color 0.2s;
                }
                
                .notification-action.primary {
                    background-color: rgba(59, 130, 246, 0.1);
                    color: #1e40af;
                }
                
                .notification-action.primary:hover {
                    background-color: rgba(59, 130, 246, 0.2);
                }
                
                .notification-close {
                    flex-shrink: 0;
                    background: none;
                    border: none;
                    font-size: 18px;
                    cursor: pointer;
                    opacity: 0.6;
                    transition: opacity 0.2s;
                    padding: 0;
                    width: 20px;
                    height: 20px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
                
                .notification-close:hover {
                    opacity: 1;
                }
                
                .notification-progress {
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    height: 2px;
                    background-color: currentColor;
                    opacity: 0.3;
                    transition: width linear;
                }
                
                .loading-spinner {
                    width: 16px;
                    height: 16px;
                    border: 2px solid currentColor;
                    border-top: 2px solid transparent;
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                }
                
                @keyframes spin {
                    to {
                        transform: rotate(360deg);
                    }
                }
            `;
            document.head.appendChild(style);
        }
    }
    
    /**
     * 显示通知
     */
    show(options) {
        const notification = this.createNotification(options);
        
        // 限制通知数量
        if (this.notifications.length >= this.maxNotifications) {
            this.remove(this.notifications[0].id);
        }
        
        this.notifications.push(notification);
        this.renderNotification(notification);
        this.notifyListeners('show', notification);
        
        // 自动移除（除非是持久通知或加载通知）
        if (notification.duration > 0 && notification.type !== NOTIFICATION_TYPES.LOADING) {
            notification.timeoutId = setTimeout(() => {
                this.remove(notification.id);
            }, notification.duration);
        }
        
        return notification.id;
    }
    
    /**
     * 创建通知对象
     */
    createNotification(options) {
        const {
            type = NOTIFICATION_TYPES.INFO,
            title,
            message,
            duration = this.defaultDuration,
            persistent = false,
            actions = [],
            icon,
            onClose,
            onClick
        } = options;
        
        return {
            id: this.nextId++,
            type,
            title,
            message,
            duration: persistent ? 0 : duration,
            actions,
            icon: icon || this.getDefaultIcon(type),
            onClose,
            onClick,
            timestamp: Date.now(),
            element: null,
            timeoutId: null
        };
    }
    
    /**
     * 获取默认图标
     */
    getDefaultIcon(type) {
        const icons = {
            [NOTIFICATION_TYPES.SUCCESS]: '✅',
            [NOTIFICATION_TYPES.ERROR]: '❌',
            [NOTIFICATION_TYPES.WARNING]: '⚠️',
            [NOTIFICATION_TYPES.INFO]: 'ℹ️',
            [NOTIFICATION_TYPES.LOADING]: null // 使用加载动画
        };
        return icons[type];
    }
    
    /**
     * 渲染通知到DOM
     */
    renderNotification(notification) {
        if (!this.container) return;
        
        const element = document.createElement('div');
        element.className = `notification ${notification.type}`;
        element.setAttribute('data-id', notification.id);
        
        // 构建通知内容
        let content = '';
        
        // 图标
        if (notification.type === NOTIFICATION_TYPES.LOADING) {
            content += '<div class="notification-icon"><div class="loading-spinner"></div></div>';
        } else if (notification.icon) {
            content += `<div class="notification-icon">${notification.icon}</div>`;
        }
        
        // 内容
        content += '<div class="notification-content">';
        if (notification.title) {
            content += `<div class="notification-title">${this.escapeHtml(notification.title)}</div>`;
        }
        if (notification.message) {
            content += `<div class="notification-message">${this.escapeHtml(notification.message)}</div>`;
        }
        
        // 操作按钮
        if (notification.actions.length > 0) {
            content += '<div class="notification-actions">';
            notification.actions.forEach((action, index) => {
                content += `<button class="notification-action ${action.primary ? 'primary' : ''}" data-action="${index}">${this.escapeHtml(action.label)}</button>`;
            });
            content += '</div>';
        }
        
        content += '</div>';
        
        // 关闭按钮
        if (notification.type !== NOTIFICATION_TYPES.LOADING) {
            content += '<button class="notification-close">×</button>';
        }
        
        element.innerHTML = content;
        
        // 添加事件监听器
        this.addNotificationEventListeners(element, notification);
        
        // 添加到容器
        this.container.appendChild(element);
        notification.element = element;
        
        // 添加进度条（如果有持续时间）
        if (notification.duration > 0) {
            this.addProgressBar(element, notification.duration);
        }
    }
    
    /**
     * 添加通知事件监听器
     */
    addNotificationEventListeners(element, notification) {
        // 关闭按钮
        const closeBtn = element.querySelector('.notification-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                this.remove(notification.id);
            });
        }
        
        // 操作按钮
        const actionBtns = element.querySelectorAll('.notification-action');
        actionBtns.forEach((btn, index) => {
            btn.addEventListener('click', () => {
                const action = notification.actions[index];
                if (action.onClick) {
                    action.onClick(notification);
                }
                if (action.closeOnClick !== false) {
                    this.remove(notification.id);
                }
            });
        });
        
        // 点击通知
        if (notification.onClick) {
            element.addEventListener('click', (e) => {
                if (!e.target.closest('.notification-close, .notification-action')) {
                    notification.onClick(notification);
                }
            });
            element.style.cursor = 'pointer';
        }
    }
    
    /**
     * 添加进度条
     */
    addProgressBar(element, duration) {
        const progressBar = document.createElement('div');
        progressBar.className = 'notification-progress';
        progressBar.style.width = '100%';
        element.style.position = 'relative';
        element.appendChild(progressBar);
        
        // 动画进度条
        setTimeout(() => {
            progressBar.style.width = '0%';
            progressBar.style.transition = `width ${duration}ms linear`;
        }, 50);
    }
    
    /**
     * 移除通知
     */
    remove(id) {
        const notification = this.notifications.find(n => n.id === id);
        if (!notification) return;
        
        // 清除定时器
        if (notification.timeoutId) {
            clearTimeout(notification.timeoutId);
        }
        
        // 添加移除动画
        if (notification.element) {
            notification.element.classList.add('removing');
            setTimeout(() => {
                if (notification.element && notification.element.parentNode) {
                    notification.element.parentNode.removeChild(notification.element);
                }
            }, 300);
        }
        
        // 从数组中移除
        const index = this.notifications.findIndex(n => n.id === id);
        if (index > -1) {
            this.notifications.splice(index, 1);
        }
        
        // 调用关闭回调
        if (notification.onClose) {
            notification.onClose(notification);
        }
        
        this.notifyListeners('remove', notification);
    }
    
    /**
     * 清除所有通知
     */
    clear() {
        const notificationIds = this.notifications.map(n => n.id);
        notificationIds.forEach(id => this.remove(id));
    }
    
    /**
     * 更新通知
     */
    update(id, updates) {
        const notification = this.notifications.find(n => n.id === id);
        if (!notification) return false;
        
        // 更新通知属性
        Object.assign(notification, updates);
        
        // 重新渲染
        if (notification.element) {
            const newElement = document.createElement('div');
            this.renderNotification({ ...notification, element: newElement });
            notification.element.parentNode.replaceChild(newElement.firstChild, notification.element);
            notification.element = newElement.firstChild;
        }
        
        this.notifyListeners('update', notification);
        return true;
    }
    
    /**
     * 便捷方法：显示成功通知
     */
    success(message, options = {}) {
        return this.show({
            type: NOTIFICATION_TYPES.SUCCESS,
            message,
            title: options.title || globalLocalization.getMessage('SAVE_SUCCESS'),
            ...options
        });
    }
    
    /**
     * 便捷方法：显示错误通知
     */
    error(message, options = {}) {
        return this.show({
            type: NOTIFICATION_TYPES.ERROR,
            message,
            title: options.title || globalLocalization.getErrorTitle('unknown'),
            duration: options.duration || 8000, // 错误通知显示更长时间
            ...options
        });
    }
    
    /**
     * 便捷方法：显示警告通知
     */
    warning(message, options = {}) {
        return this.show({
            type: NOTIFICATION_TYPES.WARNING,
            message,
            title: options.title || '注意',
            ...options
        });
    }
    
    /**
     * 便捷方法：显示信息通知
     */
    info(message, options = {}) {
        return this.show({
            type: NOTIFICATION_TYPES.INFO,
            message,
            title: options.title || '提示',
            ...options
        });
    }
    
    /**
     * 便捷方法：显示加载通知
     */
    loading(message, options = {}) {
        return this.show({
            type: NOTIFICATION_TYPES.LOADING,
            message,
            title: options.title || globalLocalization.getMessage('LOADING'),
            persistent: true,
            ...options
        });
    }
    
    /**
     * HTML转义
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    /**
     * 添加监听器
     */
    addListener(listener) {
        if (typeof listener === 'function') {
            this.listeners.push(listener);
        }
    }
    
    /**
     * 移除监听器
     */
    removeListener(listener) {
        const index = this.listeners.indexOf(listener);
        if (index > -1) {
            this.listeners.splice(index, 1);
        }
    }
    
    /**
     * 通知监听器
     */
    notifyListeners(event, notification) {
        this.listeners.forEach(listener => {
            try {
                listener(event, notification);
            } catch (error) {
                console.error('通知监听器执行失败:', error);
            }
        });
    }
    
    /**
     * 设置通知位置
     */
    setPosition(position) {
        if (Object.values(NOTIFICATION_POSITIONS).includes(position)) {
            this.position = position;
            if (this.container) {
                this.container.setAttribute('data-position', position);
            }
        }
    }
    
    /**
     * 获取所有通知
     */
    getNotifications() {
        return [...this.notifications];
    }
    
    /**
     * 获取指定类型的通知
     */
    getNotificationsByType(type) {
        return this.notifications.filter(n => n.type === type);
    }
    
    /**
     * 清理资源
     */
    destroy() {
        this.clear();
        this.listeners = [];
        
        if (this.container && this.container.parentNode) {
            this.container.parentNode.removeChild(this.container);
        }
        
        const styles = document.getElementById('notification-styles');
        if (styles && styles.parentNode) {
            styles.parentNode.removeChild(styles);
        }
    }
}

// 创建全局通知系统实例
export const globalNotificationSystem = new NotificationSystem();

// 导出便捷函数
export const notify = {
    success: (message, options) => globalNotificationSystem.success(message, options),
    error: (message, options) => globalNotificationSystem.error(message, options),
    warning: (message, options) => globalNotificationSystem.warning(message, options),
    info: (message, options) => globalNotificationSystem.info(message, options),
    loading: (message, options) => globalNotificationSystem.loading(message, options),
    remove: (id) => globalNotificationSystem.remove(id),
    clear: () => globalNotificationSystem.clear(),
    update: (id, updates) => globalNotificationSystem.update(id, updates)
};