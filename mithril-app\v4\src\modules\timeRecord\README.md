# 时间记录模块

## 模块结构

```
timeRecord/
├── index.js                    # 模块入口文件，统一导出
├── README.md                   # 模块文档
├── components/                 # 组件目录
│   ├── index.js               # 组件导出文件
│   └── TimeRecordPage.js      # 主页面组件
├── models/                     # 模型目录
│   ├── index.js               # 模型导出文件
│   └── TimeRecord.js          # 时间记录数据模型
├── services/                   # 服务目录
│   ├── index.js               # 服务导出文件
│   └── TimeRecordService.js   # API服务类
└── styles/                     # 样式目录
    └── timeRecord.css         # 模块样式文件
```

## 配置文件

- `src/config/timeRecordConfig.js` - 时间记录模块的所有配置常量

## 使用方式

```javascript
// 导入整个模块
import { createTimeRecordModule } from './modules/timeRecord/index.js';

// 创建模块实例
const timeRecordModule = createTimeRecordModule();

// 使用组件
const { component: TimeRecordPage } = timeRecordModule;
```

## 开发状态

当前任务：**1. 创建基础项目结构和配置** ✅

已完成：
- [x] 创建模块目录结构
- [x] 配置vika API相关常量和字段映射
- [x] 设置模块导入导出结构
- [x] 创建基础组件、模型、服务占位文件
- [x] 创建基础样式文件

待实现功能（后续任务）：
- [ ] TimeRecordService API服务层实现
- [ ] TimeRecord数据模型实现
- [ ] TimeRecordPage用户界面组件实现
- [ ] 样式和响应式设计
- [ ] 集成到主应用
- [ ] 错误处理和用户反馈
- [ ] 性能优化和测试

## 字段映射

根据vika API文档，时间记录表字段映射如下：

| 字段名称 | 字段ID | 类型 | 用途 |
|---------|--------|------|------|
| 时间记录分类 | fld9Mole3W9eR | 单行文本 | 记录分类标签 |
| 备注信息 | fld0znfZ7H9xM | 多行文本 | 工作内容描述 |
| 开始时间戳(秒) | fld3e659QaQ11 | 数字 | 开始时间 |
| 结束时间戳(秒) | flddjkaHotlMJ | 数字 | 结束时间 |
| 持续时间文本描述 | fldNY5MabI72y | 单行文本 | 格式化时长 |
| 记录创建时间 | fld1pawhrPs9x | 创建时间 | 系统创建时间 |
| 用户 | fldS1LvP1QVhN | 单向关联 | 关联用户 |