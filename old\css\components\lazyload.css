/**

 * 懒加载样式

 */



/* 懒加载图片基础样式 */

.lazy-image {

    transition: opacity 0.3s ease, filter 0.3s ease;

    background-color: var(--color-gray-100);

    background-image: linear-gradient(45deg, transparent 25%, rgba(255,255,255,.5) 25%, rgba(255,255,255,.5) 75%, transparent 75%, transparent),

                      linear-gradient(45deg, transparent 25%, rgba(255,255,255,.5) 25%, rgba(255,255,255,.5) 75%, transparent 75%, transparent);

    background-size: 20px 20px;

    background-position: 0 0, 10px 10px;

}



/* 即将进入视口状态 */

.lazy-entering {

    animation: lazy-prepare 0.3s ease-out;

}



/* 加载中状态 */

.lazy-loading {

    opacity: 0.7;

    filter: blur(2px);

    animation: lazy-pulse 1.5s ease-in-out infinite;

}



/* 加载完成状态 */

.lazy-loaded {

    opacity: 1;

    filter: none;

    background: none;

}



/* 加载错误状态 */

.lazy-error {

    opacity: 0.5;

    filter: grayscale(100%);

    background-color: var(--color-red-50);

    border: 1px dashed var(--color-red-300);

}



/* 准备加载动画 */

@keyframes lazy-prepare {

    0% {

        transform: scale(1);

        opacity: 1;

    }

    50% {

        transform: scale(1.02);

        opacity: 0.8;

    }

    100% {

        transform: scale(1);

        opacity: 1;

    }

}



/* 脉冲动画 */

@keyframes lazy-pulse {

    0%, 100% {

        opacity: 0.7;

    }

    50% {

        opacity: 0.4;

    }

}



/* 备忘录图片懒加载样式 */

.memo-image.lazy-image {

    width: 100%;

    border-radius: var(--radius-lg);

    cursor: pointer;

    transition: all 0.3s ease;

    min-height: 200px;

    object-fit: cover;

}



.memo-image.lazy-loading {

    background-color: var(--color-gray-100);

    background-image: 

        linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent),

        linear-gradient(45deg, #f0f0f0 25%, transparent 25%),

        linear-gradient(-45deg, #f0f0f0 25%, transparent 25%),

        linear-gradient(45deg, transparent 75%, #f0f0f0 75%),

        linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);

    background-size: 200% 100%, 20px 20px, 20px 20px, 20px 20px, 20px 20px;

    background-position: -200% 0, 0 0, 10px 0, 0 10px, 10px 10px;

    animation: lazy-shimmer 2s infinite, lazy-pattern 4s infinite;

}



.memo-image.lazy-loaded {

    min-height: auto;

}



.memo-image.lazy-loaded:hover {

    transform: scale(1.02);

    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);

}



.memo-image.lazy-error {

    min-height: 150px;

    display: flex;

    align-items: center;

    justify-content: center;

    background-color: var(--color-gray-50);

    border: 2px dashed var(--color-gray-300);

    color: var(--color-gray-500);

    font-size: 0.875rem;

}



.memo-image.lazy-error::after {

    content: '图片加载失败';

    font-family: var(--font-sans);

}



/* 闪烁动画 */

@keyframes lazy-shimmer {

    0% {

        background-position: -200% 0, 0 0, 10px 0, 0 10px, 10px 10px;

    }

    100% {

        background-position: 200% 0, 0 0, 10px 0, 0 10px, 10px 10px;

    }

}



/* 图案动画 */

@keyframes lazy-pattern {

    0%, 100% {

        background-size: 200% 100%, 20px 20px, 20px 20px, 20px 20px, 20px 20px;

    }

    50% {

        background-size: 200% 100%, 30px 30px, 30px 30px, 30px 30px, 30px 30px;

    }

}



/* 响应式处理 */

@media (max-width: 768px) {

    .memo-image.lazy-loading {

        min-height: 150px;

    }

    

    .memo-image.lazy-error {

        min-height: 120px;

        font-size: 0.75rem;

    }

}



/* 深色主题适配 */

.dark .lazy-image {

    background-color: var(--color-gray-800);

}



.dark .lazy-loading {

    background-image: 

        linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent),

        linear-gradient(45deg, #374151 25%, transparent 25%),

        linear-gradient(-45deg, #374151 25%, transparent 25%),

        linear-gradient(45deg, transparent 75%, #374151 75%),

        linear-gradient(-45deg, transparent 75%, #374151 75%);

}



.dark .lazy-error {

    background-color: var(--color-gray-800);

    border-color: var(--color-gray-600);

    color: var(--color-gray-400);

}



/* 护眼模式适配 */

.eye-protect .lazy-image {

    background-color: var(--color-eye-protect-card);

}



.eye-protect .lazy-loading {

    background-image: 

        linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent),

        linear-gradient(45deg, #f0f4f0 25%, transparent 25%),

        linear-gradient(-45deg, #f0f4f0 25%, transparent 25%),

        linear-gradient(45deg, transparent 75%, #f0f4f0 75%),

        linear-gradient(-45deg, transparent 75%, #f0f4f0 75%);

}



.eye-protect .lazy-error {

    background-color: var(--color-eye-protect-card);

    border-color: var(--color-eye-protect-border);

    color: var(--color-eye-protect-text);

}





/* 图片网格布局优化 */

.memo-media {

    display: grid;

    gap: 0.5rem;

    margin: 1rem 0;

}



.memo-media .lazy-image:only-child {

    grid-column: 1 / -1;

    max-height: 400px;

}



.memo-media .lazy-image:nth-child(1):nth-last-child(2),

.memo-media .lazy-image:nth-child(2):nth-last-child(1) {

    grid-template-columns: 1fr 1fr;

}



.memo-media .lazy-image:nth-child(1):nth-last-child(3) ~ .lazy-image,

.memo-media .lazy-image:nth-child(2):nth-last-child(2) ~ .lazy-image,

.memo-media .lazy-image:nth-child(3):nth-last-child(1) {

    grid-template-columns: repeat(2, 1fr);

}



/* 预加载提示 */

.lazy-preload-hint {

    position: absolute;

    top: 50%;

    left: 50%;

    transform: translate(-50%, -50%);

    background: rgba(0, 0, 0, 0.7);

    color: white;

    padding: 0.5rem 1rem;

    border-radius: var(--radius-md);

    font-size: 0.875rem;

    pointer-events: none;

    opacity: 0;

    transition: opacity 0.3s ease;

}



.lazy-loading .lazy-preload-hint {

    opacity: 1;

}