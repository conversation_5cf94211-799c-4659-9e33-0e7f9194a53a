// 使用相对路径导入服务
import { ApiService } from './ApiService.js';
import { API_CONFIG } from '../config.js';

/**
 * Memos服务类，处理备忘录相关操作
 */
export class MemosService {
    constructor() {
        this.apiService = new ApiService();
        this.cache = new Map(); // 简单的内存缓存
        this.cacheTimeout = 2 * 60 * 1000; // 减少到2分钟缓存时间
        this.userCache = new Map(); // 用户信息缓存
        this.userCacheTimeout = 10 * 60 * 1000; // 用户信息缓存10分钟
    }
    
    /**
     * 获取Memos列表（登录用户可以看到所有内容）
     */
    async getMemos(page = 1, limit = 10) {
        try {
            const cacheKey = `memos_${page}_${limit}`;
            
            // 检查缓存
            if (this.cache.has(cacheKey)) {
                const cached = this.cache.get(cacheKey);
                if (Date.now() - cached.timestamp < this.cacheTimeout) {
                    return { success: true, data: cached.data };
                } else {
                    // 缓存过期，删除缓存
                    this.cache.delete(cacheKey);
                }
            }
            
            // 使用pageNum而不是offset进行分页
            const params = {
                pageSize: limit,
                pageNum: page
            };
            
            const response = await this.apiService.getMemos(params);
            
            // 检查响应是否成功
            if (response && response.success) {
                // 缓存结果
                this.cache.set(cacheKey, {
                    data: response.data,
                    timestamp: Date.now()
                });
                
                return { success: true, data: response.data };
            } else {
                // 如果响应中包含错误信息，则返回该错误信息
                return { success: false, error: response.message || '获取数据失败' };
            }
        } catch (error) {
            console.error('获取Memos失败:', error);
            // 提供更友好的错误信息
            let errorMessage = '获取数据失败，请稍后重试';
            if (error.message.includes('网络请求失败')) {
                errorMessage = '无法连接到服务器，请检查网络连接或稍后重试';
            }
            return { success: false, error: errorMessage };
        }
    }
    
    /**
     * 获取公开Memos列表（未登录用户只能看到公开内容）
     */
    async getPublicMemos(page = 1, limit = 10) {
        try {
            const cacheKey = `public_memos_${page}_${limit}`;
            
            // 检查缓存
            if (this.cache.has(cacheKey)) {
                const cached = this.cache.get(cacheKey);
                if (Date.now() - cached.timestamp < this.cacheTimeout) {
                    return { success: true, data: cached.data };
                } else {
                    // 缓存过期，删除缓存
                    this.cache.delete(cacheKey);
                }
            }
            
            // 使用pageNum而不是offset进行分页
            const params = {
                pageSize: limit,
                pageNum: page
            };
            
            const response = await this.apiService.getPublicMemos(params);
            
            // 检查响应是否成功
            if (response && response.success) {
                // 缓存结果
                this.cache.set(cacheKey, {
                    data: response.data,
                    timestamp: Date.now()
                });
                
                return { success: true, data: response.data };
            } else {
                // 如果响应中包含错误信息，则返回该错误信息
                return { success: false, error: response.message || '获取数据失败' };
            }
        } catch (error) {
            console.error('获取公开Memos失败:', error);
            // 提供更友好的错误信息
            let errorMessage = '获取数据失败，请稍后重试';
            if (error.message.includes('网络请求失败')) {
                errorMessage = '无法连接到服务器，请检查网络连接或稍后重试';
            }
            return { success: false, error: errorMessage };
        }
    }
    
    /**
     * 创建新的Memo
     */
    async createMemo(content, uploadedFiles = [], status = '公开') {
        try {
            const memoData = {
                content: content,
                files: uploadedFiles, // 这里是已经上传完成的文件对象数组
                userId: window.auth?.currentUser?.id || null,
                status: status, // 使用传入的状态
                createdAt: new Date().toISOString()
            };
            
            const response = await this.apiService.createMemo(memoData);
            
            // 检查响应是否成功
            if (response && response.success) {
                // 清除相关缓存
                this.clearCache();
                
                return { success: true, data: response.data.records[0] };
            } else {
                return { success: false, error: response.message || '发布失败' };
            }
        } catch (error) {
            console.error('创建Memo失败:', error);
            // 提供更友好的错误信息
            let errorMessage = '发布失败，请稍后重试';
            if (error.message.includes('网络请求失败')) {
                errorMessage = '无法连接到服务器，请检查网络连接或稍后重试';
            }
            return { success: false, error: errorMessage };
        }
    }
    
    /**
     * 更新Memo
     */
    async updateMemo(recordId, content, images = []) {
        try {
            const memoData = {
                content: content,
                images: images
            };
            
            const response = await this.apiService.updateMemo(recordId, memoData);
            
            // 检查响应是否成功
            if (response && response.success) {
                // 清除相关缓存
                this.clearCache();
                
                return { success: true, data: response.data.records[0] };
            } else {
                return { success: false, error: response.message || '更新失败' };
            }
        } catch (error) {
            console.error('更新Memo失败:', error);
            // 提供更友好的错误信息
            let errorMessage = '更新失败，请稍后重试';
            if (error.message.includes('网络请求失败')) {
                errorMessage = '无法连接到服务器，请检查网络连接或稍后重试';
            }
            return { success: false, error: errorMessage };
        }
    }
    
    /**
     * 删除Memo
     */
    async deleteMemo(recordId) {
        try {
            const response = await this.apiService.deleteMemo(recordId);
            
            // 检查响应是否成功
            if (response && response.success) {
                // 清除相关缓存
                this.clearCache();
                
                return { success: true };
            } else {
                return { success: false, error: response.message || '删除失败' };
            }
        } catch (error) {
            console.error('删除Memo失败:', error);
            // 提供更友好的错误信息
            let errorMessage = '删除失败，请稍后重试';
            if (error.message.includes('网络请求失败')) {
                errorMessage = '无法连接到服务器，请检查网络连接或稍后重试';
            }
            return { success: false, error: errorMessage };
        }
    }
    
    /**
     * 清除缓存
     */
    clearCache() {
        this.cache.clear();
    }
    
    /**
     * 根据关键字搜索Memos
     */
    async searchMemos(keyword, page = 1, limit = 10) {
        try {
            // 使用pageNum而不是offset进行分页
            const params = {
                pageSize: limit,
                pageNum: page,
                filterByFormula: `SEARCH("${keyword}", {content})`
            };
            
            const response = await this.apiService.getMemos(params);
            
            // 检查响应是否成功
            if (response && response.success) {
                return { success: true, data: response.data };
            } else {
                return { success: false, error: response.message || '搜索失败' };
            }
        } catch (error) {
            console.error('搜索Memos失败:', error);
            // 提供更友好的错误信息
            let errorMessage = '搜索失败，请稍后重试';
            if (error.message.includes('网络请求失败')) {
                errorMessage = '无法连接到服务器，请检查网络连接或稍后重试';
            }
            return { success: false, error: errorMessage };
        }
    }
    
    /**
     * 批量获取用户信息（带缓存优化）
     */
    async getUsers(userIds) {
        try {
            if (!userIds || userIds.length === 0) {
                return { success: true, data: [] };
            }
            
            const now = Date.now();
            const cachedUsers = [];
            const uncachedUserIds = [];
            
            // 检查缓存，分离已缓存和未缓存的用户ID
            userIds.forEach(userId => {
                if (this.userCache.has(userId)) {
                    const cached = this.userCache.get(userId);
                    if (now - cached.timestamp < this.userCacheTimeout) {
                        cachedUsers.push(cached.data);
                    } else {
                        this.userCache.delete(userId);
                        uncachedUserIds.push(userId);
                    }
                } else {
                    uncachedUserIds.push(userId);
                }
            });
            
            // 如果所有用户都已缓存，直接返回
            if (uncachedUserIds.length === 0) {
                return { success: true, data: cachedUsers };
            }
            
            // 只请求未缓存的用户信息
            const filterFormula = `OR(${uncachedUserIds.map(id => `{recordId()}="${id}"`).join(',')})`;
            
            const params = {
                viewId: API_CONFIG.userViewId,
                fieldKey: API_CONFIG.fieldKey,
                filterByFormula: filterFormula,
                pageSize: uncachedUserIds.length
            };
            
            const response = await this.apiService.get(`/datasheets/${API_CONFIG.userDatasheetId}/records`, params);
            
            if (response && response.success) {
                const newUsers = response.data.records;
                
                // 缓存新获取的用户信息
                newUsers.forEach(user => {
                    this.userCache.set(user.recordId, {
                        data: user,
                        timestamp: now
                    });
                });
                
                // 合并缓存的用户和新获取的用户
                const allUsers = [...cachedUsers, ...newUsers];
                
                return { success: true, data: allUsers };
            } else {
                return { success: false, error: response.message || '获取用户信息失败' };
            }
        } catch (error) {
            console.error('获取用户信息失败:', error);
            return { success: false, error: '获取用户信息失败，请稍后重试' };
        }
    }
}