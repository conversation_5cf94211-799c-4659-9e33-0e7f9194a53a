/* 视频压缩相关样式 */

.compression-progress {
    margin: 10px 0;
    padding: 8px;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 4px solid #007bff;
}

.compression-progress .progress-bar {
    width: 100%;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 5px;
}

.compression-progress .progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #007bff, #0056b3);
    transition: width 0.3s ease;
    border-radius: 4px;
}

.compression-progress .progress-text {
    font-size: 12px;
    color: #6c757d;
    text-align: center;
}

.compression-option {
    display: flex;
    align-items: center;
    margin: 0 10px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #495057;
    cursor: pointer;
    user-select: none;
}

.checkbox-label input[type="checkbox"] {
    margin-right: 6px;
    transform: scale(1.1);
}

.checkbox-label:hover {
    color: #007bff;
}

/* 文件信息样式优化 */
.file-info {
    margin-top: 8px;
    padding: 6px;
    background: rgba(0, 0, 0, 0.05);
    border-radius: 4px;
}

.file-name {
    font-size: 12px;
    font-weight: 500;
    color: #495057;
    margin-bottom: 2px;
    word-break: break-all;
}

.file-size {
    font-size: 11px;
    color: #6c757d;
}

.file-type {
    font-size: 11px;
    color: #28a745;
    margin-top: 2px;
}

/* 预览项样式 */
.preview-item {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    transition: border-color 0.2s ease;
}

.preview-item:hover {
    border-color: #007bff;
}

.preview-video {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 6px;
}

.preview-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 6px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .compression-option {
        margin: 5px 0;
        width: 100%;
    }
    
    .checkbox-label {
        font-size: 13px;
    }
    
    .preview-video,
    .preview-image {
        height: 150px;
    }
}