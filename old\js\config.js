/**
 * 配置文件模块
 * 包含API配置和常量定义
 */

export const API_CONFIG = {
    datasheetId: 'dstCo3t04QBY3RweuR',
    userDatasheetId: 'dst13vlcxlMi1EanCl',
    token: 'uskcZUvxWXvLIPXN0hUC6DK',
    baseURL: 'https://api.vika.cn/fusion/v1',
    viewId: 'viwYoodJk54Xt',
    userViewId: 'viwHzmgPteBwK',
    fieldKey: 'name'
};

export const APP_CONFIG = {
    recordsPerPage: 10,
    maxImagesPerMemo: 4,
    debounceDelay: 200,
    maxFileSize: 10 * 1024 * 1024, // 10MB
    supportedImageTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
    supportedVideoTypes: ['video/mp4', 'video/webm', 'video/ogg'],
    autoSaveDelay: 3000,
    notificationDuration: 3000
};

export const STORAGE_KEYS = {
    THEME: 'theme',
    TOKEN: 'authToken',
    USER: 'currentUser',
    MEMOS: 'memos',
    SETTINGS: 'app_settings'
};