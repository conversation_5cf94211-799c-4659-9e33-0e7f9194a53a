# 视频压缩功能使用指南

## 功能概述

实现了类似微信的视频压缩功能，可以显著减少视频文件大小，提高上传速度和节省存储空间。

## 主要特性

### 1. 智能压缩
- **自动检测**: 文件大小超过5MB时自动启用压缩
- **质量平衡**: 在文件大小和视频质量之间找到最佳平衡点
- **格式优化**: 输出WebM格式，压缩率更高

### 2. 压缩选项
- **快速压缩**: 480p分辨率，适合快速分享
- **高质量压缩**: 1080p分辨率，保持较高画质
- **自定义参数**: 可调整分辨率、质量、码率等

### 3. 用户体验
- **实时进度**: 显示压缩和上传进度
- **压缩统计**: 显示压缩前后大小对比
- **可选开关**: 用户可选择是否启用压缩

## 技术实现

### 方案1: MediaRecorder API (推荐)
```javascript
// 优点:
- 浏览器原生支持，无需额外依赖
- 体积小，加载快
- 支持实时压缩

// 缺点:
- 输出格式限制(主要是WebM)
- 压缩选项相对有限
```

### 方案2: FFmpeg.wasm (高级)
```javascript
// 优点:
- 功能强大，支持所有视频格式
- 压缩参数可精确控制
- 输出质量更好

// 缺点:
- 文件体积大(~25MB)
- 加载时间长
- 内存占用高
```

## 使用方法

### 1. 基本使用
1. 选择视频文件
2. 勾选"压缩视频"选项(默认开启)
3. 点击发布，系统自动压缩后上传

### 2. 压缩参数说明
```javascript
// 快速压缩模式
{
    maxWidth: 480,      // 最大宽度
    maxHeight: 854,     // 最大高度  
    quality: 0.6,       // 压缩质量
    maxSizeMB: 5        // 目标大小
}

// 高质量模式
{
    maxWidth: 1080,     // 最大宽度
    maxHeight: 1920,    // 最大高度
    quality: 0.85,      // 压缩质量
    maxSizeMB: 20       // 目标大小
}
```

## 压缩效果

### 典型压缩率
- **1080p视频**: 通常可压缩60-80%
- **4K视频**: 通常可压缩70-90%
- **短视频**: 压缩率相对较低(30-50%)

### 质量对比
| 原始大小 | 压缩后 | 压缩率 | 质量损失 |
|---------|--------|--------|----------|
| 50MB    | 12MB   | 76%    | 轻微     |
| 100MB   | 18MB   | 82%    | 轻微     |
| 200MB   | 25MB   | 87.5%  | 中等     |

## 浏览器兼容性

### MediaRecorder API
- ✅ Chrome 47+
- ✅ Firefox 25+
- ✅ Safari 14.1+
- ✅ Edge 79+

### FFmpeg.wasm
- ✅ Chrome 57+
- ✅ Firefox 52+
- ✅ Safari 11+
- ✅ Edge 79+

## 性能优化建议

### 1. 内存管理
```javascript
// 及时清理资源
URL.revokeObjectURL(videoUrl);
stream.getTracks().forEach(track => track.stop());
```

### 2. 用户体验
```javascript
// 显示压缩进度
this.compressionProgress = progress;
m.redraw();

// 压缩结果提示
window.appState.showNotification(
    `压缩完成: ${originalSize} → ${compressedSize}`,
    'success'
);
```

### 3. 错误处理
```javascript
try {
    const compressed = await videoCompressor.compress(file);
    return compressed;
} catch (error) {
    console.error('压缩失败:', error);
    return originalFile; // 降级处理
}
```

## 故障排除

### 常见问题

1. **压缩失败**
   - 检查浏览器兼容性
   - 确认文件格式支持
   - 查看控制台错误信息

2. **压缩效果不佳**
   - 调整质量参数
   - 尝试不同的分辨率设置
   - 考虑使用FFmpeg方案

3. **性能问题**
   - 限制同时压缩的文件数量
   - 对大文件进行分片处理
   - 使用Web Worker避免阻塞UI

### 调试技巧
```javascript
// 启用详细日志
console.log('压缩参数:', options);
console.log('原始文件:', file.size);
console.log('压缩结果:', compressedFile.size);
```

## 未来改进

1. **支持更多格式**: 添加MP4、AVI等格式支持
2. **批量压缩**: 支持多文件并行压缩
3. **预设模板**: 提供微信、抖音等平台的压缩预设
4. **云端压缩**: 集成服务端压缩服务
5. **AI优化**: 使用机器学习优化压缩参数

## 相关资源

- [MediaRecorder API文档](https://developer.mozilla.org/en-US/docs/Web/API/MediaRecorder)
- [FFmpeg.wasm项目](https://github.com/ffmpegwasm/ffmpeg.wasm)
- [视频编码最佳实践](https://web.dev/efficient-animated-content/)