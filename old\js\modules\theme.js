/**
 * 主题管理模块 - 现代化重构版本
 * 处理应用的主题切换、样式管理和响应式适配
 */

import { STORAGE_KEYS } from '../config.js';
import { showNotification } from './utils.js';

class ThemeManager {
    constructor() {
        this.themes = {
            light: {
                name: '浅色模式',
                icon: '☀️',
                class: 'light',
                colors: {
                    primary: '#3B82F6',
                    secondary: '#8B5CF6',
                    accent: '#10B981',
                    background: '#FFFFFF',
                    surface: '#F8FAFC',
                    text: '#1E293B',
                    textSecondary: '#64748B',
                    border: '#E2E8F0',
                    shadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
                }
            },
            dark: {
                name: '深色模式',
                icon: '🌙',
                class: 'dark',
                colors: {
                    primary: '#60A5FA',
                    secondary: '#A78BFA',
                    accent: '#34D399',
                    background: '#0F172A',
                    surface: '#1E293B',
                    text: '#F1F5F9',
                    textSecondary: '#94A3B8',
                    border: '#334155',
                    shadow: '0 1px 3px rgba(0, 0, 0, 0.3)'
                }
            },
            eyeProtect: {
                name: '护眼模式',
                icon: '👁️',
                class: 'eye-protect',
                colors: {
                    primary: '#38A169',
                    secondary: '#68D391',
                    accent: '#48BB78',
                    background: '#F5F7E7',
                    surface: '#EDEFD9',
                    text: '#2D3748',
                    textSecondary: '#4A5568',
                    border: '#CBD5E0',
                    shadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
                }
            }
        };
        
        this.currentTheme = this.getStoredTheme() || 'light';
        this.isSystemDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        this.listeners = new Set();
        this.transitionDuration = 250;
        
        this.init();
    }

    init() {
        this.setupSystemThemeListener();
        this.applyTheme(this.currentTheme);
        this.bindEvents();
    }

    /**
     * 设置系统主题监听器
     */
    setupSystemThemeListener() {
        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
        mediaQuery.addEventListener('change', (e) => {
            this.isSystemDark = e.matches;
            if (this.currentTheme === 'auto') {
                this.applySystemTheme();
            }
        });
    }

    /**
     * 绑定主题切换事件
     */
    bindEvents() {
        // 监听主题切换按钮点击
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-theme]')) {
                const theme = e.target.dataset.theme;
                this.setTheme(theme);
            }
        });

        // 监听键盘快捷键 (Ctrl/Cmd + Shift + T)
        document.addEventListener('keydown', (e) => {
            if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'T') {
                e.preventDefault();
                this.toggleTheme();
            }
        });
    }

    /**
     * 获取存储的主题
     */
    getStoredTheme() {
        try {
            return localStorage.getItem('theme');
        } catch (error) {
            console.error('读取主题设置失败:', error);
            return null;
        }
    }

    /**
     * 保存主题设置
     */
    saveTheme(theme) {
        try {
            localStorage.setItem('theme', theme);
        } catch (error) {
            console.error('保存主题设置失败:', error);
        }
    }

    /**
     * 设置主题
     */
    setTheme(theme) {
        if (!this.themes[theme]) {
            console.warn('不存在的主题:', theme);
            return;
        }

        const oldTheme = this.currentTheme;
        this.currentTheme = theme;
        this.saveTheme(theme);
        
        // 添加过渡效果
        this.addThemeTransition();
        
        this.applyTheme(theme);
        this.notifyThemeChange(theme, oldTheme);
        
        // 显示切换通知
        const themeInfo = this.themes[theme];
        showNotification(`已切换到${themeInfo.name} ${themeInfo.icon}`, 'success', 2000);
    }

    /**
     * 应用主题
     */
    applyTheme(themeName) {
        const theme = this.themes[themeName];
        if (!theme || !theme.colors) return;

        const root = document.documentElement;
        const colors = theme.colors;

        // 设置CSS变量
        Object.entries(colors).forEach(([key, value]) => {
            root.style.setProperty(`--color-${key}`, value);
        });

        // 设置主题属性
        document.documentElement.setAttribute('data-theme', themeName);
        
        // 更新主题切换器状态
        this.updateThemeButtons(themeName);
        
        // 触发主题应用完成事件
        document.dispatchEvent(new CustomEvent('theme:applied', {
            detail: { theme: themeName, colors }
        }));
    }

    /**
     * 应用系统主题
     */
    applySystemTheme() {
        const systemTheme = this.isSystemDark ? 'dark' : 'light';
        this.applyTheme(systemTheme);
        
        // 更新主题切换器状态，但保持auto选项激活
        this.updateThemeButtons('auto');
    }

    /**
     * 切换主题
     */
    toggleTheme() {
        const themes = Object.keys(this.themes);
        const currentIndex = themes.indexOf(this.currentTheme);
        const nextIndex = (currentIndex + 1) % themes.length;
        this.setTheme(themes[nextIndex]);
    }

    /**
     * 更新主题按钮状态
     */
    updateThemeButtons(activeTheme) {
        const themeButtons = document.querySelectorAll('[data-theme]');
        themeButtons.forEach(button => {
            const isActive = button.dataset.theme === activeTheme;
            button.classList.toggle('active', isActive);
            button.setAttribute('aria-pressed', isActive);
        });

        // 更新主题指示器
        const themeIndicator = document.querySelector('.theme-indicator');
        if (themeIndicator) {
            const themeName = this.themes[activeTheme]?.name || activeTheme;
            themeIndicator.textContent = themeName;
        }
    }

    /**
     * 获取当前主题信息
     */
    getCurrentTheme() {
        return {
            name: this.currentTheme,
            displayName: this.themes[this.currentTheme]?.name,
            isDark: this.isDark()
        };
    }

    /**
     * 判断是否为暗色主题
     */
    isDark() {
        if (this.currentTheme === 'auto') {
            return this.isSystemDark;
        }
        return this.currentTheme === 'dark';
    }

    /**
     * 获取主题颜色
     */
    getThemeColors() {
        const theme = this.currentTheme === 'auto' 
            ? (this.isSystemDark ? this.themes.dark : this.themes.light)
            : this.themes[this.currentTheme];
        
        return theme?.colors || {};
    }

    /**
     * 添加主题变化监听器
     */
    addListener(callback) {
        this.listeners.add(callback);
    }

    /**
     * 移除主题变化监听器
     */
    removeListener(callback) {
        this.listeners.delete(callback);
    }

    /**
     * 添加主题过渡效果
     */
    addThemeTransition() {
        const root = document.documentElement;
        root.style.setProperty('--theme-transition', `all ${this.transitionDuration}ms ease-in-out`);
        
        // 为body添加过渡类
        document.body.classList.add('theme-transitioning');
        
        // 过渡完成后移除类
        setTimeout(() => {
            document.body.classList.remove('theme-transitioning');
        }, this.transitionDuration);
    }

    /**
     * 通知主题变化
     */
    notifyThemeChange(theme, oldTheme) {
        this.listeners.forEach(callback => {
            try {
                callback(theme, oldTheme);
            } catch (error) {
                console.error('主题监听器错误:', error);
            }
        });

        // 触发自定义事件
        document.dispatchEvent(new CustomEvent('theme:changed', {
            detail: { 
                theme, 
                oldTheme,
                themeInfo: this.themes[theme],
                ...this.getCurrentTheme() 
            }
        }));
    }

    /**
     * 获取主题预览
     */
    getThemePreview(themeName) {
        const theme = this.themes[themeName];
        if (!theme) return null;

        return {
            name: theme.name,
            icon: theme.icon,
            colors: theme.colors,
            preview: `
                <div style="
                    background: ${theme.colors.background};
                    color: ${theme.colors.text};
                    border: 1px solid ${theme.colors.border};
                    border-radius: 8px;
                    padding: 12px;
                    font-size: 12px;
                ">
                    <div style="color: ${theme.colors.primary}; font-weight: bold; margin-bottom: 4px;">
                        ${theme.icon} ${theme.name}
                    </div>
                    <div style="color: ${theme.colors.textSecondary};">
                        预览文本内容
                    </div>
                </div>
            `
        };
    }

    /**
     * 批量应用主题到元素
     */
    applyThemeToElements(elements, theme = this.currentTheme) {
        const themeColors = this.themes[theme]?.colors;
        if (!themeColors) return;

        elements.forEach(element => {
            if (element.dataset.themeProperty) {
                const property = element.dataset.themeProperty;
                const colorKey = element.dataset.themeColor;
                if (themeColors[colorKey]) {
                    element.style.setProperty(property, themeColors[colorKey]);
                }
            }
        });
    }

    /**
     * 重置主题到默认状态
     */
    resetTheme() {
        this.setTheme('light');
        showNotification('主题已重置为默认浅色模式', 'info', 2000);
    }

    /**
     * 获取主题统计信息
     */
    getThemeStats() {
        return {
            totalThemes: Object.keys(this.themes).length,
            currentTheme: this.currentTheme,
            isDark: this.isDark(),
            hasSystemSupport: window.matchMedia('(prefers-color-scheme: dark)').matches !== undefined,
            transitionDuration: this.transitionDuration,
            listenerCount: this.listeners.size
        };
    }

    /**
     * 初始化主题切换器UI
     */
    initThemeSwitcher() {
        const container = document.querySelector('.theme-switcher');
        if (!container) return;

        container.innerHTML = `
            <div class="theme-options">
                ${Object.entries(this.themes).map(([key, theme]) => `
                    <button 
                        class="theme-btn ${key === this.currentTheme ? 'active' : ''}" 
                        data-theme="${key}"
                        aria-pressed="${key === this.currentTheme}"
                        title="切换到${theme.name}"
                    >
                        <span class="theme-icon theme-${key}"></span>
                        <span class="theme-label">${theme.name}</span>
                    </button>
                `).join('')}
            </div>
            <div class="theme-indicator">
                ${this.themes[this.currentTheme]?.name || this.currentTheme}
            </div>
        `;
    }

    /**
     * 获取主题样式表
     */
    getThemeStyles() {
        return `
            :root {
                --color-primary: #3b82f6;
                --color-background: #ffffff;
                --color-surface: #f8fafc;
                --color-text: #1e293b;
                --color-text-secondary: #64748b;
                --color-border: #e2e8f0;
                --color-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            }

            [data-theme="dark"] {
                --color-primary: #60a5fa;
                --color-background: #0f172a;
                --color-surface: #1e293b;
                --color-text: #f1f5f9;
                --color-text-secondary: #94a3b8;
                --color-border: #334155;
                --color-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
            }

            .theme-switcher {
                display: flex;
                gap: 0.5rem;
                align-items: center;
            }

            .theme-btn {
                padding: 0.5rem;
                border: 1px solid var(--color-border);
                background: var(--color-surface);
                color: var(--color-text);
                border-radius: 0.375rem;
                cursor: pointer;
                transition: all 0.2s;
            }

            .theme-btn:hover {
                background: var(--color-primary);
                color: white;
            }

            .theme-btn.active {
                background: var(--color-primary);
                color: white;
                border-color: var(--color-primary);
            }

            .theme-icon {
                display: inline-block;
                width: 1rem;
                height: 1rem;
                margin-right: 0.25rem;
            }

            .theme-light::before { content: '☀'; }
            .theme-dark::before { content: '🌙'; }
            .theme-auto::before { content: '🔄'; }
        `;
    }
}

// 创建单例实例
const themeManager = new ThemeManager();

// 导出模块
export { ThemeManager, themeManager };
export default themeManager;